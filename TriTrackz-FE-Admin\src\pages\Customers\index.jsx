import React, { useMemo, useState, memo, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import DataTable from "react-data-table-component";
import { Avatar } from "@components/Common";
import {
  Nav,
  NavItem,
  NavLink,
  Button,
  Dropdown,
  DropdownToggle,
  DropdownMenu,
  DropdownItem,
  Input,
  Label,
  FormGroup,
  Row,
  Col,
  Card,
  CardBody,
  Collapse,
} from "reactstrap";
import {
  FaUser,
  FaPhone,
  FaMapMarkerAlt,
  FaSpinner,
  FaSearch,
  FaFilter,
  FaDownload,
  FaTruck,
  FaHandshake,
  FaShippingFast,
  FaIndustry,
  FaUserCircle,
  FaChevronDown,
  FaChevronUp,
  FaSync,
  FaEye,
  FaTimes,
  FaCheckCircle,
  FaClock,
  FaBuilding,
} from "react-icons/fa";
import {
  useGetUsersAdvanced,
  USER_TYPE_LABELS,
  USER_TYPE_CONFIG,
  USER_STATUS_LABELS,
  KYC_STATUS_LABELS,
  SUBSCRIPTION_STATUS_LABELS,
} from "@api/usersHooks";

// Static constants moved outside component to prevent recreation
const PAGE_SIZE_OPTIONS = [10, 20, 50, 100];
const DEFAULT_FILTERS = {
  status: "",
  kycStatus: "",
  subscriptionStatus: "",
  dateFrom: "",
  dateTo: "",
  city: "",
  state: "",
};

// Static user type tabs configuration
const USER_TYPE_TABS = [
  { key: "2", label: "Transport Company", icon: "FaTruck" },
  { key: "3", label: "Broker", icon: "FaHandshake" },
  { key: "4", label: "Carrier", icon: "FaShippingFast" },
  { key: "7", label: "Shipper Company", icon: "FaIndustry" },
  { key: "8", label: "Shipper Individual", icon: "FaUserCircle" },
];

// Using Bootstrap classes for table styling instead of custom styles
const TABLE_CUSTOM_STYLES = {
  table: {
    style: {
      backgroundColor: "transparent",
    },
  },
  headRow: {
    style: {
      backgroundColor: "var(--bs-secondary-bg)",
      borderBottom: "1px solid var(--bs-border-color)",
    },
  },
  headCells: {
    style: {
      color: "var(--bs-body-color)",
      fontWeight: "600",
      fontSize: "14px",
    },
  },
  rows: {
    style: {
      backgroundColor: "transparent",
      borderBottom: "1px solid var(--bs-border-color)",
      "&:hover": {
        backgroundColor: "var(--bs-tertiary-bg)",
      },
    },
  },
  cells: {
    style: {
      color: "var(--bs-body-color)",
      fontSize: "14px",
    },
  },
  pagination: {
    style: {
      backgroundColor: "var(--bs-secondary-bg)",
      borderTop: "1px solid var(--bs-border-color)",
      color: "var(--bs-body-color)",
    },
    pageButtonsStyle: {
      color: "var(--bs-body-color)",
      backgroundColor: "transparent",
      border: "none",
      "&:hover": {
        backgroundColor: "var(--bs-tertiary-bg)",
      },
      "& svg": {
        fill: "var(--bs-body-color)",
      },
    },
  },
};

// Memoized UserCell component to prevent unnecessary re-renders
const UserCell = memo(({ row }) => {
  const displayName = useMemo(() => {
    return row.displayName || `${row.firstName} ${row.lastName}`;
  }, [row.displayName, row.firstName, row.lastName]);

  return (
    <div className="d-flex align-items-center py-2">
      <div className="flex-shrink-0">
        <Avatar
          name={displayName}
          size="small"
          className="rounded-circle"
          width="40"
          height="40"
        />
      </div>
      <div className="flex-grow-1 ms-3">
        <div className="fw-semibold">{displayName}</div>
        <div className="text-muted small">{row.email}</div>
      </div>
    </div>
  );
});

UserCell.displayName = "UserCell";

// Memoized Loading Component
const LoadingComponent = memo(() => (
  <div className="d-flex justify-content-center align-items-center py-5">
    <FaSpinner className="fa-spin text-primary me-2" />
    <span>Loading users...</span>
  </div>
));

LoadingComponent.displayName = "LoadingComponent";

// Memoized No Data Component
const NoDataComponent = memo(() => (
  <div className="text-center py-5 ">
    <FaUser size={48} className="text-muted mb-3" />
    <h6 className="text-muted">No users found</h6>
    <p className="text-muted small">Try adjusting your search criteria</p>
  </div>
));

NoDataComponent.displayName = "NoDataComponent";

// Memoized StatusBadge Component
const StatusBadge = memo(({ status, type }) => {
  const badgeClass = useMemo(() => {
    let baseClass = "badge ";
    let label = "";

    if (type === "status") {
      label = USER_STATUS_LABELS[status] || "Unknown";
      baseClass += status === "Active" ? "bg-success" : "bg-warning";
    } else if (type === "kyc") {
      label = KYC_STATUS_LABELS[status] || "Unknown";
      baseClass +=
        status === "Approved"
          ? "bg-success"
          : status === "Rejected"
          ? "bg-danger"
          : status === "Submitted"
          ? "bg-warning"
          : "bg-secondary";
    } else if (type === "subscription") {
      label = SUBSCRIPTION_STATUS_LABELS[status] || "Unknown";
      baseClass += status === "Active" ? "bg-success" : "bg-secondary";
    }

    return { badgeClass: baseClass, label };
  }, [status, type]);

  return <span className={badgeClass.badgeClass}>{badgeClass.label}</span>;
});

StatusBadge.displayName = "StatusBadge";

const Customers = () => {
  const navigate = useNavigate();

  // State for pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState("CreatedAt");
  const [sortDirection, setSortDirection] = useState("desc");
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("2"); // Default to Transport Company

  // Advanced filter states
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState(DEFAULT_FILTERS);

  // Dropdown states
  const [dropdownOpen, setDropdownOpen] = useState({
    status: false,
    kycStatus: false,
    subscription: false,
    pageSize: false,
  });

  // Memoized user type filter based on active tab
  const getUserTypeFilter = useCallback(() => {
    return parseInt(activeTab);
  }, [activeTab]);

  // Fetch users data
  const {
    data: usersResponse,
    isLoading,
    error,
    refetch,
  } = useGetUsersAdvanced({
    pageNumber: currentPage,
    pageSize,
    sortBy,
    sortDirection,
    searchTerm,
    userType: getUserTypeFilter(),
    status: filters.status || null,
    kycStatus: filters.kycStatus || null,
    subscriptionStatus: filters.subscriptionStatus || null,
    dateFrom: filters.dateFrom || null,
    dateTo: filters.dateTo || null,
    city: filters.city || null,
    state: filters.state || null,
  });

  const users = usersResponse?.users || [];
  const totalCount = usersResponse?.totalCount || 0;
  const statistics = usersResponse?.statistics || {};

  // Memoized event handlers to prevent unnecessary re-renders
  const handlePageChange = useCallback((page) => {
    setCurrentPage(page);
  }, []);

  const handlePerRowsChange = useCallback((newPageSize) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  }, []);

  const handleSort = useCallback((column, sortDirection) => {
    setSortBy(column.sortField || column.selector);
    setSortDirection(sortDirection);
  }, []);

  const handleSearch = useCallback((e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  }, []);

  const handleTabChange = useCallback((tab) => {
    setActiveTab(tab);
    setCurrentPage(1);
  }, []);

  const handleFilterChange = useCallback((filterName, value) => {
    setFilters((prev) => ({
      ...prev,
      [filterName]: value,
    }));
    setCurrentPage(1);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(DEFAULT_FILTERS);
    setSearchTerm("");
    setCurrentPage(1);
  }, []);

  const handleViewUser = useCallback(
    (userId) => {
      navigate(`/customers/view/${userId}`);
    },
    [navigate]
  );

  const columns = useMemo(() => {
    return [
      {
        name: "User",
        selector: (row) =>
          row.displayName || `${row.firstName} ${row.lastName}`,
        sortable: true,
        sortField: "displayName",
        cell: (row) => <UserCell row={row} />,
        minWidth: "250px",
      },
      {
        name: "Contact",
        cell: (row) => (
          <div>
            <div className="d-flex align-items-center mb-1">
              <FaPhone size={12} className="text-muted me-2" />
              <span className="small">{row.phoneNumber}</span>
            </div>
            <div className="d-flex align-items-center">
              <FaMapMarkerAlt size={12} className="text-muted me-2" />
              <span className="small text-muted">
                {row.address?.city}, {row.address?.state}
              </span>
            </div>
          </div>
        ),
        minWidth: "200px",
      },
      {
        name: "Type",
        selector: (row) => USER_TYPE_LABELS[row.userType],
        sortable: true,
        sortField: "userType",
        cell: (row) => (
          <span className="badge bg-info">
            {USER_TYPE_LABELS[row.userType] || "Unknown"}
          </span>
        ),
        minWidth: "150px",
      },
      {
        name: "Status",
        selector: (row) => row.status,
        sortable: true,
        sortField: "status",
        cell: (row) => <StatusBadge status={row.status} type="status" />,
        minWidth: "100px",
      },
      {
        name: "KYC Status",
        selector: (row) => row.kycStatus,
        sortable: true,
        sortField: "kycStatus",
        cell: (row) => <StatusBadge status={row.kycStatus} type="kyc" />,
        minWidth: "120px",
      },
      {
        name: "Subscription",
        cell: (row) => (
          <div>
            <StatusBadge
              status={row.subscription?.status}
              type="subscription"
            />
            {row.subscription?.planName && (
              <div className="small text-muted mt-1">
                {row.subscription.planName}
              </div>
            )}
          </div>
        ),
        minWidth: "130px",
      },
      {
        name: "Created",
        selector: (row) => row.createdAt,
        sortable: true,
        sortField: "createdAt",
        cell: (row) => (
          <div className="small">
            {new Date(row.createdAt).toLocaleDateString()}
          </div>
        ),
        minWidth: "100px",
      },
      {
        name: "Actions",
        cell: (row) => (
          <div className="d-flex">
            <Button
              color="primary"
              size="sm"
              outline
              onClick={() => handleViewUser(row.userId)}
              title="View User Details"
            >
              <FaEye size={12} />
            </Button>
          </div>
        ),
        minWidth: "100px",
        center: true,
      },
    ];
  }, [handleViewUser]);

  // Memoized dropdown toggle
  const toggleDropdown = useCallback((dropdownName) => {
    setDropdownOpen((prev) => ({
      ...prev,
      [dropdownName]: !prev[dropdownName],
    }));
  }, []);

  // Memoized active filter count
  const activeFilterCount = useMemo(() => {
    return (
      Object.values(filters).filter((value) => value !== "").length +
      (searchTerm ? 1 : 0)
    );
  }, [filters, searchTerm]);

  // Memoized icon component for user type
  const getUserTypeIcon = useCallback((userType) => {
    const config = USER_TYPE_CONFIG[userType];
    if (!config) return <FaUser />;

    const iconMap = {
      FaTruck: <FaTruck />,
      FaHandshake: <FaHandshake />,
      FaShippingFast: <FaShippingFast />,
      FaIndustry: <FaIndustry />,
      FaUserCircle: <FaUserCircle />,
    };

    return iconMap[config.icon] || <FaUser />;
  }, []);

  return (
    <div className="container-fluid py-4">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h4 className="mb-1">Users Management</h4>
              <p className="text-muted mb-0">
                Manage user onboarding and KYC processes
              </p>
            </div>
            <div className="d-flex">
              <Button
                color="outline-primary"
                size="sm"
                onClick={() => setShowFilters(!showFilters)}
                className="position-relative"
              >
                <FaFilter size={14} className="me-2" />
                Filters
                {activeFilterCount > 0 && (
                  <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                    {activeFilterCount}
                  </span>
                )}
                {showFilters ? (
                  <FaChevronUp size={12} className="ms-2" />
                ) : (
                  <FaChevronDown size={12} className="ms-2" />
                )}
              </Button>
              <Button
                color="outline-secondary"
                size="sm"
                onClick={clearFilters}
                className="ms-2"
              >
                <FaSync size={14} className="me-2" />
                Reset
              </Button>
              <Button color="outline-success" size="sm" className="ms-2">
                <FaDownload size={14} className="me-2" />
                Export
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Advanced Filters */}
      <Collapse isOpen={showFilters}>
        <Card className="mb-4">
          <CardBody>
            <Row>
              <Col md={3}>
                <FormGroup>
                  <Label
                    for="statusFilter"
                    className="form-label small fw-semibold"
                  >
                    Status
                  </Label>
                  <Dropdown
                    isOpen={dropdownOpen.status}
                    toggle={() => toggleDropdown("status")}
                  >
                    <DropdownToggle
                      caret
                      className="form-control text-start d-flex justify-content-between align-items-center bg-white border"
                    >
                      {filters.status
                        ? USER_STATUS_LABELS[filters.status]
                        : "All Status"}
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem
                        onClick={() => handleFilterChange("status", "")}
                      >
                        All Status
                      </DropdownItem>
                      {Object.entries(USER_STATUS_LABELS).map(
                        ([value, label]) => (
                          <DropdownItem
                            key={value}
                            onClick={() => handleFilterChange("status", value)}
                          >
                            {label}
                          </DropdownItem>
                        )
                      )}
                    </DropdownMenu>
                  </Dropdown>
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label
                    for="kycFilter"
                    className="form-label small fw-semibold"
                  >
                    KYC Status
                  </Label>
                  <Dropdown
                    isOpen={dropdownOpen.kycStatus}
                    toggle={() => toggleDropdown("kycStatus")}
                  >
                    <DropdownToggle
                      caret
                      className="form-control text-start d-flex justify-content-between align-items-center bg-white border"
                    >
                      {filters.kycStatus
                        ? KYC_STATUS_LABELS[filters.kycStatus]
                        : "All KYC Status"}
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem
                        onClick={() => handleFilterChange("kycStatus", "")}
                      >
                        All KYC Status
                      </DropdownItem>
                      {Object.entries(KYC_STATUS_LABELS).map(
                        ([value, label]) => (
                          <DropdownItem
                            key={value}
                            onClick={() =>
                              handleFilterChange("kycStatus", value)
                            }
                          >
                            {label}
                          </DropdownItem>
                        )
                      )}
                    </DropdownMenu>
                  </Dropdown>
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label
                    for="subscriptionFilter"
                    className="form-label small fw-semibold"
                  >
                    Subscription
                  </Label>
                  <Dropdown
                    isOpen={dropdownOpen.subscription}
                    toggle={() => toggleDropdown("subscription")}
                  >
                    <DropdownToggle
                      caret
                      className="form-control text-start d-flex justify-content-between align-items-center bg-white border"
                    >
                      {filters.subscriptionStatus
                        ? SUBSCRIPTION_STATUS_LABELS[filters.subscriptionStatus]
                        : "All Subscriptions"}
                    </DropdownToggle>
                    <DropdownMenu className="w-100">
                      <DropdownItem
                        onClick={() =>
                          handleFilterChange("subscriptionStatus", "")
                        }
                      >
                        All Subscriptions
                      </DropdownItem>
                      {Object.entries(SUBSCRIPTION_STATUS_LABELS).map(
                        ([value, label]) => (
                          <DropdownItem
                            key={value}
                            onClick={() =>
                              handleFilterChange("subscriptionStatus", value)
                            }
                          >
                            {label}
                          </DropdownItem>
                        )
                      )}
                    </DropdownMenu>
                  </Dropdown>
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label
                    for="cityFilter"
                    className="form-label small fw-semibold"
                  >
                    City
                  </Label>
                  <Input
                    type="text"
                    id="cityFilter"
                    placeholder="Enter city"
                    value={filters.city}
                    onChange={(e) => handleFilterChange("city", e.target.value)}
                  />
                </FormGroup>
              </Col>
            </Row>

            <Row>
              <Col md={3}>
                <FormGroup>
                  <Label
                    for="stateFilter"
                    className="form-label small fw-semibold"
                  >
                    State
                  </Label>
                  <Input
                    type="text"
                    id="stateFilter"
                    placeholder="Enter state"
                    value={filters.state}
                    onChange={(e) =>
                      handleFilterChange("state", e.target.value)
                    }
                  />
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label
                    for="dateFromFilter"
                    className="form-label small fw-semibold"
                  >
                    Date From
                  </Label>
                  <Input
                    type="date"
                    id="dateFromFilter"
                    value={filters.dateFrom}
                    onChange={(e) =>
                      handleFilterChange("dateFrom", e.target.value)
                    }
                  />
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label
                    for="dateToFilter"
                    className="form-label small fw-semibold"
                  >
                    Date To
                  </Label>
                  <Input
                    type="date"
                    id="dateToFilter"
                    value={filters.dateTo}
                    onChange={(e) =>
                      handleFilterChange("dateTo", e.target.value)
                    }
                  />
                </FormGroup>
              </Col>

              <Col md={3}>
                <FormGroup>
                  <Label className="form-label small fw-semibold">
                    Actions
                  </Label>
                  <div className="d-flex">
                    <Button
                      color="primary"
                      size="sm"
                      onClick={() => refetch()}
                      disabled={isLoading}
                    >
                      <FaSearch size={14} className="me-2" />
                      Apply
                    </Button>
                    <Button
                      color="outline-secondary"
                      size="sm"
                      onClick={clearFilters}
                      className="ms-2"
                    >
                      <FaTimes size={14} className="me-2" />
                      Clear
                    </Button>
                  </div>
                </FormGroup>
              </Col>
            </Row>
          </CardBody>
        </Card>
      </Collapse>

      {/* User Type Tabs */}
      <div className="row mb-4">
        <div className="col-12">
          <Nav tabs className="border-bottom">
            {Object.entries(USER_TYPE_CONFIG).map(([userType, config]) => (
              <NavItem key={userType}>
                <NavLink
                  className={`${
                    activeTab === userType ? "active" : ""
                  } user-select-none`}
                  onClick={() => handleTabChange(userType)}
                  role="button"
                >
                  {getUserTypeIcon(parseInt(userType))}
                  <span className="ms-2">{config.label}</span>
                  {/* You can add count badges here if needed */}
                </NavLink>
              </NavItem>
            ))}
          </Nav>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row mb-4">
        <div className="col-md-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <FaUser className="text-primary" size={24} />
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-muted small">Total Users</div>
                  <div className="h5 mb-0">{totalCount}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  {/* <FaCheckCircle className="text-success" size={24} /> */}
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-muted small">Active Users</div>
                  <div className="h5 mb-0">{statistics.activeUsers || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <FaClock className="text-warning" size={24} />
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-muted small">KYC Pending</div>
                  <div className="h5 mb-0">{statistics.kycPending || 0}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-md-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <FaBuilding className="text-info" size={24} />
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="text-muted small">With Subscription</div>
                  <div className="h5 mb-0">
                    {statistics.withActiveSubscription || 0}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Pagination Controls */}
      <div className="row mb-3">
        <div className="col-md-6">
          <div className="input-group">
            <span className="input-group-text">
              <FaSearch size={14} />
            </span>
            <Input
              type="text"
              placeholder="Search users by name, email, or phone..."
              value={searchTerm}
              onChange={handleSearch}
            />
            {searchTerm && (
              <Button
                color="outline-secondary"
                onClick={() => setSearchTerm("")}
                className="input-group-text user-select-none"
                type="button"
              >
                <FaTimes size={12} />
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className="card border-0 shadow-sm">
        <div className="card-body p-0">
          <DataTable
            columns={columns}
            data={users}
            pagination
            paginationServer
            paginationTotalRows={totalCount}
            paginationDefaultPage={currentPage}
            paginationPerPage={pageSize}
            paginationRowsPerPageOptions={[10, 20, 50, 100]}
            onChangeRowsPerPage={handlePerRowsChange}
            onChangePage={handlePageChange}
            onSort={handleSort}
            sortServer
            progressPending={isLoading}
            progressComponent={<LoadingComponent />}
            noDataComponent={<NoDataComponent />}
            customStyles={TABLE_CUSTOM_STYLES}
            responsive
            pointerOnHover
          />
        </div>
      </div>

      {error && (
        <div className="alert alert-danger mt-3">
          <strong>Error:</strong> {error.message || "Failed to load users"}
          <button
            className="btn btn-link btn-sm ms-2"
            onClick={() => refetch()}
          >
            Retry
          </button>
        </div>
      )}
    </div>
  );
};

export default memo(Customers);
