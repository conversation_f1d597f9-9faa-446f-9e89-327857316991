import * as Yup from 'yup';
import { USER_TYPES } from '@constants/enum';
import {
  validateDate,
  validateAge,
  DATE_FIELD_TYPES,
  AGE_LIMITS,
  calculateAge
} from '@utils/dateValidationUtils';

// Basic Details Validation
export const basicDetailsSchema = Yup.object().shape({
  firstName: Yup.string()
    .min(2, 'First Name must be between 2 and 50 characters')
    .max(50, 'First Name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid First Name')
    .required('First name is required'),
  lastName: Yup.string()
    .min(1, 'Last Name must be between 1 and 50 characters')
    .max(50, 'Last Name must be between 1 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Last Name'),
  email: Yup.string()
    .trim()
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter valid Email Address')
    .required('Email is required'),
  phoneNumber: Yup.string()
    .required('Mobile number is required')
    .test('phone-format', 'Enter valid Mobile Number (10 digits starting with 6-9)', function(value) {
      if (!value) return false;

      // Remove any non-digit characters
      const cleaned = value.replace(/\D/g, '');

      // Check if it's 10 digits (without country code) or 12 digits (with 91 country code)
      if (cleaned.length === 10) {
        return /^[6-9]\d{9}$/.test(cleaned);
      } else if (cleaned.length === 12) {
        return /^91[6-9]\d{9}$/.test(cleaned);
      }

      return false;
    }),
  userType: Yup.number().required('User type is required')
});

// Business Address Validation
export const businessAddressSchema = Yup.object().shape({
  address: Yup.string()
    .required('Business Address is required')
    .min(5, 'Business Address must be between 5 and 250 characters')
    .max(250, 'Business Address must be between 5 and 250 characters')
    .matches(/^[a-zA-Z0-9\s,.'#-/\\()]*$/, 'Enter valid Business Address'),
  city: Yup.string()
    .required('City is required')
    .min(2, 'City must be between 2 and 50 characters')
    .max(50, 'City must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid City'),
  state: Yup.string()
    .required('State is required')
    .min(2, 'State must be between 2 and 50 characters')
    .max(50, 'State must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid State'),
  country: Yup.string()
    .required('Country is required')
    .min(2, 'Country must be between 2 and 50 characters')
    .max(50, 'Country must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Country'),
  pincode: Yup.string()
    .required('Pin Code is required')
    .matches(/^\d{6}$/, 'Enter valid Pin Code (6 digits)')
    .length(6, 'Pin Code must be exactly 6 digits')
});

// Company Details Validation
export const companyDetailsSchema = Yup.object().shape({
  companyName: Yup.string()
    .min(3, 'Company Name must be between 3 to 150 characters')
    .max(150, 'Company Name must be between 3 to 150 characters')
    .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Company Name')
    .required('Company Name is required'),
  brandName: Yup.string()
    .min(3, 'Brand Name must be between 3 to 100 characters')
    .max(100, 'Brand Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Brand Name'),
  companyContactEmail: Yup.string()
    .trim()
    .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter valid Email Address')
    .required('Email is required'),
  companyContactPhone: Yup.string()
    .required('Mobile number is required')
    .test('phone-format', 'Enter valid Mobile Number (10 digits starting with 6-9)', function(value) {
      if (!value) return false;

      // Remove any non-digit characters
      const cleaned = value.replace(/\D/g, '');

      // Check if it's 10 digits (without country code) or 12 digits (with 91 country code)
      if (cleaned.length === 10) {
        return /^[6-9]\d{9}$/.test(cleaned);
      } else if (cleaned.length === 12) {
        return /^91[6-9]\d{9}$/.test(cleaned);
      }

      return false;
    }),
  companyLogo: Yup.string()
    .url('Invalid URL format')
    .nullable()
});

// PAN Card Details Validation
export const panCardDetailsSchema = Yup.object().shape({
  nameAsPerPan: Yup.string()
    .min(3, 'PAN Name must be between 3 to 100 characters')
    .max(100, 'PAN Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter valid PAN Name')
    .required('PAN Name is required'),
  fatherOrHusbandNameInPan: Yup.string()
    .min(3, 'Name must be between 3 to 100 characters')
    .max(100, 'Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter a valid Father/Husband Name')
    .required('Father/Husband Name is required'),
  dateOfBirthInPan: Yup.date()
    .typeError('Enter a valid date')
    .required('Date of Birth is required')
    .test('date-validation', 'Invalid date', function(value) {
      if (!value) return false;

      // Handle string dates (from date input)
      let dateToValidate = value;
      if (typeof value === 'string') {
        dateToValidate = new Date(value);
      }

      const validation = validateDate(dateToValidate, DATE_FIELD_TYPES.DATE_OF_BIRTH);
      if (!validation.isValid) {
        return this.createError({ message: validation.error });
      }
      return true;
    })
    .test('age-validation', 'Age validation failed', function(value) {
      if (!value) return false;

      // Handle string dates (from date input)
      let dateToValidate = value;
      if (typeof value === 'string') {
        dateToValidate = new Date(value);
      }

      const ageValidation = validateAge(dateToValidate, AGE_LIMITS.MIN_AGE);
      if (!ageValidation.isValid) {
        return this.createError({ message: ageValidation.error });
      }
      return true;
    }),
  panNumber: Yup.string()
    .required('PAN Number is required')
    .test('pan-format', 'Enter valid PAN number in format "**********"', function(value) {
      if (!value) return false; // Required field, so empty is invalid

      // Remove any whitespace and convert to uppercase
      const cleanValue = value.trim().toUpperCase();

      // Check format: 5 letters + 4 digits + 1 letter
      return /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(cleanValue) && cleanValue.length === 10;
    }),
  // Company-specific fields
  companyName: Yup.string().when('$userType', {
    is: (userType) => [USER_TYPES.TRANSPORT_COMPANY, USER_TYPES.CARRIER, USER_TYPES.SHIPPER_COMPANY].includes(userType),
    then: (schema) => schema
      .min(3, 'Company Name must be between 3 to 150 characters')
      .max(150, 'Company Name must be between 3 to 150 characters')
      .matches(/^[a-zA-Z0-9&.\- ]+$/, 'Enter valid Company Name')
      .required('Company Name is required'),
    otherwise: (schema) => schema.nullable()
  }),
  dateOfIncorporation: Yup.date().when('$userType', {
    is: (userType) => [USER_TYPES.TRANSPORT_COMPANY, USER_TYPES.CARRIER, USER_TYPES.SHIPPER_COMPANY].includes(userType),
    then: (schema) => schema
      .typeError('Enter a valid date')
      .required('Date of Incorporation is required')
      .test('incorporation-date-validation', 'Invalid incorporation date', function(value) {
        if (!value) return false;
        const validation = validateDate(value, DATE_FIELD_TYPES.DATE_OF_INCORPORATION);
        if (!validation.isValid) {
          return this.createError({ message: validation.error });
        }
        return true;
      }),
    otherwise: (schema) => schema.nullable()
  }),
  businessPanNumber: Yup.string()
    .nullable()
    .when('$userType', {
      is: (userType) => {
        const companyTypes = [USER_TYPES.TRANSPORT_COMPANY, USER_TYPES.CARRIER, USER_TYPES.SHIPPER_COMPANY];
        return companyTypes.includes(userType);
      },
      then: (schema) => schema
        .required('Business PAN Number is required')
        .test('pan-format', 'Enter valid PAN number in format "**********"', function(value) {
          if (!value) return false; // Required for company types

          // Remove any whitespace and convert to uppercase
          const cleanValue = value.trim().toUpperCase();

          // Check format: 5 letters + 4 digits + 1 letter
          return /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(cleanValue) && cleanValue.length === 10;
        }),
      otherwise: (schema) => schema
        .test('pan-format-optional', 'Enter valid PAN number in format "**********"', function(value) {
          // If no value for non-company types, pass validation
          if (!value || value.trim() === '') return true;

          // If value exists, validate format
          const cleanValue = value.trim().toUpperCase();
          return /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(cleanValue) && cleanValue.length === 10;
        })
    })
});

// Aadhaar Card Details Validation
export const aadhaarCardDetailsSchema = Yup.object().shape({
  aadhaarNumber: Yup.string()
    .required('Aadhaar Number is required')
    .matches(/^\d{12}$/, 'Aadhaar Number must be exactly 12 digits'),
  nameAsPerAadhaar: Yup.string()
    .min(3, 'Aadhaar Name must be between 3 to 100 characters')
    .max(100, 'Aadhaar Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter valid Aadhaar Name')
    .required('Aadhaar Name is required'),
  fatherOrHusbandNameInAadhaar: Yup.string()
    .min(3, 'Name must be between 3 to 100 characters')
    .max(100, 'Name must be between 3 to 100 characters')
    .matches(/^[a-zA-Z\s.'-]+$/, 'Enter a valid Father/Husband Name')
    .required('Father/Husband Name is required'),
  dateOfBirthInAadhaar: Yup.date()
    .typeError('Enter a valid date')
    .required('Date of Birth is required')
    .test('date-validation', 'Invalid date', function(value) {
      if (!value) return false;
      const validation = validateDate(value, DATE_FIELD_TYPES.DATE_OF_BIRTH);
      if (!validation.isValid) {
        return this.createError({ message: validation.error });
      }
      return true;
    })
    .test('age-validation', 'Age validation failed', function(value) {
      if (!value) return false;
      const ageValidation = validateAge(value, AGE_LIMITS.MIN_AGE);
      if (!ageValidation.isValid) {
        return this.createError({ message: ageValidation.error });
      }
      return true;
    }),
  genderInAadhaar: Yup.string()
    .oneOf(['Male', 'Female', 'Other'], 'Select a valid gender')
    .required('Gender is required')
});

// Bank Details Validation
export const bankDetailsSchema = Yup.object().shape({
  accountNumber: Yup.string()
    .matches(/^[0-9]+$/, 'Account Number must contain only numbers')
    .min(6, 'Account Number must be between 6 and 18 digits')
    .max(18, 'Account Number must be between 6 and 18 digits')
    .required('Account Number is required'),
  accountHolderName: Yup.string()
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Account Holder Name')
    .min(3, 'Account Holder Name must be between 3 and 100 characters')
    .max(100, 'Account Holder Name must be between 3 and 100 characters')
    .required('Account Holder Name is required'),
  bankName: Yup.string()
    .matches(/^[a-zA-Z\s]+$/, 'Enter valid Bank Name')
    .min(3, 'Bank Name must be between 3 and 100 characters')
    .max(100, 'Bank Name must be between 3 and 100 characters')
    .required('Bank Name is required'),
  ifscCode: Yup.string()
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC Code format')
    .length(11, 'IFSC Code must be 11 characters')
    .required('IFSC Code is required')
});

// GST Details Validation
export const gstDetailsSchema = Yup.object().shape({
  gstNumber: Yup.string()
    .when('$isRequired', {
      is: true,
      then: (schema) => schema.required('GST Number is required'),
      otherwise: (schema) => schema
    })
    .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}[A-Z]{1}[0-9A-Z]{1}$/, 'Enter a valid GST Number')
    .length(15, 'GST Number must be 15 characters'),
  legalName: Yup.string()
    .when('$isRequired', {
      is: true,
      then: (schema) => schema.required('Legal Name is required'),
      otherwise: (schema) => schema
    })
    .min(3, 'Legal Name must be between 3 to 150 characters')
    .max(150, 'Legal Name must be between 3 to 150 characters')
    .matches(/^(?![0-9]$)(?![\W_]$)[a-zA-Z0-9\s&.,-]+$/, 'Enter a valid Legal Name'),
  tradeName: Yup.string()
    .min(3, 'Trade Name must be between 3 to 150 characters')
    .max(150, 'Trade Name must be between 3 to 150 characters')
    .matches(/^(?![0-9]*$)(?![\W_]*$)[a-zA-Z0-9\s&.,-]+$/, 'Enter a valid Trade Name'),
  placeOfBusiness: Yup.string()
    .when('$isRequired', {
      is: true,
      then: (schema) => schema.required('Place of Business is required'),
      otherwise: (schema) => schema
    })
    .min(3, 'Place of Business must be between 3 to 200 characters')
    .max(200, 'Place of Business must be between 3 to 200 characters')
    .matches(/^(?![0-9]*$)(?![\W_]*$)[a-zA-Z0-9\s#&.,-/]+$/, 'Enter a valid Place of Business'),
  registrationDate: Yup.date()
    .when('$isRequired', {
      is: true,
      then: (schema) => schema.required('Registration Date is required'),
      otherwise: (schema) => schema
    })
    .test('registration-date-validation', 'Invalid registration date', function(value) {
      if (!value && this.options.context?.isRequired) return false;
      if (!value) return true; // Allow empty for non-required

      const validation = validateDate(value, DATE_FIELD_TYPES.REGISTRATION_DATE);
      if (!validation.isValid) {
        return this.createError({ message: validation.error });
      }
      return true;
    }),
  registrationNumber: Yup.string()
    .min(3, 'Registration Number must be between 3 to 50 characters')
    .max(50, 'Registration Number must be between 3 to 50 characters')
});

// CIN Number Validation
export const cinNumberSchema = Yup.object().shape({
  cin: Yup.string()
    .required('CIN Number is required')
    .matches(/^[LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/, 'Enter a valid CIN')
    .length(21, 'CIN must be exactly 21 characters')
});

// Industry Details Validation
export const industryDetailsSchema = Yup.object().shape({
  industryType: Yup.string()
    .required('Industry Type is required'),
  customIndustryType: Yup.string().when('industryType', {
    is: 'Others',
    then: (schema) => schema
      .required('Custom Industry Type is required')
      .min(3, 'Custom Industry Type must be between 3 and 50 characters')
      .max(50, 'Custom Industry Type must be between 3 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/, 'Enter valid Custom Industry Type'),
    otherwise: (schema) => schema.nullable().notRequired(),
  }),
  businessCategory: Yup.string()
    .required('Business Category is required'),
  customBusinessCategory: Yup.string().when('businessCategory', {
    is: 'Others',
    then: (schema) => schema
      .required('Custom Business Category is required')
      .min(3, 'Custom Business Category must be between 3 and 50 characters')
      .max(50, 'Custom Business Category must be between 3 and 50 characters')
      .matches(/^[a-zA-Z\s]+$/, 'Enter valid Custom Business Category'),
    otherwise: (schema) => schema.nullable().notRequired(),
  })
});

// Regulatory Document Validation
export const regulatoryDocumentSchema = Yup.object().shape({
  regulatoryLicenseNumber: Yup.string()
    .nullable()
    .trim()
    .matches(/^[a-zA-Z0-9\-\/\s]*$/, 'Enter valid Regulatory License Number')
});
