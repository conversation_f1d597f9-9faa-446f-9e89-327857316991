/* TriTrackz Consolidated CSS - All styles in one file */
/* Optimized for Bootstrap 5.3.6 with normal CSS classes (no CSS modules) */

/*
 * CONSOLIDATED CSS DOCUMENTATION
 * ==============================
 *
 * This file contains minimal custom CSS that complements Bootstrap 5.3.6
 *
 * BOOTSTRAP-FIRST APPROACH:
 * - Uses Bootstrap's built-in color system and utilities
 * - Uses <PERSON><PERSON><PERSON>'s button, card, layout, and spacing classes
 * - Uses Bootstrap's responsive breakpoints and grid system
 * - Uses Bootstrap's accessibility features
 *
 * CUSTOM CSS LIMITED TO:
 * - Essential keyframe animations
 * - Auth layout specific styles
 * - Performance optimizations
 * - Minimal backdrop effects
 */

/* ===== FONT IMPORTS ===== */
@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Sans:ital,wght@0,100..700;1,100..700&family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");

/* ===== GLOBAL SCROLLBAR MANAGEMENT ===== */
*,
html,
body {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
  font-family: "Inter", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

*::-webkit-scrollbar,
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
  /* WebKit browsers (Chrome, Safari, Edge) */
}

/* Global overflow management */
html,
body,
#root {
  overflow-x: hidden;
  overflow-y: auto;
}

/* Removed unused layout classes - using Bootstrap utilities instead */

/* ===== CUSTOM COLOR PALETTE ===== */
:root {
  /* ===== PRIMARY PALETTE (#0B1F3A) ===== */
  --primary-900: #0b1f3a;
  --primary-800: #1a2f4d;
  --primary-700: #293f60;
  --primary-600: #384f73;
  --primary-500: #475f86;
  --primary-400: #5f7599;
  --primary-300: #778bac;
  --primary-200: #8fa1bf;
  --primary-100: #a7b7d2;
  --primary-50: #bfcde5;

  /* ===== COMPLEMENTARY PALETTE (#3A260B) ===== */
  --complementary-900: #3a260b;
  --complementary-800: #4d361a;
  --complementary-700: #604629;
  --complementary-600: #735638;
  --complementary-500: #866647;
  --complementary-400: #997659;
  --complementary-300: #ac866b;
  --complementary-200: #bf967d;
  --complementary-100: #d2a68f;
  --complementary-50: #e5b6a1;

  /* ===== ANALOGOUS COLORS (Blue-Green Range) ===== */
  --analogous-blue-900: #0b3a26;
  --analogous-blue-800: #1a4d35;
  --analogous-blue-700: #296044;
  --analogous-blue-600: #387353;
  --analogous-blue-500: #478662;
  --analogous-blue-400: #5f9975;
  --analogous-blue-300: #77ac88;
  --analogous-blue-200: #8fbf9b;
  --analogous-blue-100: #a7d2ae;
  --analogous-blue-50: #bfe5c1;

  /* ===== TRIADIC COLORS ===== */
  --triadic-red-900: #3a0b26;
  --triadic-red-800: #4d1a35;
  --triadic-red-700: #602944;
  --triadic-red-600: #733853;
  --triadic-red-500: #864762;
  --triadic-red-400: #995f75;
  --triadic-red-300: #ac7788;
  --triadic-red-200: #bf8f9b;
  --triadic-red-100: #d2a7ae;
  --triadic-red-50: #e5bfc1;

  /* ===== SEMANTIC COLOR MAPPINGS ===== */
  --success-900: var(--analogous-blue-900);
  --success-800: var(--analogous-blue-800);
  --success-700: var(--analogous-blue-700);
  --success-600: var(--analogous-blue-600);
  --success-500: var(--analogous-blue-500);
  --success-400: var(--analogous-blue-400);
  --success-300: var(--analogous-blue-300);
  --success-200: var(--analogous-blue-200);
  --success-100: var(--analogous-blue-100);
  --success-50: var(--analogous-blue-50);

  --warning-900: var(--complementary-900);
  --warning-800: var(--complementary-800);
  --warning-700: var(--complementary-700);
  --warning-600: var(--complementary-600);
  --warning-500: var(--complementary-500);
  --warning-400: var(--complementary-400);
  --warning-300: var(--complementary-300);
  --warning-200: var(--complementary-200);
  --warning-100: var(--complementary-100);
  --warning-50: var(--complementary-50);

  --danger-900: var(--triadic-red-900);
  --danger-800: var(--triadic-red-800);
  --danger-700: var(--triadic-red-700);
  --danger-600: var(--triadic-red-600);
  --danger-500: var(--triadic-red-500);
  --danger-400: var(--triadic-red-400);
  --danger-300: var(--triadic-red-300);
  --danger-200: var(--triadic-red-200);
  --danger-100: var(--triadic-red-100);
  --danger-50: var(--triadic-red-50);

  --info-900: var(--primary-900);
  --info-800: var(--primary-800);
  --info-700: var(--primary-700);
  --info-600: var(--primary-600);
  --info-500: var(--primary-500);
  --info-400: var(--primary-400);
  --info-300: var(--primary-300);
  --info-200: var(--primary-200);
  --info-100: var(--primary-100);
  --info-50: var(--primary-50);

  /* ===== NEUTRAL COLORS ===== */
  --white: #ffffff;
  --black: #000000;

  /* ===== LIGHT BORDERS AND BACKGROUNDS ===== */
  --border-light: #e5e7eb;
  --border-light-dark: #374151;

  /* ===== BOOTSTRAP CSS VARIABLE OVERRIDES ===== */
  /* Override Bootstrap's default colors with our custom palette */
  --bs-primary: var(--primary-600);
  --bs-primary-rgb: 56, 79, 115;
  --bs-secondary: var(--primary-400);
  --bs-secondary-rgb: 95, 117, 153;
  --bs-success: var(--success-600);
  --bs-success-rgb: 56, 115, 83;
  --bs-danger: var(--danger-600);
  --bs-danger-rgb: 115, 56, 83;
  --bs-warning: var(--warning-600);
  --bs-warning-rgb: 115, 86, 56;
  --bs-info: var(--info-600);
  --bs-info-rgb: 56, 79, 115;
  --bs-light: #f8f9fa;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark: var(--primary-900);
  --bs-dark-rgb: 11, 31, 58;

  /* Bootstrap button colors */
  --bs-btn-color: #fff;
  --bs-btn-bg: var(--primary-600);
  --bs-btn-border-color: var(--primary-600);
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: var(--primary-700);
  --bs-btn-hover-border-color: var(--primary-700);
  --bs-btn-focus-shadow-rgb: 56, 79, 115;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: var(--primary-800);
  --bs-btn-active-border-color: var(--primary-800);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: var(--primary-400);
  --bs-btn-disabled-border-color: var(--primary-400);
}

/* ===== GLOBAL KEYFRAME ANIMATIONS ===== */

/* Float Animation */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Icon Float Animation */
@keyframes iconFloat {
  0%,
  100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-5px) scale(1.05);
  }
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Slide Up Animation */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Slide In Left Animation */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide In Right Animation */
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Fade In Up Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success Bounce Animation */
@keyframes successBounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Shake Error Animation */
@keyframes shakeError {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

/* Spin Animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Shimmer Animation */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Pulse Animation */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Form Slide In Animation */
@keyframes formSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Error Slide In Animation */
@keyframes slideInError {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Success Slide Down Animation */
@keyframes successSlideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ===== THEME-SPECIFIC BOOTSTRAP OVERRIDES ===== */

/* Dark theme (default) */
[data-bs-theme="dark"] {
  --bs-body-bg: var(--primary-900);
  --bs-body-color: #ffffff;
  --bs-border-color: var(--primary-700);
  --bs-secondary-bg: var(--primary-800);
  --bs-tertiary-bg: var(--primary-700);
}

/* Light theme */
[data-bs-theme="light"] {
  --bs-body-bg: #ffffff;
  --bs-body-color: var(--primary-900);
  --bs-border-color: var(--primary-200);
  --bs-secondary-bg: var(--primary-50);
  --bs-tertiary-bg: var(--primary-100);

  /* Override primary colors for light theme */
  --bs-primary: var(--primary-100);
  --bs-primary-rgb: 167, 183, 210;
  --bs-dark: var(--primary-800);
  --bs-dark-rgb: 26, 47, 77;
}

/* Theme-specific component styling */
[data-bs-theme="light"] .bg-primary {
  background-color: var(--primary-100) !important;
  color: var(--primary-900) !important;
}

[data-bs-theme="light"] .text-white {
  color: var(--primary-900) !important;
}

[data-bs-theme="light"] .text-light {
  color: var(--primary-700) !important;
}

[data-bs-theme="light"] .btn-outline-light {
  color: var(--primary-700) !important;
  border-color: var(--primary-300) !important;
}

[data-bs-theme="light"] .btn-outline-light:hover {
  background-color: var(--primary-200) !important;
  border-color: var(--primary-400) !important;
  color: var(--primary-900) !important;
}

[data-bs-theme="dark"] .bg-primary {
  background-color: var(--primary-800) !important;
  color: #ffffff !important;
}

[data-bs-theme="dark"] .text-white {
  color: #ffffff !important;
}

[data-bs-theme="dark"] .text-light {
  color: #e8f0ff !important;
}

[data-bs-theme="dark"] .btn-outline-light {
  color: #e8f0ff !important;
  border-color: var(--primary-600) !important;
}

[data-bs-theme="dark"] .btn-outline-light:hover {
  background-color: var(--primary-700) !important;
  border-color: var(--primary-500) !important;
  color: #ffffff !important;
}

/* Dropdown menu theme styling */
[data-bs-theme="light"] .dropdown-menu {
  background-color: #ffffff !important;
  border-color: var(--primary-200) !important;
  color: var(--primary-900) !important;
}

[data-bs-theme="light"] .dropdown-item {
  color: var(--primary-900) !important;
}

[data-bs-theme="light"] .dropdown-item:hover {
  background-color: var(--primary-100) !important;
  color: var(--primary-900) !important;
}

[data-bs-theme="light"] .dropdown-header {
  color: var(--primary-700) !important;
}

[data-bs-theme="dark"] .dropdown-menu {
  background-color: var(--primary-800) !important;
  border-color: var(--primary-600) !important;
  color: #ffffff !important;
}

[data-bs-theme="dark"] .dropdown-item {
  color: #ffffff !important;
}

[data-bs-theme="dark"] .dropdown-item:hover {
  background-color: var(--primary-700) !important;
  color: #ffffff !important;
}

[data-bs-theme="dark"] .dropdown-header {
  color: #e8f0ff !important;
}

/* Topbar - Same background as main content */
.position-sticky.top-0 {
  background-color: var(--bs-body-bg) !important;
  color: var(--bs-body-color) !important;
}

/* Ensure topbar matches body background in both themes */
[data-bs-theme="dark"] .position-sticky.top-0 {
  background-color: var(--primary-900) !important;
  color: #ffffff !important;
}

[data-bs-theme="light"] .position-sticky.top-0 {
  background-color: #ffffff !important;
  color: var(--primary-900) !important;
}

/* Update button colors for the new background */
[data-bs-theme="dark"] .btn-outline-light {
  color: #ffffff !important;
  border-color: var(--primary-600) !important;
}

[data-bs-theme="dark"] .btn-outline-light:hover {
  background-color: var(--primary-800) !important;
  border-color: var(--primary-500) !important;
  color: #ffffff !important;
}

[data-bs-theme="light"] .btn-outline-light {
  color: var(--primary-700) !important;
  border-color: var(--primary-300) !important;
}

[data-bs-theme="light"] .btn-outline-light:hover {
  background-color: var(--primary-100) !important;
  border-color: var(--primary-400) !important;
  color: var(--primary-900) !important;
}

/* ===== USING BOOTSTRAP TRANSITIONS ONLY ===== */
/* Bootstrap provides smooth transitions for interactive elements */

/* ===== BOOTSTRAP COMPONENT OVERRIDES ===== */

/* Button enhancements with custom colors */
.btn-primary {
  background-color: var(--primary-600) !important;
  border-color: var(--primary-600) !important;
  color: #ffffff !important;
}

.btn-primary:hover {
  background-color: var(--primary-700) !important;
  border-color: var(--primary-700) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  transition: transform 0.2s ease;
}

/* Keep only used button styles */

/* Background color utilities - Keep only used ones */
.bg-primary {
  background-color: var(--primary-600) !important;
}

.bg-danger {
  background-color: var(--danger-600) !important;
}

/* Card hover effects */
.card:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* ===== BOOTSTRAP BUTTONS ONLY ===== */
/* Using Bootstrap's built-in button classes: .btn, .btn-primary, .btn-secondary, .btn-success, .btn-danger, .btn-warning, .btn-info, .btn-light, .btn-dark */
/* Button sizes: .btn-sm, .btn-lg */
/* Button variants: .btn-outline-primary, .btn-outline-secondary, etc. */

/* ===== USING BOOTSTRAP CARDS ONLY ===== */
/* Bootstrap provides: .card, .card-body, .card-header, .card-footer, .card-title, .card-text */
/* Card variants: .card-primary, .card-secondary, .card-success, .card-danger, .card-warning, .card-info, .card-light, .card-dark */

/* ===== USING BOOTSTRAP LAYOUT ONLY ===== */
/* Bootstrap provides: .container, .container-fluid, .row, .col, .d-flex, .flex-column, .flex-row, etc. */

/* ===== USING BOOTSTRAP NAVBAR AND BUTTONS ONLY ===== */
/* Bootstrap provides: .navbar, .navbar-brand, .navbar-nav, .nav-link, .btn, .btn-outline-*, etc. */

/* ===== AUTH FORM COMPONENT STYLES ===== */
/* Converted from AuthForm.module.css */

.tritracz-container {
  background: linear-gradient(
    135deg,
    var(--primary-800) 0%,
    var(--primary-600) 100%
  );
}

.tritracz-video-background {
  object-fit: cover;
  z-index: 0;
  will-change: transform;
  backface-visibility: hidden;
  pointer-events: none;
}

.tritracz-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgb(11 31 58 / 30%);
  z-index: 1;
}

.hero-section {
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 60px;
  overflow: hidden;
  transform: translateX(-30px);
  opacity: 0;
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
}

.hero-section.loaded {
  transform: translateX(0);
  opacity: 1;
}

.form-section {
  flex: 0 0 600px;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 60px 40px;
  transform: translateX(30px);
  opacity: 0;
  transition: all 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s;
  z-index: 3;
  overflow: hidden;
  position: relative;
}

.form-section.loaded {
  transform: translateX(0);
  opacity: 1;
}

.form-container {
  width: 100%;
  max-width: 500px;
}
.form-control {
  padding: 0.75rem 1rem;
}
.react-tel-input .form-control {
  border: var(--bs-border-width) solid var(--bs-border-color) !important;
  border-radius: var(--bs-border-radius) !important;
  color: var(--text-primary) !important;
  background-color: transparent !important;
  height: auto !important;
  width: 100% !important;
}
.react-tel-input .flag-dropdown {
  border: none !important;
  background-color: transparent !important;
}
/* Auth Form Styles */
.auth-form {
  animation: formSlideIn 0.5s ease-out;
}

.auth-welcome-text h2 {
  color: var(--text-primary);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.auth-welcome-text p {
  color: #f8f9fa;
  font-size: 1rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.auth-form .form-label {
  color: #f8f9fa;
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.auth-form .form-control {
  border: 2px solid var(--accent-complementary);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  color: var(--text-primary);
}

.auth-form .form-control:focus {
  border-color: var(--btn-primary-bg);
  box-shadow: 0 0 0 0.2rem rgba(11, 31, 58, 0.25);
  outline: none;
}

.auth-form .form-control.is-invalid {
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.1) !important;
}

.auth-form .btn-primary {
  background: var(--btn-primary-bg);
  border-color: var(--btn-primary-bg);
  box-shadow: 0 4px 15px rgba(11, 31, 58, 0.3);
  color: var(--btn-primary-text);
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  border-width: 2px;
}

.auth-form .btn-primary:hover {
  background: var(--btn-primary-hover-bg);
  border-color: var(--btn-primary-hover-bg);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(11, 31, 58, 0.4);
}

.auth-form a {
  color: var(--text-secondary);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  letter-spacing: 0.05em;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.auth-form a:hover {
  color: #e9ecef;
  text-decoration: underline !important;
  transform: translateY(-1px);
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

/* Error Messages */
.invalid-feedback,
.clean-form-error,
.text-danger,
.error-message,
.form-error,
.phone-error-message {
  color: #dc3545 !important;
  font-size: 0.875rem;
  font-weight: 500;
  margin-top: 0.5rem;
  line-height: 1.4;
  animation: slideInError 0.3s ease-out;
}

/* ===== USING BOOTSTRAP UTILITIES ONLY ===== */
/* Bootstrap provides: .text-primary, .text-secondary, .text-success, .text-danger, .text-warning, .text-info, .text-light, .text-dark, .text-muted */
/* Background utilities: .bg-primary, .bg-secondary, .bg-success, .bg-danger, .bg-warning, .bg-info, .bg-light, .bg-dark */
/* Spacing utilities: .m-*, .p-*, .mt-*, .mb-*, .ms-*, .me-*, .mx-*, .my-* */
/* Display utilities: .d-none, .d-block, .d-flex, .d-inline, .d-inline-block */
/* Flexbox utilities: .justify-content-*, .align-items-*, .flex-*, .order-* */

/* Keep only essential animation classes that are actually used */

/* Essential backdrop blur for auth forms */
.backdrop-blur {
  -webkit-backdrop-filter: blur(20px);
  backdrop-filter: blur(20px);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Responsive Styles for Auth Layout */
@media (max-width: 768px) {
  .hero-section {
    flex: 1;
    padding: 40px 20px;
    justify-content: center;
    text-align: center;
  }

  .form-section {
    flex: none;
    padding: 40px 20px;
    border-left: none;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}

@media (max-width: 576px) {
  h2 {
    font-size: 1.5rem;
  }
}

/* ===== USING BOOTSTRAP ACCESSIBILITY FEATURES ===== */
/* Bootstrap provides: .visually-hidden, .visually-hidden-focusable, .sr-only (deprecated) */

/* ===== STATISTICS CARDS DESIGN ===== */
/* Minimal CSS for exact card design matching the provided image */

.stat-card {
  background: #2c3e50 !important;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
}

.stat-icon-primary {
  background: #3498db !important;
}

.stat-icon-success {
  background: #27ae60 !important;
}

.stat-icon-warning {
  background: #f39c12 !important;
}

.stat-icon-info {
  background: #95a5a6 !important;
}

/* Theme support for stat cards */
[data-bs-theme="light"] .stat-card {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08) !important;
}

[data-bs-theme="light"] .stat-label {
  color: #6c757d !important;
}

[data-bs-theme="light"] .stat-value {
  color: #212529 !important;
}

/* ===== SEARCH INPUT DESIGN ===== */
/* Minimal CSS for exact search input design matching the provided image */

.search-input-wrapper {
  position: relative;
  width: 100%;
}

.search-input {
  background: #34495e !important;
  border-radius: 8px !important;
  padding: 12px 16px 12px 48px !important;
  color: #ffffff !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.search-input::placeholder {
  color: #bdc3c7 !important;
}

.search-input:focus {
  background: #3d566e !important;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3) !important;
  outline: none !important;
}

.search-icon {
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  font-size: 14px;
}

.search-clear-btn {
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  background: none !important;
  border: none !important;
  padding: 4px !important;
}

.search-clear-btn:hover {
  color: #ffffff !important;
}

/* ===== DATA TABLE WRAPPER DESIGN ===== */
/* Minimal CSS for exact table design matching the provided image */

.data-table-wrapper {
  background: #2c3e50 !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
}

/* Theme support for search and table */
[data-bs-theme="light"] .search-input {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  color: #212529 !important;
}

[data-bs-theme="light"] .search-input::placeholder {
  color: #6c757d !important;
}

[data-bs-theme="light"] .search-input:focus {
  background: #ffffff !important;
  border-color: #86b7fe !important;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
}

[data-bs-theme="light"] .search-icon {
  color: #6c757d !important;
}

[data-bs-theme="light"] .search-clear-btn {
  color: #6c757d !important;
}

[data-bs-theme="light"] .data-table-wrapper {
  background: #ffffff !important;
  border: 1px solid #e9ecef !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08) !important;
}

/* Reduce Motion for Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* ===== PRINT STYLES ===== */

@media print {
  .hero-section {
    display: none;
  }

  .form-section {
    width: 100% !important;
  }

  .btn {
    display: none !important;
  }
}
.auth-form,
.tritracz-video-background {
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
