import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { DOCUMENT_TYPES } from '@api/documentUploadHooks';
import DocumentUpload from './DocumentUpload';

const CinNumberForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">CIN Number</h3>
        <p className="clean-section-subtitle">Please provide your Corporate Identification Number</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-12 mb-3">
            <label htmlFor="cinNumber.cin" className="clean-form-label">
              CIN Number <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="cinNumber.cin"
              className={`clean-form-control ${
                formik.touched.cinNumber?.cin && formik.errors.cinNumber?.cin
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter CIN number"
              style={{ textTransform: 'uppercase' }}
            />
            <ErrorMessage
              name="cinNumber.cin"
              component="div"
              className="clean-form-error"
            />
            <div className="form-text">
              Corporate Identification Number (CIN) is a unique 21-digit alphanumeric code.
            </div>
          </div>

          {/* CIN Document Upload - Optional */}
          <DocumentUpload
            documentType={DOCUMENT_TYPES.TRADE_LICENSE}
            title="Trade Document"
            accept=".pdf,.jpg,.jpeg,.png"
            existingDocuments={existingDocuments}
            onDocumentChange={onDocumentChange}
            optional={true}
          />
        </div>
      </div>
    </div>
  );
};

export default CinNumberForm;
