import * as Yup from 'yup';

// Common validation patterns
export const VALIDATION_PATTERNS = {
  // Name patterns
  NAME: /^[a-zA-Z\s]+$/,
  NAME_WITH_SPECIAL: /^[a-zA-Z\s.'-]+$/,
  
  // Email pattern
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
  
  // Phone patterns
  INDIAN_MOBILE: /^[6-9]\d{9}$/, // Exactly 10 digits starting with 6-9
  INDIAN_MOBILE_WITH_COUNTRY_CODE: /^91[6-9]\d{9}$/, // With 91 country code (12 digits total)
  
  // Document patterns
  PAN: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
  AADHAAR: /^\d{12}$/,
  GST: /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/,
  CIN: /^[LU][0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/,
  IFSC: /^[A-Z]{4}0[A-Z0-9]{6}$/,
  
  // Address patterns
  PINCODE: /^\d{6}$/,
  
  // Company patterns
  COMPANY_NAME: /^[a-zA-Z0-9&.\- ]+$/,
  
  // Password pattern
  PASSWORD_UPPERCASE: /[A-Z]/,
  PASSWORD_LOWERCASE: /[a-z]/,
  PASSWORD_NUMBER: /\d/,
  PASSWORD_SPECIAL: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/,
  
  // Business patterns
  BUSINESS_ADDRESS: /^[a-zA-Z0-9\s,.'#-/\\()]*$/,
  LEGAL_NAME: /^(?![0-9]$)(?![\W_]$)[a-zA-Z0-9\s&.,-]+$/,
  TRADE_NAME: /^(?![0-9]*$)(?![\W_]*$)[a-zA-Z0-9\s&.,-]+$/,
  PLACE_OF_BUSINESS: /^(?![0-9]*$)(?![\W_]*$)[a-zA-Z0-9\s#&.,-/]+$/,
  
  // Regulatory patterns
  REGULATORY_LICENSE: /^[a-zA-Z0-9\-\/\s]*$/
};

// Common validation messages
export const VALIDATION_MESSAGES = {
  REQUIRED: {
    FIRST_NAME: 'First name is required',
    LAST_NAME: 'Last name is required',
    EMAIL: 'Email is required',
    MOBILE: 'Mobile number is required',
    PASSWORD: 'Password is required',
    CONFIRM_PASSWORD: 'Confirm Password is required',
    PAN_NUMBER: 'PAN Number is required',
    PAN_NAME: 'PAN Name is required',
    FATHER_HUSBAND_NAME: 'Father/Husband Name is required',
    DOB: 'Date of Birth is required',
    AADHAAR_NUMBER: 'Aadhaar Number is required',
    GENDER: 'Gender is required',
    ACCOUNT_NUMBER: 'Account Number is required',
    ACCOUNT_HOLDER_NAME: 'Account Holder Name is required',
    BANK_NAME: 'Bank Name is required',
    IFSC_CODE: 'IFSC Code is required',
    COMPANY_NAME: 'Company Name is required',
    GST_NUMBER: 'GST Number is required',
    LEGAL_NAME: 'Legal Name is required',
    PLACE_OF_BUSINESS: 'Place of Business is required',
    REGISTRATION_DATE: 'Registration Date is required',
    CIN_NUMBER: 'CIN Number is required',
    INDUSTRY_TYPE: 'Industry Type is required',
    BUSINESS_CATEGORY: 'Business Category is required',
    BUSINESS_ADDRESS: 'Business Address is required',
    CITY: 'City is required',
    STATE: 'State is required',
    COUNTRY: 'Country is required',
    PIN_CODE: 'Pin Code is required'
  },
  
  LENGTH: {
    FIRST_NAME: 'First Name must be between 2 and 50 characters',
    LAST_NAME: 'Last Name must be between 1 and 50 characters',

    MOBILE: 'Mobile number must be exactly 10 digits',
    PASSWORD: 'Password must be between 8 to 15 characters',
    PAN_NAME: 'PAN Name must be between 3 to 100 characters',
    FATHER_HUSBAND_NAME: 'Name must be between 3 to 100 characters',
    PAN_NUMBER: 'PAN must be exactly 10 characters',
    AADHAAR_NUMBER: 'Aadhaar Number must be exactly 12 digits',
    ACCOUNT_NUMBER: 'Account Number must be between 6 and 18 digits',
    ACCOUNT_HOLDER_NAME: 'Account Holder Name must be between 3 and 100 characters',
    BANK_NAME: 'Bank Name must be between 3 and 100 characters',
    IFSC_CODE: 'IFSC Code must be 11 characters',
    COMPANY_NAME: 'Company Name must be between 3 to 150 characters',
    BRANDING_NAME: 'Branding Name must be between 3 to 100 characters',
    GST_NUMBER: 'GST Number must be 15 characters',
    LEGAL_NAME: 'Legal Name must be between 3 to 150 characters',
    TRADE_NAME: 'TradeName must be between 3 to 150 characters',
    PLACE_OF_BUSINESS: 'Place of Business must be between 3 to 200 characters',
    CIN_NUMBER: 'CIN must be exactly 21 characters',
    CUSTOM_INDUSTRY: 'Custom Industry Type must be between 3 and 50 characters',
    CUSTOM_BUSINESS: 'Custom Business Category must be between 3 and 50 characters',
    BUSINESS_ADDRESS: 'Business Address must be between 5 and 250 characters',
    CITY: 'City must be between 3 and 50 characters',
    STATE: 'State must be between 3 and 50 characters',
    COUNTRY: 'Country must be between 3 and 50 characters',
    PIN_CODE: 'Pin Code must be 6 Digits'
  },
  
  FORMAT: {
    FIRST_NAME: 'Enter valid First Name',
    LAST_NAME: 'Enter valid Last Name',
    EMAIL: 'Enter valid Email Address',
    MOBILE: 'Enter valid Mobile Number',
    PASSWORD_COMPLEXITY: 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character',
    PASSWORD_MATCH: 'Passwords do not match',
    PAN_NUMBER: 'Enter valid PAN number in format "**********"',
    PAN_NAME: 'Enter valid PAN Name',
    FATHER_HUSBAND_NAME: 'Enter a valid Father/Husband Name',
    DOB_FUTURE: 'Date of Birth cannot be in the future',
    DOB_REALISTIC: 'Enter a realistic Date of Birth',
    DOB_INVALID: 'Enter a valid date',
    AADHAAR_NUMBER: 'Aadhaar Number must be exactly 12 digits',
    GENDER: 'Select a valid gender',
    ACCOUNT_NUMBER: 'Account Number must contain only numbers',
    ACCOUNT_HOLDER_NAME: 'Enter valid Account Holder Name',
    BANK_NAME: 'Enter valid Bank Name',
    IFSC_CODE: 'Invalid IFSC Code format',
    COMPANY_NAME: 'Enter valid Company Name',
    BRANDING_NAME: 'Enter valid Branding Name',
    GST_NUMBER: 'Enter a valid GST Number',
    LEGAL_NAME: 'Enter a valid Legal Name',
    TRADE_NAME: 'Enter a valid Trade Name',
    PLACE_OF_BUSINESS: 'Enter a valid Place of Business',
    REGISTRATION_DATE_FUTURE: 'Registration Date cannot be in the future',
    REGISTRATION_DATE_MIN: 'Registration Date cannot be before 01-Jan-1950',
    CIN_NUMBER: 'Enter a valid CIN',
    CUSTOM_INDUSTRY: 'Enter valid Custom Industry Type',
    CUSTOM_BUSINESS: 'Enter valid Custom Business Category',
    BUSINESS_ADDRESS: 'Enter valid Business Address',
    CITY: 'Enter valid City',
    STATE: 'Enter valid State',
    COUNTRY: 'Enter valid Country',
    PIN_CODE: 'Enter valid Pin Code',
    REGULATORY_LICENSE: 'Enter valid Regulatory License Number'
  }
};

// Common validation functions
export const createNameValidation = (fieldName, isRequired = true, minLength = 2, maxLength = 50) => {
  let validation = Yup.string()
    .min(minLength, VALIDATION_MESSAGES.LENGTH[fieldName] || `Must be between ${minLength} and ${maxLength} characters`)
    .max(maxLength, VALIDATION_MESSAGES.LENGTH[fieldName] || `Must be between ${minLength} and ${maxLength} characters`)
    .matches(VALIDATION_PATTERNS.NAME, VALIDATION_MESSAGES.FORMAT[fieldName] || 'Enter valid name');
  
  if (isRequired) {
    validation = validation.required(VALIDATION_MESSAGES.REQUIRED[fieldName] || 'This field is required');
  }
  
  return validation;
};

export const createEmailValidation = (isRequired = true) => {
  let validation = Yup.string()
    .trim()
    .matches(VALIDATION_PATTERNS.EMAIL, VALIDATION_MESSAGES.FORMAT.EMAIL);

  if (isRequired) {
    validation = validation.required(VALIDATION_MESSAGES.REQUIRED.EMAIL);
  }

  return validation;
};

export const createMobileValidation = (isRequired = true, withCountryCode = false) => {
  let validation = Yup.string();

  if (withCountryCode) {
    // For phone numbers with country code (91XXXXXXXXXX - 12 digits total)
    validation = validation
      .matches(VALIDATION_PATTERNS.INDIAN_MOBILE_WITH_COUNTRY_CODE, 'Enter valid Mobile Number with country code (+91)')
      .length(12, 'Mobile number with country code must be exactly 12 digits');
  } else {
    // For phone numbers without country code (XXXXXXXXXX - 10 digits only)
    validation = validation
      .matches(VALIDATION_PATTERNS.INDIAN_MOBILE, VALIDATION_MESSAGES.FORMAT.MOBILE)
      .length(10, VALIDATION_MESSAGES.LENGTH.MOBILE);
  }

  if (isRequired) {
    validation = validation.required(VALIDATION_MESSAGES.REQUIRED.MOBILE);
  }

  return validation;
};

export const createPasswordValidation = () => {
  return Yup.string()
    .required(VALIDATION_MESSAGES.REQUIRED.PASSWORD)
    .min(8, VALIDATION_MESSAGES.LENGTH.PASSWORD)
    .max(15, VALIDATION_MESSAGES.LENGTH.PASSWORD)
    .matches(VALIDATION_PATTERNS.PASSWORD_UPPERCASE, VALIDATION_MESSAGES.FORMAT.PASSWORD_COMPLEXITY)
    .matches(VALIDATION_PATTERNS.PASSWORD_LOWERCASE, VALIDATION_MESSAGES.FORMAT.PASSWORD_COMPLEXITY)
    .matches(VALIDATION_PATTERNS.PASSWORD_NUMBER, VALIDATION_MESSAGES.FORMAT.PASSWORD_COMPLEXITY)
    .matches(VALIDATION_PATTERNS.PASSWORD_SPECIAL, VALIDATION_MESSAGES.FORMAT.PASSWORD_COMPLEXITY);
};

export const createConfirmPasswordValidation = () => {
  return Yup.string()
    .required(VALIDATION_MESSAGES.REQUIRED.CONFIRM_PASSWORD)
    .oneOf([Yup.ref('password')], VALIDATION_MESSAGES.FORMAT.PASSWORD_MATCH);
};

export const createPANValidation = () => {
  return Yup.string()
    .matches(VALIDATION_PATTERNS.PAN, VALIDATION_MESSAGES.FORMAT.PAN_NUMBER)
    .length(10, VALIDATION_MESSAGES.LENGTH.PAN_NUMBER)
    .required(VALIDATION_MESSAGES.REQUIRED.PAN_NUMBER);
};

export const createAadhaarValidation = () => {
  return Yup.string()
    .required(VALIDATION_MESSAGES.REQUIRED.AADHAAR_NUMBER)
    .matches(VALIDATION_PATTERNS.AADHAAR, VALIDATION_MESSAGES.FORMAT.AADHAAR_NUMBER);
};

export const createDateValidation = (fieldName, isRequired = true, minYear = 1900) => {
  let validation = Yup.date()
    .typeError(VALIDATION_MESSAGES.FORMAT.DOB_INVALID)
    .max(new Date(), VALIDATION_MESSAGES.FORMAT.DOB_FUTURE)
    .min(new Date(minYear, 0, 1), VALIDATION_MESSAGES.FORMAT.DOB_REALISTIC);
  
  if (isRequired) {
    validation = validation.required(VALIDATION_MESSAGES.REQUIRED[fieldName] || 'Date is required');
  }
  
  return validation;
};

// Export all validation schemas for easy import
export * from '../components/ProfileKYC/validationSchemas';
