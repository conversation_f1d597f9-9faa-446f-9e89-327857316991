import AppRouter from "@routes/AppRouter";
import { persistor, store } from "@store/store";
import { QueryClientProvider } from "@tanstack/react-query";
import queryClient from "@utils/queryClient";
import { Toaster } from "react-hot-toast";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { ThemeProvider } from "@contexts/ThemeContext";


function App() {
  return (
    <>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <PersistGate loading={null} persistor={persistor}>
            <ThemeProvider>
              <Toaster
                toastOptions={{
                  duration: 3000,
                  position: "top-center",
                  style: {
                    borderRadius: '8px',
                    fontSize: '14px',
                    fontWeight: '500',
                    padding: '16px',
                  },
                  error: {
                    style: {
                      background: '#dc3545',
                      color: 'white',
                    },
                  },
                }}
              />
              <AppRouter />
            </ThemeProvider>
          </PersistGate>
        </QueryClientProvider>
      </Provider>
    </>
  );
}

export default App;
