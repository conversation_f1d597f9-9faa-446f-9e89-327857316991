import React, { useState } from "react";
import PageHeader from "@components/PageHeader";
import ModernActionPopup from "@components/ModernActionPopup";

const Reports = () => {
  const [showModernPopup, setShowModernPopup] = useState(false);

  return (
    <div className="reports-page">
      <PageHeader
        title="Reports"
        description="View and analyze your logistics data and performance metrics."
      />

      <div className="reports-content p-4">
        <div className="row justify-content-center">
          <div className="col-md-8 text-center">
            <div className="card">
              <div className="card-body py-5">
                <h4 className="card-title mb-3">Reports Dashboard</h4>
                <p className="card-text text-muted mb-4">
                  View and analyze your logistics data and performance metrics.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Action Popup */}
      <ModernActionPopup
        isOpen={showModernPopup}
        onClose={() => setShowModernPopup(false)}
      />
    </div>
  );
};

export default Reports;
