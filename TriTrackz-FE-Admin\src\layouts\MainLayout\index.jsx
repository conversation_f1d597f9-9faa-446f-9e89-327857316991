import React, { useState, useCallback } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { resetUser } from "@store/userSlice";
import { useBootstrapTheme } from "@contexts/BootstrapThemeContext";

import toast from "react-hot-toast";

// Import new components
import NewSidebar from "./Components/NewSidebar/index.jsx";
import NewTopbar from "./Components/NewTopbar/index.jsx";
import RightSidebar from "@components/RightSidebar";
import { RightSidebarProvider } from "@contexts/RightSidebarContext";
import ROUTES from "@constants/routes";
const MainLayout = () => {
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { setDarkTheme } = useBootstrapTheme();

  // Memoize logout handler to prevent unnecessary re-renders
  const handleLogout = useCallback(() => {
    dispatch(resetUser());
    setDarkTheme(); // Reset to dark theme on logout

    toast.success("Logged out successfully");
    navigate(ROUTES.LOGIN);
  }, [dispatch, navigate, setDarkTheme]);

  // Memoize mobile sidebar toggle to prevent unnecessary re-renders
  const toggleMobileSidebar = useCallback(() => {
    setMobileSidebarOpen(!mobileSidebarOpen);
  }, [mobileSidebarOpen]);

  // Memoize mobile sidebar close handler
  const handleMobileSidebarClose = useCallback(() => {
    setMobileSidebarOpen(false);
  }, []);

  return (
    <RightSidebarProvider>
      <div className="d-flex vh-100">
        <NewSidebar
          mobileOpen={mobileSidebarOpen}
          onMobileClose={handleMobileSidebarClose}
        />
        <div className="d-flex flex-column flex-grow-1 overflow-hidden">
          <NewTopbar
            onToggleMobileSidebar={toggleMobileSidebar}
            onLogout={handleLogout}
          />
          <div className="p-3 flex-grow-1 overflow-auto border border-bottom-0 border-end-0  rounded-5 rounded-end-0 rounded-bottom-0">
            <Outlet />
          </div>
        </div>
        <RightSidebar />
      </div>
    </RightSidebarProvider>
  );
};

export default MainLayout;
