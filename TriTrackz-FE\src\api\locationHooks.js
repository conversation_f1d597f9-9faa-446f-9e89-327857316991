import { useMutation } from '@tanstack/react-query';
import { API_ENDPOINTS } from '@constants/apiNamevariables';
import { baseAPI } from './axiosInstance';




export const useLocationByPincode = (options) =>
  useMutation({
    mutationFn: async (zipcode) => {

      const response = await baseAPI.get(`${API_ENDPOINTS.LOCATION_BY_PINCODE}/${zipcode}`, {
        zipcode: zipcode
      });

      return response;
    },
    ...options,
  });


export const useReverseGeocode = (options) =>
  useMutation({
    mutationFn: async ({ latitude, longitude }) => {
      // Use OpenStreetMap Nominatim API for reverse geocoding
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'TriTrackz-App/1.0'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform the response to match our expected format
      const address = data.address || {};

      return {
        success: true,
        data: {
          latitude: parseFloat(data.lat),
          longitude: parseFloat(data.lon),
          address: data.display_name || '',
          area: address.suburb || address.neighbourhood || address.quarter || address.district || address.subdistrict || '',
          city: address.city || address.town || address.village || address.municipality || '',
          state: address.state || address.region || '',
          country: address.country || '',
          pincode: address.postcode || '',
          formattedAddress: data.display_name || ''
        }
      };
    },
    ...options,
  });

export const useForwardGeocode = (options) =>
  useMutation({
    mutationFn: async (searchQuery) => {
      // Use OpenStreetMap Nominatim API for forward geocoding
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery)}&addressdetails=1&limit=5`,
        {
          headers: {
            'User-Agent': 'TriTrackz-App/1.0'
          }
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      // Transform the response to match our expected format
      return {
        success: true,
        data: data.map(item => {
          const address = item.address || {};

          // More comprehensive area extraction
          const area = address.suburb ||
                      address.neighbourhood ||
                      address.quarter ||
                      address.district ||
                      address.subdistrict ||
                      address.hamlet ||
                      address.residential ||
                      address.commercial ||
                      '';

          // Better city extraction
          const city = address.city ||
                      address.town ||
                      address.village ||
                      address.municipality ||
                      address.county ||
                      '';

          // Better state extraction
          const state = address.state ||
                       address.region ||
                       address.province ||
                       address.state_district ||
                       '';

          // Better country extraction
          const country = address.country ||
                         address.country_code?.toUpperCase() ||
                         '';

          // Better pincode extraction
          const pincode = address.postcode ||
                         address.postal_code ||
                         '';

          return {
            latitude: parseFloat(item.lat),
            longitude: parseFloat(item.lon),
            address: item.display_name || '',
            area: area,
            city: city,
            state: state,
            country: country,
            pincode: pincode,
            formattedAddress: item.display_name || '',
            importance: item.importance || 0
          };
        })
      };
    },
    ...options,
  });
