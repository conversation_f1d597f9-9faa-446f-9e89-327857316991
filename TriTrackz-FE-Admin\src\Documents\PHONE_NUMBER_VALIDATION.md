# Phone Number Validation Documentation

## Overview
This document explains the phone number validation and formatting requirements for the TriTrackz application.

## Format Requirements

### Input Format
- **10 digits only**: `9876543210` (without country code)
- **12 digits with country code**: `************` (with 91 country code)
- **Formatted with +**: `+************` (with + prefix)

### Validation Rules
1. **Must start with 6, 7, 8, or 9** (Indian mobile number format)
2. **Exactly 10 digits** after the country code
3. **Country code 91** for India (automatically added if missing)

### API Payload Format
```json
{
  "mobileNumber": "+************",
  "countryCode": "IN"
}
```

## Implementation Details

### Validation Patterns
```javascript
// 10 digits without country code
INDIAN_MOBILE: /^[6-9]\d{9}$/

// 12 digits with country code (91)
INDIAN_MOBILE_WITH_COUNTRY_CODE: /^91[6-9]\d{9}$/
```

### Utility Functions

#### `formatPhoneForAPI(phoneNumber)`
Formats any phone number input for API submission:
- Input: `"9876543210"` → Output: `{mobileNumber: "+************", countryCode: "IN"}`
- Input: `"************"` → Output: `{mobileNumber: "+************", countryCode: "IN"}`

#### `extractTenDigitPhone(phoneNumber)`
Extracts the 10-digit mobile number from any format:
- Input: `"+************"` → Output: `"9876543210"`
- Input: `"91 9876 543210"` → Output: `"9876543210"`

#### `isValidIndianMobile(phoneNumber)`
Validates if the phone number is a valid Indian mobile number:
- Returns `true` for valid numbers starting with 6-9
- Returns `false` for invalid formats

#### `formatPhoneForDisplay(phoneNumber)`
Formats phone number for user-friendly display:
- Input: `"9876543210"` → Output: `"+91 98765 43210"`

### Form Integration

#### PhoneInput Component
- Uses `react-phone-input-2` library
- Automatically handles country code selection
- Validates input in real-time
- Prevents invalid characters and formats

#### Validation Schema
```javascript
phoneNumber: Yup.string()
  .required('Mobile number is required')
  .test('phone-format', 'Enter valid Mobile Number (10 digits starting with 6-9)', function(value) {
    if (!value) return false;
    
    const cleaned = value.replace(/\D/g, '');
    
    // Check if it's 10 digits (without country code) or 12 digits (with 91 country code)
    if (cleaned.length === 10) {
      return /^[6-9]\d{9}$/.test(cleaned);
    } else if (cleaned.length === 12) {
      return /^91[6-9]\d{9}$/.test(cleaned);
    }
    
    return false;
  })
```

## Auto-fill Behavior

### Profile Data Auto-fill
When profile data is loaded from the API:
1. Phone number is automatically formatted for the form component
2. If mobile number exists, the field is disabled
3. Shows "Verified" indicator for existing numbers

### Disabled State
- Mobile number field is disabled when `profileData.phoneNumber` exists
- Shows verification badge
- Prevents modification of verified numbers

## Error Messages

### Validation Errors
- `"Enter valid Mobile Number (10 digits starting with 6-9)"`
- `"Mobile number is required"`
- `"Mobile number must be exactly 10 digits"`

### Success Indicators
- Green checkmark for valid numbers
- "Verified" badge for existing numbers
- Auto-formatting during input

## Testing

### Test Cases
```javascript
// Valid numbers
"9876543210"     // ✅ Valid 10-digit
"7890123456"     // ✅ Valid starting with 7
"8123456789"     // ✅ Valid starting with 8
"6987654321"     // ✅ Valid starting with 6

// Invalid numbers
"5876543210"     // ❌ Starts with 5
"98765432"       // ❌ Too short
"98765432109"    // ❌ Too long
"abcd123456"     // ❌ Contains letters
```

### Manual Testing
You can manually test phone number validation by:
1. Opening the profile/KYC forms
2. Entering various phone number formats
3. Observing validation messages and behavior

## API Integration

### Registration/Login
```javascript
const payload = {
  mobileNumber: "+************",
  countryCode: "IN",
  userType: selectedUserType
};
```

### Profile Update
```javascript
const profileData = {
  basicDetails: {
    phoneNumber: "************", // Format for react-phone-input-2
    // ... other fields
  }
};
```

## Best Practices

1. **Always validate** phone numbers on both client and server side
2. **Use utility functions** for consistent formatting
3. **Handle edge cases** like copy-paste with spaces/dashes
4. **Provide clear feedback** for validation errors
5. **Auto-format** numbers for better UX
6. **Disable verified numbers** to prevent accidental changes

## Troubleshooting

### Common Issues
1. **Number not accepting input**: Check if field is disabled due to existing verification
2. **Validation failing**: Ensure number starts with 6-9 and is exactly 10 digits
3. **API errors**: Verify payload format includes country code
4. **Display issues**: Use `formatPhoneForDisplay()` for consistent formatting

### Debug Tools
```javascript
// Test phone number formatting in browser console
import { formatPhoneForAPI, isValidIndianMobile } from '@utils/profileUtils';

console.log(formatPhoneForAPI("9876543210"));
console.log(isValidIndianMobile("9876543210"));
```

Or directly in the browser console:
```javascript
// Access utility functions from the global scope if available
console.log("Testing phone validation...");
```
