/**
 * Global Autosave Manager
 * Manages autosave callbacks across the application
 */

class AutosaveManager {
  constructor() {
    this.callbacks = new Map();
    this.isInitialized = false;
    this.init();
  }

  init() {
    if (this.isInitialized) return;
    
    // Listen for beforeunload events globally
    window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    
    // Listen for visibility change events
    document.addEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    
    // Listen for page hide events
    window.addEventListener('pagehide', this.handlePageHide.bind(this));
    
    this.isInitialized = true;
  }

  /**
   * Register an autosave callback for a specific component/page
   * @param {string} key - Unique identifier for the callback
   * @param {Function} callback - Function to call for autosave
   */
  register(key, callback) {
    if (typeof callback !== 'function') {

      return;
    }
    
    this.callbacks.set(key, callback);

  }

  /**
   * Unregister an autosave callback
   * @param {string} key - Unique identifier for the callback
   */
  unregister(key) {
    const removed = this.callbacks.delete(key);
    if (removed) {

    }
  }

  /**
   * Execute all registered autosave callbacks
   * @param {string} trigger - What triggered the autosave (beforeunload, logout, etc.)
   */
  executeAll(trigger = 'unknown') {
    const promises = [];

    this.callbacks.forEach((callback, key) => {
      try {
        const result = callback();

        // If callback returns a promise, add it to promises array
        if (result && typeof result.then === 'function') {
          promises.push(result);
        }
      } catch (error) {
        // Silently handle errors
      }
    });

    // Return promise that resolves when all callbacks complete
    return Promise.allSettled(promises);
  }

  /**
   * Execute a specific callback by key
   * @param {string} key - Unique identifier for the callback
   * @param {string} trigger - What triggered the autosave
   */
  execute(key, trigger = 'manual') {
    const callback = this.callbacks.get(key);
    if (callback) {
      try {
        return callback();
      } catch (error) {
        // Silently handle errors
      }
    }
  }

  /**
   * Handle beforeunload event
   */
  handleBeforeUnload() {
    this.executeAll('beforeunload');
  }

  /**
   * Handle visibility change event
   */
  handleVisibilityChange() {
    if (document.visibilityState === 'hidden') {

      // Add small delay to ensure it's not just a quick tab switch
      setTimeout(() => {
        if (document.visibilityState === 'hidden') {
          this.executeAll('visibilitychange');
        }
      }, 100);
    }
  }

  /**
   * Handle page hide event
   */
  handlePageHide() {

    this.executeAll('pagehide');
  }

  /**
   * Get the number of registered callbacks
   */
  getCallbackCount() {
    return this.callbacks.size;
  }

  /**
   * Get all registered callback keys
   */
  getRegisteredKeys() {
    return Array.from(this.callbacks.keys());
  }

  /**
   * Clear all callbacks
   */
  clear() {
    this.callbacks.clear();
  }

  /**
   * Destroy the manager and remove event listeners
   */
  destroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    document.removeEventListener('visibilitychange', this.handleVisibilityChange.bind(this));
    window.removeEventListener('pagehide', this.handlePageHide.bind(this));

    this.clear();
    this.isInitialized = false;
  }
}

// Create singleton instance
const autosaveManager = new AutosaveManager();

export default autosaveManager;

/**
 * Hook for easy integration with React components
 * Note: This hook should be imported and used in React components
 * @param {string} key - Unique identifier for the component
 * @param {Function} callback - Autosave callback function
 * @param {Array} deps - Dependencies array (like useEffect)
 */
export const createAutosaveManagerHook = () => {
  // This will be used in React components that import React hooks
  return (key, callback, deps = []) => {
    // React hooks will be available in the component context
    // This is just a factory function
    return {
      register: () => autosaveManager.register(key, callback),
      unregister: () => autosaveManager.unregister(key),
      executeNow: () => autosaveManager.execute(key, 'manual'),
      executeAll: () => autosaveManager.executeAll('manual')
    };
  };
};

/**
 * Function to trigger autosave before logout
 * This should be called from logout functions
 */
export const triggerAutosaveBeforeLogout = async () => {
  try {
    await autosaveManager.executeAll('logout');
  } catch (error) {
    // Silent error handling
  }
};
