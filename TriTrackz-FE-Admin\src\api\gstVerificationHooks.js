import { useMutation } from '@tanstack/react-query';
import { API_ENDPOINTS } from '@constants/apiNamevariables';
import { baseAPI } from './axiosInstance';

// GST verification
export const useGstVerification = (options) =>
  useMutation({
    mutationFn: (data) =>
      baseAPI.post(API_ENDPOINTS.VERIFY_GST, {
        gstin: data.gstin,
        consent: data.consent || "Y",
        reason: data.reason || "KYC onboarding"
      }),
    ...options,
  });
