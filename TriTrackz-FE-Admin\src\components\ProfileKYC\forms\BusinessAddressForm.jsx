import React, { useCallback, useState, useRef } from "react";
import { Field, ErrorMessage } from "formik";
import toast from "react-hot-toast";
import {
  useLocationByPincode,
  useReverseGeocode,
  useForwardGeocode,
} from "@api/locationHooks";
import LocationMap from "@components/common/LocationMap";

const BusinessAddressForm = ({
  formik,
  existingDocuments,
  onDocumentChange,
}) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  // State to store location coordinates for map display
  const [locationCoords, setLocationCoords] = useState({
    latitude: null,
    longitude: null,
  });

  // Search-related state
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [showSearchResults, setShowSearchResults] = useState(false);
  const searchTimeoutRef = useRef(null);

  // React Query mutation hook for forward geocoding (address search)
  const searchMutation = useForwardGeocode({
    onSuccess: (data) => {
      if (data.success && data.data) {
        setSearchResults(data.data);
        setShowSearchResults(true);
      } else {
        setSearchResults([]);
        setShowSearchResults(false);
        toast.error("No locations found for your search");
      }
    },
    onError: (error) => {
      setSearchResults([]);
      setShowSearchResults(false);
      toast.error("Failed to search locations. Please try again.");
    },
  });

  // React Query mutation hook for reverse geocoding (GPS coordinates to address)
  const reverseGeocodeMutation = useReverseGeocode({
    onSuccess: (locationData) => {
      if (locationData && locationData.success) {
        // Update form fields with reverse geocoded data
        const updates = {};

        if (locationData.data?.city) {
          updates["businessAddress.city"] = locationData.data.city;
        }

        if (locationData.data?.state) {
          updates["businessAddress.state"] = locationData.data.state;
        }

        if (locationData.data?.country) {
          updates["businessAddress.country"] = locationData.data.country;
        }

        if (locationData.data?.pincode) {
          updates["businessAddress.pincode"] = locationData.data.pincode;
        }

        // Apply all updates at once
        Object.entries(updates).forEach(([field, value]) => {
          formik.setFieldValue(field, value);
        });

        if (Object.keys(updates).length > 0) {
          toast.success("Address details filled from GPS location");
        }
      } else {
        toast.error("Unable to get address details for this location");
      }
    },
    onError: (error) => {
      toast.error("Failed to get address from location");
    },
  });

  // React Query mutation hook for location lookup
  const locationMutation = useLocationByPincode({
    onSuccess: (locationData) => {
      if (locationData && locationData.success) {
        // Update form fields with API response
        const updates = {};

        if (locationData.data?.city) {
          updates["businessAddress.city"] = locationData.data.city;
        }

        if (locationData.data?.state) {
          updates["businessAddress.state"] = locationData.data.state;
        }

        if (locationData.data?.country) {
          updates["businessAddress.country"] = locationData.data.country;
        }

        // Apply all updates at once
        Object.entries(updates).forEach(([field, value]) => {
          formik.setFieldValue(field, value);
        });

        // Update location coordinates for map display
        if (locationData.data?.latitude && locationData.data?.longitude) {
          setLocationCoords({
            latitude: parseFloat(locationData.data.latitude),
            longitude: parseFloat(locationData.data.longitude),
          });
        }

        if (Object.keys(updates).length > 0) {
          toast.success("Location details updated automatically");
        }
      } else {
        toast.error("Unable to fetch location details for this pincode");
      }
    },
    onError: (error) => {
      toast.error("Failed to fetch location details");
    },
  });

  // Handle search input change with debounced search
  const handleSearchInputChange = (e) => {
    const value = e.target.value;
    setSearchQuery(value);

    // Auto-search when user types 3+ characters
    if (value.trim().length >= 3) {
      // Debounce the search to avoid too many API calls
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      searchTimeoutRef.current = setTimeout(() => {
        searchMutation.mutate(value.trim());
      }, 500);
    } else {
      setShowSearchResults(false);
      setSearchResults([]);
    }
  };

  // Handle search input blur
  const handleSearchInputBlur = () => {
    // Delay hiding results to allow for clicks
    setTimeout(() => {
      setShowSearchResults(false);
    }, 200);
  };

  // Handle search result selection
  const handleSearchResultSelect = (result) => {
    setSearchQuery(result.formattedAddress);
    setShowSearchResults(false);

    // Update coordinates and trigger location select
    setLocationCoords({
      latitude: result.latitude,
      longitude: result.longitude,
    });
    handleLocationSelect(result.latitude, result.longitude, result);

    toast.success("Location selected from search");
  };

  // Handle GPS location selection from map
  const handleLocationSelect = useCallback(
    (latitude, longitude, searchResultData = null) => {
      // Update coordinates state
      setLocationCoords({ latitude, longitude });

      // If we have search result data (from address search), use it directly
      if (searchResultData) {
        console.log("Search result data:", searchResultData); // Debug log

        const updates = {};

        if (searchResultData.city) {
          updates["businessAddress.city"] = searchResultData.city;
        }

        if (searchResultData.state) {
          updates["businessAddress.state"] = searchResultData.state;
        }

        if (searchResultData.country) {
          updates["businessAddress.country"] = searchResultData.country;
        }

        if (searchResultData.pincode) {
          updates["businessAddress.pincode"] = searchResultData.pincode;
        }

        console.log("Form updates:", updates); // Debug log

        // Update all fields at once
        Object.keys(updates).forEach((key) => {
          formik.setFieldValue(key, updates[key]);
        });

        toast.success("Address details filled from search");
      } else {
        // Call reverse geocoding to get address details (for GPS/map click)
        reverseGeocodeMutation.mutate({ latitude, longitude });
      }
    },
    [reverseGeocodeMutation, formik]
  );

  // Handle pincode change and API call
  const handlePincodeChange = useCallback(
    async (event) => {
      const pincode = event.target.value;

      // Update formik value first
      formik.setFieldValue("businessAddress.pincode", pincode);

      // Only make API call if pincode is 6 digits
      if (pincode && pincode.length === 6 && /^\d{6}$/.test(pincode)) {
        locationMutation.mutate(pincode);
      }
    },
    [formik, locationMutation]
  );

  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">Business Address</h3>
        <p className="text-muted mb-0">
          Use the interactive map to select your location or fill in the address
          details manually
        </p>
      </div>

      <div className="row g-4">
        {/* Map Section */}
        <div className="col-lg-6">
          <div className="border rounded p-3 bg-light">
            <LocationMap
              latitude={locationCoords.latitude}
              longitude={locationCoords.longitude}
              onLocationSelect={handleLocationSelect}
              height="520px"
              enableGPS={true}
              enableMapClick={true}
            />
          </div>
        </div>

        {/* Form Fields Section */}
        <div className="col-lg-6">
          <div>
            {/* Address Search Field */}
            <div className="mb-3">
              <label className="form-label fw-medium">Search Address</label>
              <div className="position-relative">
                <input
                  type="text"
                  className="form-control"
                  placeholder="Search for an address, city, or landmark..."
                  value={searchQuery}
                  onChange={handleSearchInputChange}
                  onBlur={handleSearchInputBlur}
                  onFocus={() =>
                    searchResults.length > 0 && setShowSearchResults(true)
                  }
                  autoComplete="off"
                />
                {searchMutation.isPending ? (
                  <span
                    className="spinner-border spinner-border-sm position-absolute top-50 end-0 translate-middle-y me-2 text-primary"
                    role="status"
                    aria-hidden="true"
                  ></span>
                ) : (
                  <i className="fas fa-search position-absolute top-50 end-0 translate-middle-y me-2 text-muted"></i>
                )}

                {/* Search Results Dropdown */}
                {showSearchResults && searchResults.length > 0 && (
                  <div
                    className="position-absolute w-100 bg-white border rounded-bottom shadow-lg"
                    style={{
                      zIndex: 1100,
                      maxHeight: "250px",
                      overflowY: "auto",
                      top: "100%",
                      left: 0,
                      right: 0,
                      marginTop: "1px",
                    }}
                  >
                    {searchResults.map((result, index) => (
                      <div
                        key={index}
                        className="p-3 border-bottom"
                        onClick={() => handleSearchResultSelect(result)}
                        style={{
                          cursor: "pointer",
                          transition: "background-color 0.2s ease",
                        }}
                        onMouseEnter={(e) =>
                          (e.target.style.backgroundColor = "#f8f9fa")
                        }
                        onMouseLeave={(e) =>
                          (e.target.style.backgroundColor = "white")
                        }
                      >
                        <div className="fw-bold text-primary mb-1">
                          <i className="fas fa-map-marker-alt me-2"></i>
                          {result.city || "Unknown Location"}
                        </div>
                        <small className="text-muted d-block text-truncate">
                          {result.formattedAddress}
                        </small>
                      </div>
                    ))}
                  </div>
                )}

                {/* Loading State */}
                {searchMutation.isPending && searchQuery.trim().length >= 3 && (
                  <div
                    className="position-absolute w-100 bg-white border rounded-bottom shadow-lg"
                    style={{
                      zIndex: 1100,
                      top: "100%",
                      left: 0,
                      right: 0,
                      marginTop: "1px",
                    }}
                  >
                    <div className="p-3 text-center text-muted">
                      <span
                        className="spinner-border spinner-border-sm"
                        role="status"
                        aria-hidden="true"
                      ></span>
                      <span className="ms-2">Searching...</span>
                    </div>
                  </div>
                )}

                {/* No Results Message */}
                {showSearchResults &&
                  searchResults.length === 0 &&
                  searchMutation.isPending === false &&
                  searchQuery.trim().length >= 3 && (
                    <div
                      className="position-absolute w-100 bg-white border rounded-bottom shadow-lg"
                      style={{
                        zIndex: 1100,
                        top: "100%",
                        left: 0,
                        right: 0,
                        marginTop: "1px",
                      }}
                    >
                      <div className="p-3 text-center text-muted">
                        <i className="fas fa-search me-2"></i>
                        No locations found
                      </div>
                    </div>
                  )}
              </div>
              <small className="form-text text-muted">
                Search for your business address to auto-fill location details
              </small>
            </div>

            {/* Complete Address Field */}
            <div className="mb-3">
              <label
                htmlFor="businessAddress.address"
                className="form-label fw-medium"
              >
                Complete Address <span className="text-danger">*</span>
              </label>
              <Field
                as="textarea"
                name="businessAddress.address"
                rows="3"
                className={`form-control ${
                  formik.touched.businessAddress?.address &&
                  formik.errors.businessAddress?.address
                    ? "is-invalid"
                    : formik.touched.businessAddress?.address &&
                      formik.values.businessAddress?.address
                    ? "is-valid"
                    : ""
                }`}
                placeholder="Enter complete address (building, street, area, etc.)"
              />
              <ErrorMessage
                name="businessAddress.address"
                component="div"
                className="invalid-feedback"
              />
              <small className="form-text text-muted">
                Provide detailed address including building number, street name,
                and landmarks
              </small>
            </div>

            <div className="row">
              <div className="col-md-6 mb-3">
                <label
                  htmlFor="businessAddress.pincode"
                  className="form-label fw-medium"
                >
                  Pincode <span className="text-danger">*</span>
                  {locationMutation.isPending && (
                    <span className="ms-2">
                      <span
                        className="spinner-border spinner-border-sm"
                        role="status"
                        aria-hidden="true"
                      ></span>
                      <span className="ms-1 text-muted">Loading...</span>
                    </span>
                  )}
                </label>
                <Field
                  type="text"
                  name="businessAddress.pincode"
                  onChange={handlePincodeChange}
                  className={`form-control ${
                    formik.touched.businessAddress?.pincode &&
                    formik.errors.businessAddress?.pincode
                      ? "is-invalid"
                      : formik.touched.businessAddress?.pincode &&
                        formik.values.businessAddress?.pincode
                      ? "is-valid"
                      : ""
                  }`}
                  placeholder="Enter 6-digit pincode"
                  maxLength="6"
                />
                <ErrorMessage
                  name="businessAddress.pincode"
                  component="div"
                  className="invalid-feedback"
                />
                <small className="form-text text-muted">
                  City, state, and country will be auto-filled when you enter a
                  valid pincode
                </small>
              </div>

              <div className="col-md-6 mb-3">
                <label
                  htmlFor="businessAddress.country"
                  className="form-label fw-medium"
                >
                  Country <span className="text-danger">*</span>
                </label>
                <Field
                  type="text"
                  name="businessAddress.country"
                  className={`form-control ${
                    formik.touched.businessAddress?.country &&
                    formik.errors.businessAddress?.country
                      ? "is-invalid"
                      : formik.touched.businessAddress?.country &&
                        formik.values.businessAddress?.country
                      ? "is-valid"
                      : ""
                  }`}
                  placeholder="Enter country or use pincode to auto-fill"
                />
                <ErrorMessage
                  name="businessAddress.country"
                  component="div"
                  className="invalid-feedback"
                />
              </div>

              <div className="col-md-6 mb-3">
                <label
                  htmlFor="businessAddress.city"
                  className="form-label fw-medium"
                >
                  City <span className="text-danger">*</span>
                </label>
                <Field
                  type="text"
                  name="businessAddress.city"
                  className={`form-control ${
                    formik.touched.businessAddress?.city &&
                    formik.errors.businessAddress?.city
                      ? "is-invalid"
                      : formik.touched.businessAddress?.city &&
                        formik.values.businessAddress?.city
                      ? "is-valid"
                      : ""
                  }`}
                  placeholder="Enter city or use pincode to auto-fill"
                />
                <ErrorMessage
                  name="businessAddress.city"
                  component="div"
                  className="invalid-feedback"
                />
              </div>

              <div className="col-md-6 mb-3">
                <label
                  htmlFor="businessAddress.state"
                  className="form-label fw-medium"
                >
                  State <span className="text-danger">*</span>
                </label>
                <Field
                  type="text"
                  name="businessAddress.state"
                  className={`form-control ${
                    formik.touched.businessAddress?.state &&
                    formik.errors.businessAddress?.state
                      ? "is-invalid"
                      : formik.touched.businessAddress?.state &&
                        formik.values.businessAddress?.state
                      ? "is-valid"
                      : ""
                  }`}
                  placeholder="Enter state or use pincode to auto-fill"
                />
                <ErrorMessage
                  name="businessAddress.state"
                  component="div"
                  className="invalid-feedback"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BusinessAddressForm;
