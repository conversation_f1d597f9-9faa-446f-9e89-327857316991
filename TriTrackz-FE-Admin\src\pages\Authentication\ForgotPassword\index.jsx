import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FaEnvelope, FaArrowLeft } from "react-icons/fa";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import { useForgotPassword } from "@api/authHooks";

const ForgotPassword = () => {
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Forgot password mutation
  const forgotPasswordMutation = useForgotPassword({
    onSuccess: (response) => {
      console.log("Forgot password response:", response);
      toast.success("Password reset link sent to your email!");
      setIsSubmitted(true);
    },
    onError: (error) => {
      console.error("Forgot password error:", error);
      toast.error(
        error?.response?.data?.message ||
          "Failed to send reset link. Please try again."
      );
    },
  });

  // Validation schema
  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Please enter a valid email address")
      .required("Email is required"),
  });

  // Formik setup
  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema,
    onSubmit: (values) => {
      console.log("Forgot password request:", values);
      forgotPasswordMutation.mutate({
        email: values.email.trim().toLowerCase(),
      });
    },
  });

  if (isSubmitted) {
    return (
      <div className="auth-form">
        {/* Success Header */}
        <div className="auth-welcome-text mb-4">
          <h2 className="fw-bold text-white">Check Your Email</h2>
          <p className="text-light">We've sent you a password reset link</p>
        </div>

        {/* Success Content */}
        <div className="text-center mb-4">
          <div
            className="bg-success bg-opacity-20 rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
            style={{ width: "80px", height: "80px" }}
          >
            <FaEnvelope className="text-white" size={32} />
          </div>
          <p className="text-light mb-4">
            We've sent a password reset link to your email address. Please check
            your inbox and follow the instructions to reset your password.
          </p>
        </div>

        {/* Back to Login Button */}
        <Link
          to={ROUTES.LOGIN}
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn mb-4"
        >
          <FaArrowLeft className="me-2" />
          Back to Login
        </Link>

        {/* Try Again Link */}
        <div className="text-center">
          <p className="text-light small mb-2">
            Didn't receive the email? Check your spam folder or
          </p>
          <button
            className="btn btn-link auth-link-btn text-decoration-none"
            onClick={() => setIsSubmitted(false)}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="auth-form">
      {/* Header */}
      <div className="auth-welcome-text mb-4">
        <h2 className="fw-bold text-white">Forgot Password?</h2>
        <p className="text-light">Enter your email to reset your password</p>
      </div>

      {/* Form */}
      <form onSubmit={formik.handleSubmit}>
        {/* Email Field */}
        <div className="mb-4">
          <label
            htmlFor="email"
            className="form-label fw-semibold mb-2 text-light"
          >
            Email Address
          </label>
          <input
            type="email"
            className={`form-control auth-input ${
              formik.touched.email && formik.errors.email ? "is-invalid" : ""
            }`}
            id="email"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder="Enter your email address"
            autoComplete="email"
          />
          {formik.touched.email && formik.errors.email && (
            <div className="invalid-feedback">{formik.errors.email}</div>
          )}
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
          disabled={forgotPasswordMutation.isPending || !formik.isValid}
        >
          {forgotPasswordMutation.isPending ? (
            <>
              <span className="spinner-border spinner-border-sm me-2" />
              Sending Reset Link...
            </>
          ) : (
            "Send Reset Link"
          )}
        </button>

        {/* Back to Login Link */}
        <div className="text-center mt-4">
          <Link
            to={ROUTES.LOGIN}
            className="btn btn-link auth-link-btn text-decoration-none"
          >
            <FaArrowLeft className="me-2" />
            Back to Login
          </Link>
        </div>
      </form>
    </div>
  );
};

export default ForgotPassword;
