import React, { useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FaEye, FaEyeSlash, FaArrowLeft } from "react-icons/fa";
import toast from "react-hot-toast";
import { showToast } from "@utils/toastUtils";
import ROUTES from "@constants/routes";
import { useMobileLogin } from "@api/authHooks";
import { setUser, setToken, setProfileStatus, setPhoneVerified } from "@store/userSlice";

const PasswordLogin = () => {
  const [showPassword, setShowPassword] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {
    phoneNumber,
    selectedUserType
  } = useSelector((state) => state.user.authData);

  // Validation
  React.useEffect(() => {
    // If no phone number or user type, redirect back to login
    if (!phoneNumber || !selectedUserType) {
      toast.error('Please complete the login process from the beginning');
      navigate(ROUTES.LOGIN);
    }
  }, [phoneNumber, selectedUserType, navigate]);

  // Mobile login mutation
  const mobileLoginMutation = useMobileLogin({
    onSuccess: (response) => {

      // Handle response similar to OTP verification
      if (response.accessToken || response.token) {
        dispatch(setPhoneVerified(true));
        dispatch(setToken(response.accessToken || response.token));

        // Check if current userType exists in otherUserTypes array (same logic as OTP)
        const currentUserTypeExists = response.otherUserTypes &&
          Array.isArray(response.otherUserTypes) &&
          response.otherUserTypes.includes(selectedUserType);

        // Additional check: if userType and otherUserTypes contain the same value
        const userTypeMatchesOtherTypes = response.userType === selectedUserType &&
          response.otherUserTypes &&
          Array.isArray(response.otherUserTypes) &&
          response.otherUserTypes.includes(response.userType);



        if (currentUserTypeExists || userTypeMatchesOtherTypes) {

          const userData = {
            id: response.userId || response.user?.id,
            firstName: response.firstName || response.user?.firstName,
            lastName: response.lastName || response.user?.lastName,
            email: response.email || response.user?.email,
            mobileNumber: response.mobileNumber || response.user?.mobileNumber,
            userType: selectedUserType, // Use the selected user type
            phoneNumber: phoneNumber,
            // Additional fields from response
            username: response.username || response.user?.username,
            accessToken: response.accessToken || response.token,
            refreshToken: response.refreshToken
          };

          dispatch(setUser(userData));
          dispatch(setToken(response.accessToken || response.token));

          // Set profile status as complete since user has existing data
          dispatch(setProfileStatus({
            isProfileCompleted: true,
            isKYCCompleted: true,
            profileCompletionPercentage: 100
          }));

          showToast.success(`Welcome back ${userData.firstName}! Logged in as ${getUserTypeLabel(selectedUserType)}`);
          navigate(ROUTES.DASHBOARD);
        } else {
          toast.error(`You don't have access as ${getUserTypeLabel(selectedUserType)}. Please register for this user type.`);
        }
      } else {
        toast.error('Login failed - invalid response format');
      }
    },
    onError: (error) => {
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          "Login failed. Please check your credentials.";
      toast.error(errorMessage);
    }
  });

  // Helper function to get user type label
  const getUserTypeLabel = (userTypeId) => {
    const userTypes = {
      2: "Transport Company",
      3: "Broker",
      4: "Carrier",
      5: "Driver",
      6: "Shipper Company", // Legacy mapping - should be cleaned up in database
      7: "Shipper Company",
      8: "Shipper Individual"
    };
    return userTypes[userTypeId] || userTypeId;
  };

  // Password strength calculation
  const calculatePasswordStrength = (password) => {
    if (!password) return { strength: 0, label: "", color: "" };

    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };

    // Calculate score
    if (checks.length) score += 20;
    if (checks.lowercase) score += 20;
    if (checks.uppercase) score += 20;
    if (checks.numbers) score += 20;
    if (checks.special) score += 20;

    // Determine strength level
    if (score < 40) {
      return { strength: score, label: "Weak", color: "#dc3545" }; // Red
    } else if (score < 60) {
      return { strength: score, label: "Fair", color: "#fd7e14" }; // Orange
    } else if (score < 80) {
      return { strength: score, label: "Good", color: "#ffc107" }; // Yellow
    } else {
      return { strength: score, label: "Strong", color: "#198754" }; // Green
    }
  };

 

  // Password validation schema
  const passwordValidationSchema = Yup.object({
    password: Yup.string()
      .min(6, "Password must be at least 6 characters")
      .required("Password is required"),
  });

  const passwordFormik = useFormik({
    initialValues: {
      password: "",
    },
    validationSchema: passwordValidationSchema,
    onSubmit: (values) => {
      // Ensure phone number is in correct format
      let formattedPhoneNumber = phoneNumber;
      if (phoneNumber && !phoneNumber.startsWith('+')) {
        formattedPhoneNumber = `+${phoneNumber}`;
      }

      // Send password as plain text for mobile login
      const loginPayload = {
        mobileNumber: formattedPhoneNumber,
        password: values.password, // Plain text password
        userType: selectedUserType,
        isAdmin: false,
        deviceInfo: "web-browser"
      };

      mobileLoginMutation.mutate(loginPayload);
    },
  });

  // Format phone number for display
  const maskPhoneNumber = (phone) => {
    if (!phone) return "";
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length >= 10) {
      return `${cleaned.slice(0, -10)}****${cleaned.slice(-4)}`;
    }
    return phone;
  };
 const passwordStrength = calculatePasswordStrength(passwordFormik.values.password);
  return (
    <div className="auth-form">
      {/* Header */}
      <div className="auth-welcome-text mb-4">
        <h2 className="fw-bold" style={{ color: "var(--text-primary)" }}>
          Welcome Back
        </h2>
        <p style={{ color: "var(--text-secondary)" }}>
          Enter your password to continue
        </p>
      </div>

      {/* User Info Display */}
      <div className="selected-role-simple mb-4 mt-5">
        <div className="d-flex justify-content-between align-items-center">
          <div className="d-flex align-items-center gap-3">
            <div className="role-info-icon">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="m9,12 2,2 4,-4"/>
              </svg>
            </div>
            <div>
              <small className="role-label-simple">LOGGING IN AS</small>
              <div className="role-value-simple">{getUserTypeLabel(selectedUserType)}</div>
            </div>
          </div>
          <button
            type="button"
            className="change-btn-simple"
            onClick={() => navigate(ROUTES.LOGIN)}
          >
            Change
          </button>
        </div>
      </div>

      {/* Phone Number Display */}
      <div className="mb-3 p-3 border rounded" style={{ borderColor: "var(--border-secondary)" }}>
        <small style={{ color: "var(--text-secondary)" }}>
          Mobile: <strong style={{ color: "var(--text-primary)" }}>{maskPhoneNumber(phoneNumber)}</strong>
        </small>
      </div>

      {/* Password Form */}
      <form onSubmit={passwordFormik.handleSubmit}>
        <div className="mb-4">
          <div className="position-relative">
            <input
              type={showPassword ? "text" : "password"}
              className={`form-control ${passwordFormik.touched.password && passwordFormik.errors.password ? 'is-invalid' : ''}`}
              name="password"
              value={passwordFormik.values.password}
              onChange={passwordFormik.handleChange}
              onBlur={passwordFormik.handleBlur}
              placeholder="Enter your password"
              style={{
                borderRadius: "12px",
                padding: "1rem 3rem 1rem 1rem",
                fontSize: "1rem",
                border: "2px solid var(--border-primary)",
                backgroundColor: "var(--surface-primary)"
              }}
            />
            <button
              type="button"
              className="btn position-absolute"
              style={{
                right: "10px",
                top: "50%",
                transform: "translateY(-50%)",
                border: "none",
                background: "transparent",
                color: "#6c757d",
                padding: "0.25rem",
                width: "32px",
                height: "32px",
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: "4px",
                transition: "all 0.2s ease"
              }}
              onMouseEnter={(e) => {
                e.target.style.color = "#495057";
                e.target.style.backgroundColor = "rgba(108, 117, 125, 0.1)";
              }}
              onMouseLeave={(e) => {
                e.target.style.color = "#6c757d";
                e.target.style.backgroundColor = "transparent";
              }}
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
            </button>
          </div>

          {/* Password Strength Indicator */}
          {passwordFormik.values.password && (
            <div className="mt-2">
              <div className="d-flex justify-content-between align-items-center mb-1">
                <small style={{ color: "var(--text-secondary)", fontSize: "0.75rem" }}>
                  Password Strength
                </small>
                <small style={{
                  color: passwordStrength.color,
                  fontSize: "0.75rem",
                  fontWeight: "600"
                }}>
                  {passwordStrength.label}
                </small>
              </div>
              <div
                className="progress"
                style={{
                  height: "4px",
                  backgroundColor: "var(--border-secondary)",
                  borderRadius: "2px"
                }}
              >
                <div
                  className="progress-bar"
                  role="progressbar"
                  style={{
                    width: `${passwordStrength.strength}%`,
                    backgroundColor: passwordStrength.color,
                    transition: "all 0.3s ease",
                    borderRadius: "2px"
                  }}
                  aria-valuenow={passwordStrength.strength}
                  aria-valuemin="0"
                  aria-valuemax="100"
                ></div>
              </div>

              {/* Password Requirements */}
              {passwordStrength.strength < 100 && (
                <div className="mt-2">
                  <small style={{ color: "var(--text-secondary)", fontSize: "0.7rem" }}>
                    Password should contain: uppercase, lowercase, numbers, and special characters
                  </small>
                </div>
              )}
            </div>
          )}

          {passwordFormik.touched.password && passwordFormik.errors.password && (
            <div className="invalid-feedback d-block mt-2">
              <small>{passwordFormik.errors.password}</small>
            </div>
          )}
        </div>

        {/* Forgot Password Link */}
        <div className="text-end mb-4">
          <button
            type="button"
            className="btn btn-link p-0 auth-link-btn"
            style={{
              color: "var(--triadic-red-600)",
              fontSize: "0.875rem",
              fontWeight: "500",
              textDecoration: "none"
            }}
            onClick={() => {
              toast.info("Forgot password feature coming soon!");
            }}
          >
            Forgot Password?
          </button>
        </div>

        {/* Login Button */}
        <button
          type="submit"
          className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
          disabled={!passwordFormik.isValid || !passwordFormik.values.password || mobileLoginMutation.isPending}
          style={{
            borderRadius: "12px",
            fontSize: "1.1rem",
            background: "var(--triadic-red-50)",
            border: "none",
            color: "var(--triadic-red-900)",
            boxShadow: "0 4px 15px rgba(229, 191, 193, 0.3)"
          }}
        >
          {mobileLoginMutation.isPending ? "Logging in..." : "Login"}
        </button>

        {/* Back to OTP Option */}
        <div className="text-center mt-4">
          <button
            type="button"
            className="btn btn-link auth-link-btn"
            style={{
              color: "var(--text-secondary)",
              fontSize: "0.875rem",
              fontWeight: "500",
              textDecoration: "none"
            }}
            onClick={() => navigate(ROUTES.LOGIN)}
          >
            <FaArrowLeft className="me-2" />
            Back to login options
          </button>
        </div>
      </form>
    </div>
  );
};

export default PasswordLogin;
