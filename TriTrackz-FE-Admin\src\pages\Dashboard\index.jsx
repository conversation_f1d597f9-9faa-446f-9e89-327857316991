import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import PageHeader from "@components/PageHeader";

const Dashboard = () => {
  const [showModernPopup, setShowModernPopup] = useState(false);
  const navigate = useNavigate();

  return (
    <div className="container-fluid">
      <PageHeader
        title="Dashboard"
        description="Welcome to your logistics management system."
      />

      <div className="p-4">
        <div className="row justify-content-center">
          <div className="col-md-8 text-center">
            <div className="card bg-dark border-secondary">
              <div className="card-body py-5">
                <h4 className="card-title mb-3 text-white">
                  Ready to Get Started
                </h4>
                <p className="card-text text-light mb-4">
                  Your dashboard is ready for customization. Add your content
                  and features here.
                </p>
                <div className="d-flex gap-3 justify-content-center">
                  <button className="btn btn-warning px-4">Get Started</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modern Action Popup */}
    </div>
  );
};

export default Dashboard;
