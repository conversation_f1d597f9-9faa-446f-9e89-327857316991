// User Types
export const USER_TYPES = {
  TRANSPORT_COMPANY: 2,
  BROKER: 3,
  CARRIER: 4,
  DRIVER: 5,
  SHIPPER_COMPANY: 7,
  SHIPPER_INDIVIDUAL: 8
};

// Profile Status Values
export const PROFILE_STATUS = {
  INCOMPLETE: 0,
  COMPLETE: 1,
  UNDER_REVIEW: 2,
  APPROVED: 3,
  REJECTED: 4
};

// Profile Status Labels
export const PROFILE_STATUS_LABELS = {
  [PROFILE_STATUS.INCOMPLETE]: "Incomplete",
  [PROFILE_STATUS.COMPLETE]: "Complete",
  [PROFILE_STATUS.UNDER_REVIEW]: "Under Review",
  [PROFILE_STATUS.APPROVED]: "Approved",
  [PROFILE_STATUS.REJECTED]: "Rejected"
};

// User Type Labels
export const USER_TYPE_LABELS = {
  [USER_TYPES.TRANSPORT_COMPANY]: "Transport Company",
  [USER_TYPES.BROKER]: "Broker",
  [USER_TYPES.CARRIER]: "Carrier",
  [USER_TYPES.DRIVER]: "Driver",
  [USER_TYPES.SHIPPER_COMPANY]: "Shipper Company",
  [USER_TYPES.SHIPPER_INDIVIDUAL]: "Shipper Individual"
};

// Gender options for Aadhaar
export const GENDER_OPTIONS = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" },
  { value: "Other", label: "Other" }
];

// Industry types for Shipper Company
export const INDUSTRY_TYPES = [
  { value: "Manufacturing", label: "Manufacturing" },
  { value: "Retail", label: "Retail" },
  { value: "FMCG", label: "FMCG" },
  { value: "Pharma", label: "Pharma" },
  { value: "Auto", label: "Auto" },
  { value: "Others", label: "Others" }
];

// Business categories for Shipper Company
export const BUSINESS_CATEGORIES = [
  { value: "Manufacturer", label: "Manufacturer" },
  { value: "Trader", label: "Trader" },
  { value: "Distributor", label: "Distributor" },
  { value: "Retailer", label: "Retailer" },
  { value: "Others", label: "Others" }
];