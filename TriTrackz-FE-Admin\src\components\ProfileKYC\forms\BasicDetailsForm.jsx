import React from "react";
import { Field, ErrorMessage } from "formik";
import { useSelector } from "react-redux";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { shouldDisableMobileNumber } from "@utils/profileUtils";

const BasicDetailsForm = ({
  formik,
  userType,
  existingDocuments,
  onDocumentChange,
  isEditMode = false,
}) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  // Get profile data from Redux to check if mobile should be disabled
  const { profileData } = useSelector((state) => state.user);

  // In admin panel edit mode, always disable phone number editing
  // In regular mode, use the shouldDisableMobileNumber logic
  const isMobileDisabled = isEditMode || shouldDisableMobileNumber(profileData);
  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">Basic Details</h3>
        <p className="text-muted mb-0">Please provide your basic information</p>
      </div>

      <div className="row">
        <div className="col-md-6 mb-3">
          <label
            htmlFor="basicDetails.firstName"
            className="form-label fw-medium"
          >
            First Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="basicDetails.firstName"
            className={`form-control ${
              formik.touched.basicDetails?.firstName &&
              formik.errors.basicDetails?.firstName
                ? "is-invalid"
                : formik.touched.basicDetails?.firstName &&
                  formik.values.basicDetails?.firstName
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter first name"
          />
          <ErrorMessage
            name="basicDetails.firstName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="basicDetails.lastName"
            className="form-label fw-medium"
          >
            Last Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="basicDetails.lastName"
            className={`form-control ${
              formik.touched.basicDetails?.lastName &&
              formik.errors.basicDetails?.lastName
                ? "is-invalid"
                : formik.touched.basicDetails?.lastName &&
                  formik.values.basicDetails?.lastName
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter last name"
          />
          <ErrorMessage
            name="basicDetails.lastName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label htmlFor="basicDetails.email" className="form-label fw-medium">
            Email <span className="text-danger">*</span>
          </label>
          <Field
            type="email"
            name="basicDetails.email"
            className={`form-control ${
              formik.touched.basicDetails?.email &&
              formik.errors.basicDetails?.email
                ? "is-invalid"
                : formik.touched.basicDetails?.email &&
                  formik.values.basicDetails?.email
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter email address"
          />
          <ErrorMessage
            name="basicDetails.email"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="basicDetails.phoneNumber"
            className="form-label fw-medium"
          >
            Phone Number <span className="text-danger">*</span>
            {isMobileDisabled && (
              <small className="text-muted ms-2">
                {isEditMode ? "(Not Editable)" : "(Verified)"}
              </small>
            )}
          </label>
          <PhoneInput
            country="in"
            value={formik.values.basicDetails?.phoneNumber || ""}
            onChange={(value) => {
              formik.setFieldValue("basicDetails.phoneNumber", value || "");
            }}
            onBlur={() =>
              formik.setFieldTouched("basicDetails.phoneNumber", true)
            }
            placeholder="Enter phone number"
            disabled={isMobileDisabled}
            inputClass={`form-control ${
              formik.touched.basicDetails?.phoneNumber &&
              formik.errors.basicDetails?.phoneNumber
                ? "is-invalid"
                : formik.touched.basicDetails?.phoneNumber &&
                  formik.values.basicDetails?.phoneNumber &&
                  !formik.errors.basicDetails?.phoneNumber
                ? "is-valid"
                : ""
            }`}
            containerClass="phone-input-container"
            buttonClass="phone-input-button"
          />
          <ErrorMessage
            name="basicDetails.phoneNumber"
            component="div"
            className="invalid-feedback"
          />
          {isMobileDisabled && (
            <small className="text-info">
              {isEditMode
                ? "Phone number cannot be edited in admin panel."
                : "Mobile number is already verified and cannot be changed."}
            </small>
          )}
        </div>

        {/* Hidden field for userType */}
        <Field type="hidden" name="basicDetails.userType" value={userType} />
      </div>
    </div>
  );
};

export default BasicDetailsForm;
