import { authAPI } from "./axiosInstance";

/**
 * Authentication Service
 * Handles mobile registration, OTP verification, and basic user management
 */

// Mobile Registration Flow
export const authService = {
  /**
   * Send OTP to mobile number
   * @param {string} phoneNumber - Mobile number with country code
   * @param {string} userType - Selected user type (vendor, customer, driver, etc.)
   * @returns {Promise} API response with OTP request ID and user info
   */
  sendOTP: async (phoneNumber, userType = null) => {
    try {
      const response = await authAPI.post("/auth/send-otp", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""), // Remove spaces
        userType,
      });
      return {
        success: true,
        data: response.data,
        message: "O<PERSON> sent successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to send OTP",
        error: error.response?.data,
      };
    }
  },

  /**
   * Verify OTP
   * @param {string} phoneNumber - Mobile number
   * @param {string} otp - OTP code
   * @param {string} requestId - OTP request ID
   * @param {string} userType - Selected user type
   * @returns {Promise} API response with verification status
   */
  verifyOTP: async (phoneNumber, otp, requestId, userType = null) => {
    try {
      const response = await authAPI.post("/auth/verify-otp", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""),
        otp,
        requestId,
        userType,
      });
      return {
        success: true,
        data: response.data,
        message: "OTP verified successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Invalid OTP",
        error: error.response?.data,
      };
    }
  },

  /**
   * Complete basic information registration
   * @param {Object} basicInfo - User basic information
   * @returns {Promise} API response with user data and token
   */
  completeBasicInfo: async (basicInfo) => {
    try {
      const response = await authAPI.post("/auth/complete-basic-info", {
        firstName: basicInfo.firstName,
        lastName: basicInfo.lastName,
        email: basicInfo.email,
        phoneNumber: basicInfo.phoneNumber,
      });
      return {
        success: true,
        data: response.data,
        message: "Registration completed successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Registration failed",
        error: error.response?.data,
      };
    }
  },

  /**
   * Check phone number registration status and existing user types
   * @param {string} phoneNumber - Mobile number
   * @param {string} requestedUserType - Requested user type
   * @returns {Promise} API response with registration status and user types
   */
  checkPhoneRegistration: async (phoneNumber, requestedUserType) => {
    try {
      const response = await authAPI.post("/auth/check-phone-registration", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""),
        requestedUserType,
      });
      return {
        success: true,
        data: response.data,
        // data structure: { isRegistered, existingUserTypes, canRegisterNewType, conflictInfo }
      };
    } catch (error) {
      return {
        success: false,
        message:
          error.response?.data?.message || "Failed to check phone registration",
        error: error.response?.data,
      };
    }
  },

  /**
   * Resend OTP
   * @param {string} phoneNumber - Mobile number
   * @returns {Promise} API response with new OTP request ID
   */
  resendOTP: async (phoneNumber) => {
    try {
      const response = await authAPI.post("/auth/resend-otp", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""),
      });
      return {
        success: true,
        data: response.data,
        message: "OTP resent successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to resend OTP",
        error: error.response?.data,
      };
    }
  },
};

// Mock implementations for development (remove in production)
export const mockAuthService = {
  sendOTP: async (phoneNumber, userType = null) => {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Mock success response
    return {
      success: true,
      data: {
        requestId: `mock_req_${Date.now()}`,
        expiresIn: 300, // 5 minutes
        userType,
      },
      message: "OTP sent successfully",
    };
  },

  verifyOTP: async (phoneNumber, otp, requestId, userType = null) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Mock verification (accept 123456 as valid OTP)
    if (otp === "123456") {
      const cleanPhone = phoneNumber.replace(/\D/g, "");
      const lastDigit = cleanPhone.slice(-1);
      const isExistingUser = ["1", "2", "3"].includes(lastDigit);

      let responseData = {
        verified: true,
        userType,
        isExistingUser,
        existingUserTypes: [],
      };

      if (isExistingUser) {
        // Mock existing user data
        responseData.user = {
          id: `user_${cleanPhone}`,
          firstName: "John",
          lastName: "Doe",
          email: `user${cleanPhone}@example.com`,
          phoneNumber: phoneNumber,
          userType: userType,
          isProfileCompleted: true,
          isKYCCompleted: true,
        };
        responseData.token = `jwt_token_${Date.now()}`;

        // Set existing user types based on last digit
        switch (lastDigit) {
          case "1":
            responseData.existingUserTypes = [7]; // Shipper Individual
            break;
          case "2":
            responseData.existingUserTypes = [6]; // Shipper Company
            break;
          case "3":
            responseData.existingUserTypes = [7, 4]; // Shipper Individual + Carrier
            break;
        }
      } else {
        // New user - just verification token
        responseData.tempToken = `temp_token_${Date.now()}`;
        responseData.existingUserTypes = [];
      }

      return {
        success: true,
        data: responseData,
        message: "OTP verified successfully",
      };
    } else {
      return {
        success: false,
        message: "Invalid OTP. Use 123456 for testing.",
      };
    }
  },

  completeBasicInfo: async (basicInfo) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return {
      success: true,
      data: {
        user: {
          id: Date.now(),
          firstName: basicInfo.firstName,
          lastName: basicInfo.lastName,
          email: basicInfo.email,
          phoneNumber: basicInfo.phoneNumber,
          isProfileCompleted: true,
          isKYCCompleted: false,
        },
        token: `jwt_token_${Date.now()}`,
      },
      message: "Registration completed successfully",
    };
  },

  checkPhoneRegistration: async (phoneNumber, requestedUserType) => {
    await new Promise((resolve) => setTimeout(resolve, 500));

    // Mock logic for testing different scenarios
    const cleanPhone = phoneNumber.replace(/\D/g, "");
    const lastDigit = cleanPhone.slice(-1);

    let mockData = {
      isRegistered: false,
      existingUserTypes: [],
      canRegisterNewType: true,
      conflictInfo: null,
    };

    // Mock scenarios based on last digit
    switch (lastDigit) {
      case "1": // Existing Shipper Individual
        mockData = {
          isRegistered: true,
          existingUserTypes: [7], // Shipper Individual
          canRegisterNewType: requestedUserType !== 7,
          conflictInfo:
            requestedUserType === 7
              ? {
                  existingUserType: 7,
                  requestedUserType,
                  message:
                    "This number is already registered as a Shipper Individual.",
                }
              : null,
        };
        break;
      case "2": // Existing Shipper Company
        mockData = {
          isRegistered: true,
          existingUserTypes: [6], // Shipper Company
          canRegisterNewType: requestedUserType !== 6,
          conflictInfo:
            requestedUserType === 6
              ? {
                  existingUserType: 6,
                  requestedUserType,
                  message:
                    "This number is already registered as a Shipper Company.",
                }
              : null,
        };
        break;
      case "3": // Multiple user types
        mockData = {
          isRegistered: true,
          existingUserTypes: [7, 4], // Shipper Individual + Carrier
          canRegisterNewType: ![7, 4].includes(requestedUserType),
          conflictInfo: [7, 4].includes(requestedUserType)
            ? {
                existingUserType: [7, 4].find(
                  (type) => type === requestedUserType
                ),
                requestedUserType,
                message: `This number is already registered with this user type.`,
              }
            : null,
        };
        break;
      default: // New number
        mockData = {
          isRegistered: false,
          existingUserTypes: [],
          canRegisterNewType: true,
          conflictInfo: null,
        };
    }

    return {
      success: true,
      data: mockData,
    };
  },

  resendOTP: async (phoneNumber) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    return {
      success: true,
      data: {
        requestId: `mock_req_${Date.now()}`,
        expiresIn: 300,
      },
      message: "OTP resent successfully",
    };
  },
};

// Use mock services in development, real services in production
const isDevelopment = import.meta.env.MODE === "development";
export const activeAuthService = isDevelopment ? mockAuthService : authService;
export const activeProfileService = mockProfileService; // Always use mock for demo
