import { useMutation } from '@tanstack/react-query';
import { API_ENDPOINTS } from '@constants/apiNamevariables';
import { baseAPI } from './axiosInstance';

// PAN verification
export const usePanVerification = (options) =>
  useMutation({
    mutationFn: (data) =>
      baseAPI.post(API_ENDPOINTS.VERIFY_PAN, {
        pan: data.pan,
        consent: data.consent || "Y",
        reason: data.reason || "KYC onboarding"
      }),
    ...options,
  });
