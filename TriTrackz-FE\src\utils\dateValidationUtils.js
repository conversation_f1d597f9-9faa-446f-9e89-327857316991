/**
 * Date Validation Utilities
 * Provides comprehensive date validation functions for forms
 */

/**
 * Date field types with specific validation rules
 */
export const DATE_FIELD_TYPES = {
  DATE_OF_BIRTH: 'dateOfBirth',
  DATE_OF_INCORPORATION: 'dateOfIncorporation',
  REGISTRATION_DATE: 'registrationDate',
  FUTURE_DATE: 'futureDate',
  PAST_DATE: 'pastDate',
  ANY_DATE: 'anyDate'
};

/**
 * Age validation constants
 */
export const AGE_LIMITS = {
  MIN_AGE: 18, // Minimum age for legal documents
  MAX_AGE: 120, // Maximum realistic age
  MIN_INCORPORATION_YEARS: 1950, // Minimum year for company incorporation
  MAX_FUTURE_YEARS: 10 // Maximum years in future for certain dates
};

/**
 * Calculate age from date of birth
 * @param {Date|string} dateOfBirth - Date of birth
 * @returns {number} Age in years
 */
export const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) return 0;
  
  const birthDate = new Date(dateOfBirth);
  const today = new Date();
  
  if (isNaN(birthDate.getTime())) return 0;
  
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Get minimum date based on field type
 * @param {string} fieldType - Type of date field
 * @returns {Date} Minimum allowed date
 */
export const getMinDate = (fieldType) => {
  const today = new Date();
  
  switch (fieldType) {
    case DATE_FIELD_TYPES.DATE_OF_BIRTH:
      // Minimum age of 18 years
      const minBirthDate = new Date();
      minBirthDate.setFullYear(today.getFullYear() - AGE_LIMITS.MAX_AGE);
      return minBirthDate;
      
    case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
    case DATE_FIELD_TYPES.REGISTRATION_DATE:
      // Minimum year for business registration
      return new Date(AGE_LIMITS.MIN_INCORPORATION_YEARS, 0, 1);
      
    case DATE_FIELD_TYPES.FUTURE_DATE:
      // Today is minimum for future dates
      return today;
      
    case DATE_FIELD_TYPES.PAST_DATE:
      // 100 years ago as reasonable minimum
      const minPastDate = new Date();
      minPastDate.setFullYear(today.getFullYear() - 100);
      return minPastDate;
      
    default:
      // Default to 100 years ago
      const defaultMinDate = new Date();
      defaultMinDate.setFullYear(today.getFullYear() - 100);
      return defaultMinDate;
  }
};

/**
 * Get maximum date based on field type
 * @param {string} fieldType - Type of date field
 * @returns {Date} Maximum allowed date
 */
export const getMaxDate = (fieldType) => {
  const today = new Date();
  
  switch (fieldType) {
    case DATE_FIELD_TYPES.DATE_OF_BIRTH:
      // Maximum age of 18 years (minimum legal age)
      const maxBirthDate = new Date();
      maxBirthDate.setFullYear(today.getFullYear() - AGE_LIMITS.MIN_AGE);
      return maxBirthDate;
      
    case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
    case DATE_FIELD_TYPES.REGISTRATION_DATE:
    case DATE_FIELD_TYPES.PAST_DATE:
      // Today is maximum for past dates
      return today;
      
    case DATE_FIELD_TYPES.FUTURE_DATE:
      // Maximum 10 years in future
      const maxFutureDate = new Date();
      maxFutureDate.setFullYear(today.getFullYear() + AGE_LIMITS.MAX_FUTURE_YEARS);
      return maxFutureDate;
      
    default:
      // Default to today
      return today;
  }
};

/**
 * Format date for HTML date input (YYYY-MM-DD)
 * @param {Date} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDateForInput = (date) => {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
};

/**
 * Validate date based on field type
 * @param {Date|string} date - Date to validate
 * @param {string} fieldType - Type of date field
 * @returns {object} Validation result with isValid and error message
 */
export const validateDate = (date, fieldType) => {
  if (!date) {
    return { isValid: false, error: 'Date is required' };
  }
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, error: 'Enter a valid date' };
  }
  
  const minDate = getMinDate(fieldType);
  const maxDate = getMaxDate(fieldType);
  
  if (dateObj < minDate) {
    switch (fieldType) {
      case DATE_FIELD_TYPES.DATE_OF_BIRTH:
        return { isValid: false, error: 'Enter a realistic date of birth' };
      case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
        return { isValid: false, error: `Date cannot be before ${AGE_LIMITS.MIN_INCORPORATION_YEARS}` };
      case DATE_FIELD_TYPES.REGISTRATION_DATE:
        return { isValid: false, error: `Registration date cannot be before ${AGE_LIMITS.MIN_INCORPORATION_YEARS}` };
      default:
        return { isValid: false, error: 'Date is too far in the past' };
    }
  }
  
  if (dateObj > maxDate) {
    switch (fieldType) {
      case DATE_FIELD_TYPES.DATE_OF_BIRTH:
        const age = calculateAge(dateObj);
        return { isValid: false, error: `Age must be at least ${AGE_LIMITS.MIN_AGE} years` };
      case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
      case DATE_FIELD_TYPES.REGISTRATION_DATE:
      case DATE_FIELD_TYPES.PAST_DATE:
        return { isValid: false, error: 'Date cannot be in the future' };
      case DATE_FIELD_TYPES.FUTURE_DATE:
        return { isValid: false, error: `Date cannot be more than ${AGE_LIMITS.MAX_FUTURE_YEARS} years in the future` };
      default:
        return { isValid: false, error: 'Date cannot be in the future' };
    }
  }
  
  // Additional age validation for date of birth
  if (fieldType === DATE_FIELD_TYPES.DATE_OF_BIRTH) {
    const age = calculateAge(dateObj);
    if (age < AGE_LIMITS.MIN_AGE) {
      return { isValid: false, error: `Age must be at least ${AGE_LIMITS.MIN_AGE} years` };
    }
    if (age > AGE_LIMITS.MAX_AGE) {
      return { isValid: false, error: `Age cannot exceed ${AGE_LIMITS.MAX_AGE} years` };
    }
  }
  
  return { isValid: true, error: null };
};

/**
 * Get date validation attributes for HTML input
 * @param {string} fieldType - Type of date field
 * @returns {object} Object with min and max attributes
 */
export const getDateInputAttributes = (fieldType) => {
  const minDate = getMinDate(fieldType);
  const maxDate = getMaxDate(fieldType);
  
  return {
    min: formatDateForInput(minDate),
    max: formatDateForInput(maxDate)
  };
};

/**
 * Validate age requirement
 * @param {Date|string} dateOfBirth - Date of birth
 * @param {number} minAge - Minimum required age (default: 18)
 * @returns {object} Validation result
 */
export const validateAge = (dateOfBirth, minAge = AGE_LIMITS.MIN_AGE) => {
  if (!dateOfBirth) {
    return { isValid: false, error: 'Date of birth is required' };
  }
  
  const age = calculateAge(dateOfBirth);
  
  if (age < minAge) {
    return { isValid: false, error: `Age must be at least ${minAge} years` };
  }
  
  return { isValid: true, error: null };
};

/**
 * Check if date is in the future
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the future
 */
export const isFutureDate = (date) => {
  if (!date) return false;
  const dateObj = new Date(date);
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Reset time to start of day
  return dateObj > today;
};

/**
 * Check if date is in the past
 * @param {Date|string} date - Date to check
 * @returns {boolean} True if date is in the past
 */
export const isPastDate = (date) => {
  if (!date) return false;
  const dateObj = new Date(date);
  const today = new Date();
  today.setHours(23, 59, 59, 999); // Reset time to end of day
  return dateObj < today;
};

/**
 * Validate date consistency between related fields
 * @param {Date|string} dateOfBirth - Date of birth
 * @param {Date|string} incorporationDate - Date of incorporation
 * @returns {object} Validation result
 */
export const validateDateConsistency = (dateOfBirth, incorporationDate) => {
  if (!dateOfBirth || !incorporationDate) {
    return { isValid: true, error: null };
  }

  const birthDate = new Date(dateOfBirth);
  const incorpDate = new Date(incorporationDate);

  if (isNaN(birthDate.getTime()) || isNaN(incorpDate.getTime())) {
    return { isValid: false, error: 'Invalid date format' };
  }

  // Check if incorporation date is after birth date + minimum age
  const minIncorpDate = new Date(birthDate);
  minIncorpDate.setFullYear(birthDate.getFullYear() + AGE_LIMITS.MIN_AGE);

  if (incorpDate < minIncorpDate) {
    return {
      isValid: false,
      error: `Incorporation date must be at least ${AGE_LIMITS.MIN_AGE} years after date of birth`
    };
  }

  return { isValid: true, error: null };
};

/**
 * Get age-appropriate date ranges for different scenarios
 * @param {string} scenario - Scenario type ('minor', 'adult', 'senior', 'business')
 * @returns {object} Object with min and max dates
 */
export const getAgeAppropriateRange = (scenario) => {
  const today = new Date();

  switch (scenario) {
    case 'minor':
      // 0-17 years old
      const minorMax = new Date();
      minorMax.setFullYear(today.getFullYear() - 0);
      const minorMin = new Date();
      minorMin.setFullYear(today.getFullYear() - 17);
      return { min: minorMin, max: minorMax };

    case 'adult':
      // 18-65 years old
      const adultMax = new Date();
      adultMax.setFullYear(today.getFullYear() - AGE_LIMITS.MIN_AGE);
      const adultMin = new Date();
      adultMin.setFullYear(today.getFullYear() - 65);
      return { min: adultMin, max: adultMax };

    case 'senior':
      // 65+ years old
      const seniorMax = new Date();
      seniorMax.setFullYear(today.getFullYear() - 65);
      const seniorMin = new Date();
      seniorMin.setFullYear(today.getFullYear() - AGE_LIMITS.MAX_AGE);
      return { min: seniorMin, max: seniorMax };

    case 'business':
      // Business incorporation dates
      const businessMax = today;
      const businessMin = new Date(AGE_LIMITS.MIN_INCORPORATION_YEARS, 0, 1);
      return { min: businessMin, max: businessMax };

    default:
      return { min: getMinDate(DATE_FIELD_TYPES.ANY_DATE), max: getMaxDate(DATE_FIELD_TYPES.ANY_DATE) };
  }
};

/**
 * Validate business registration date against incorporation date
 * @param {Date|string} registrationDate - Registration date
 * @param {Date|string} incorporationDate - Incorporation date
 * @returns {object} Validation result
 */
export const validateBusinessDates = (registrationDate, incorporationDate) => {
  if (!registrationDate || !incorporationDate) {
    return { isValid: true, error: null };
  }

  const regDate = new Date(registrationDate);
  const incorpDate = new Date(incorporationDate);

  if (isNaN(regDate.getTime()) || isNaN(incorpDate.getTime())) {
    return { isValid: false, error: 'Invalid date format' };
  }

  // Registration date should be on or after incorporation date
  if (regDate < incorpDate) {
    return {
      isValid: false,
      error: 'Registration date cannot be before incorporation date'
    };
  }

  // Registration date should not be more than 30 days after incorporation
  const maxRegDate = new Date(incorpDate);
  maxRegDate.setDate(incorpDate.getDate() + 30);

  if (regDate > maxRegDate) {
    return {
      isValid: false,
      error: 'Registration date should be within 30 days of incorporation'
    };
  }

  return { isValid: true, error: null };
};

/**
 * Get localized date format for display
 * @param {Date|string} date - Date to format
 * @param {string} locale - Locale string (default: 'en-IN')
 * @returns {string} Formatted date string
 */
export const formatDateForDisplay = (date, locale = 'en-IN') => {
  if (!date) return '';

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';

  return dateObj.toLocaleDateString(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};
