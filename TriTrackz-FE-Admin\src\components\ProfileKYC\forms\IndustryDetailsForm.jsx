import React from "react";
import { Field, ErrorMessage } from "formik";
import { INDUSTRY_TYPES, BUSINESS_CATEGORIES } from "@constants/enum";
import { DOCUMENT_TYPES } from "@api/documentUploadHooks";
import SelectInput from "@components/Common/SelectInput";
import MultipleDocumentUploadLikeAadhaar from "./MultipleDocumentUploadLikeAadhaar";

const IndustryDetailsForm = ({
  formik,
  existingDocuments,
  onDocumentChange,
  targetUserId = null,
}) => {
  const industryType = formik.values.industryDetails?.industryType;
  const businessCategory = formik.values.industryDetails?.businessCategory;

  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">Industry Details</h3>
        <p className="text-muted mb-0">
          Please provide your industry and business category information
        </p>
      </div>
      <div className="row">
        <div className="col-md-6 mb-3">
          <label
            htmlFor="industryDetails.industryType"
            className="form-label fw-medium"
          >
            Industry Type <span className="text-danger">*</span>
          </label>
          <SelectInput
            name="industryDetails.industryType"
            value={formik.values.industryDetails?.industryType || ""}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            options={INDUSTRY_TYPES}
            placeholder="Select industry type"
            isInvalid={
              formik.touched.industryDetails?.industryType &&
              formik.errors.industryDetails?.industryType
            }
            isValid={
              formik.touched.industryDetails?.industryType &&
              formik.values.industryDetails?.industryType &&
              !formik.errors.industryDetails?.industryType
            }
            isClearable={true}
            isSearchable={true}
          />
          <ErrorMessage
            name="industryDetails.industryType"
            component="div"
            className="invalid-feedback"
          />
        </div>

        {/* Custom Industry Type Input - Show when "Others" is selected */}
        {industryType === "Others" && (
          <div className="col-md-6 mb-3">
            <label
              htmlFor="industryDetails.customIndustryType"
              className="form-label fw-medium"
            >
              Custom Industry Type <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="industryDetails.customIndustryType"
              className={`clean-form-control ${
                formik.touched.industryDetails?.customIndustryType &&
                formik.errors.industryDetails?.customIndustryType
                  ? "is-invalid"
                  : formik.touched.industryDetails?.customIndustryType &&
                    formik.values.industryDetails?.customIndustryType
                  ? "is-valid"
                  : ""
              }`}
              placeholder="Enter custom industry type"
            />
            <ErrorMessage
              name="industryDetails.customIndustryType"
              component="div"
              className="invalid-feedback"
            />
          </div>
        )}

        <div className="col-md-6 mb-3">
          <label
            htmlFor="industryDetails.businessCategory"
            className="form-label fw-medium"
          >
            Business Category <span className="text-danger">*</span>
          </label>
          <SelectInput
            name="industryDetails.businessCategory"
            value={formik.values.industryDetails?.businessCategory || ""}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            options={BUSINESS_CATEGORIES}
            placeholder="Select business category"
            isInvalid={
              formik.touched.industryDetails?.businessCategory &&
              formik.errors.industryDetails?.businessCategory
            }
            isValid={
              formik.touched.industryDetails?.businessCategory &&
              formik.values.industryDetails?.businessCategory &&
              !formik.errors.industryDetails?.businessCategory
            }
            isClearable={true}
            isSearchable={true}
          />
          <ErrorMessage
            name="industryDetails.businessCategory"
            component="div"
            className="invalid-feedback"
          />
        </div>

        {/* Custom Business Category Input - Show when "Others" is selected */}
        {businessCategory === "Others" && (
          <div className="col-md-6 mb-3">
            <label
              htmlFor="industryDetails.customBusinessCategory"
              className="form-label fw-medium"
            >
              Custom Business Category <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="industryDetails.customBusinessCategory"
              className={`clean-form-control ${
                formik.touched.industryDetails?.customBusinessCategory &&
                formik.errors.industryDetails?.customBusinessCategory
                  ? "is-invalid"
                  : formik.touched.industryDetails?.customBusinessCategory &&
                    formik.values.industryDetails?.customBusinessCategory
                  ? "is-valid"
                  : ""
              }`}
              placeholder="Enter custom business category"
            />
            <ErrorMessage
              name="industryDetails.customBusinessCategory"
              component="div"
              className="invalid-feedback"
            />
          </div>
        )}
      </div>

      {/* Regulatory Documents Upload - Show when any industry type is selected */}
      {industryType && (
        <div className="row">
          <div className="col-12 mb-3">
            <MultipleDocumentUploadLikeAadhaar
              documentType={DOCUMENT_TYPES.BUSINESS_LICENSE}
              title="Regulatory Documents"
              accept=".pdf,.jpg,.jpeg,.png"
              existingDocuments={existingDocuments}
              onDocumentChange={onDocumentChange}
              maxDocuments={5}
              targetUserId={targetUserId}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default IndustryDetailsForm;
