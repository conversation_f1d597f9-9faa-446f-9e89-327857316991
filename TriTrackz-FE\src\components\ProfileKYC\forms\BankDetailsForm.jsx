import React from 'react';
import { Field, ErrorMessage } from 'formik';

const BankDetailsForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Bank Details</h3>
        <p className="clean-section-subtitle">Please provide your bank account information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.accountNumber" className="clean-form-label">
              Account Number <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.accountNumber"
              className={`clean-form-control ${
                formik.touched.bankDetails?.accountNumber && formik.errors.bankDetails?.accountNumber
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter account number"
            />
            <ErrorMessage
              name="bankDetails.accountNumber"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.accountHolderName" className="clean-form-label">
              Account Holder Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.accountHolderName"
              className={`clean-form-control ${
                formik.touched.bankDetails?.accountHolderName && formik.errors.bankDetails?.accountHolderName
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter account holder name"
            />
            <ErrorMessage
              name="bankDetails.accountHolderName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.bankName" className="clean-form-label">
              Bank Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.bankName"
              className={`clean-form-control ${
                formik.touched.bankDetails?.bankName && formik.errors.bankDetails?.bankName
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter bank name"
            />
            <ErrorMessage
              name="bankDetails.bankName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="bankDetails.ifscCode" className="clean-form-label">
              IFSC Code <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="bankDetails.ifscCode"
              className={`clean-form-control ${
                formik.touched.bankDetails?.ifscCode && formik.errors.bankDetails?.ifscCode
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter IFSC code"
              style={{ textTransform: 'uppercase' }}
            />
            <ErrorMessage
              name="bankDetails.ifscCode"
              component="div"
              className="clean-form-error"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BankDetailsForm;
