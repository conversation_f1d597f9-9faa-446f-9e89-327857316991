import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { useSelector } from 'react-redux';
import PhoneInput from '@components/Common/PhoneInput';
import { shouldDisableMobileNumber } from '@utils/profileUtils';

const BasicDetailsForm = ({ formik, userType, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  // Get profile data from Redux to check if mobile should be disabled
  const { profileData } = useSelector((state) => state.user);
  const isMobileDisabled = shouldDisableMobileNumber(profileData);
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Basic Details</h3>
        <p className="clean-section-subtitle">Please provide your basic information</p>
      </div>

      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="basicDetails.firstName" className="clean-form-label">
              First Name <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="basicDetails.firstName"
              className={`clean-form-control ${
                formik.touched.basicDetails?.firstName && formik.errors.basicDetails?.firstName
                  ? 'is-invalid'
                  : formik.touched.basicDetails?.firstName && formik.values.basicDetails?.firstName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter first name"
            />
            <ErrorMessage
              name="basicDetails.firstName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="basicDetails.lastName" className="clean-form-label">
              Last Name
            </label>
            <Field
              type="text"
              name="basicDetails.lastName"
              className={`clean-form-control ${
                formik.touched.basicDetails?.lastName && formik.errors.basicDetails?.lastName
                  ? 'is-invalid'
                  : formik.touched.basicDetails?.lastName && formik.values.basicDetails?.lastName
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter last name"
            />
            <ErrorMessage
              name="basicDetails.lastName"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="basicDetails.email" className="clean-form-label">
              Email
            </label>
            <Field
              type="email"
              name="basicDetails.email"
              className={`clean-form-control ${
                formik.touched.basicDetails?.email && formik.errors.basicDetails?.email
                  ? 'is-invalid'
                  : formik.touched.basicDetails?.email && formik.values.basicDetails?.email
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter email address"
            />
            <ErrorMessage
              name="basicDetails.email"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="basicDetails.phoneNumber" className="clean-form-label">
              Phone Number <span className="text-danger">*</span>
              {isMobileDisabled && (
                <small className="text-muted ms-2">(Verified)</small>
              )}
            </label>
            <PhoneInput
              name="basicDetails.phoneNumber"
              value={formik.values.basicDetails?.phoneNumber || ''}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter phone number"
              isInvalid={formik.touched.basicDetails?.phoneNumber && formik.errors.basicDetails?.phoneNumber}
              isValid={formik.touched.basicDetails?.phoneNumber && formik.values.basicDetails?.phoneNumber && !formik.errors.basicDetails?.phoneNumber}
              country="in"
              disabled={isMobileDisabled}
            />
            <ErrorMessage
              name="basicDetails.phoneNumber"
              component="div"
              className="clean-form-error"
            />
            {isMobileDisabled && (
              <small className="text-info">
                Mobile number is already verified and cannot be changed.
              </small>
            )}
          </div>

          {/* Hidden field for userType */}
          <Field
            type="hidden"
            name="basicDetails.userType"
            value={userType}
          />
        </div>
      </div>
    </div>
  );
};

export default BasicDetailsForm;
