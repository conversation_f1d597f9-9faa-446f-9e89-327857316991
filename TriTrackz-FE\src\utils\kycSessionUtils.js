/**
 * Utility functions for managing KYC form session state
 * This prevents the KYC completion popup from showing while user is actively filling the form
 */

const KYC_FORM_ACTIVE_KEY = 'kyc-form-active';
const KYC_FORM_LAST_ACTIVITY_KEY = 'kyc-form-last-activity';
const INACTIVITY_TIMEOUT = 10 * 60 * 1000; // 10 minutes in milliseconds

/**
 * Mark that user is actively in KYC form
 */
export const setKYCFormActive = () => {
  sessionStorage.setItem(KYC_FORM_ACTIVE_KEY, 'true');
  sessionStorage.setItem(KYC_FORM_LAST_ACTIVITY_KEY, Date.now().toString());
};

/**
 * Update the last activity timestamp
 */
export const updateKYCFormActivity = () => {
  sessionStorage.setItem(KYC_FORM_LAST_ACTIVITY_KEY, Date.now().toString());
};

/**
 * Clear KYC form session flags
 */
export const clearKYCFormSession = () => {
  sessionStorage.removeItem(KYC_FORM_ACTIVE_KEY);
  sessionStorage.removeItem(KYC_FORM_LAST_ACTIVITY_KEY);
};

/**
 * Check if user is currently in KYC form
 * @returns {boolean} - Whether user is actively in KYC form
 */
export const isKYCFormActive = () => {
  const isActive = sessionStorage.getItem(KYC_FORM_ACTIVE_KEY) === 'true';
  
  if (!isActive) {
    return false;
  }
  
  // Check if session has expired due to inactivity
  const lastActivity = sessionStorage.getItem(KYC_FORM_LAST_ACTIVITY_KEY);
  if (lastActivity) {
    const timeSinceActivity = Date.now() - parseInt(lastActivity);
    
    if (timeSinceActivity > INACTIVITY_TIMEOUT) {
      // Session expired, clear flags
      clearKYCFormSession();
      return false;
    }
  }
  
  return true;
};

/**
 * Check if KYC form session has expired and clean up if needed
 */
export const cleanupExpiredKYCSession = () => {
  const lastActivity = sessionStorage.getItem(KYC_FORM_LAST_ACTIVITY_KEY);
  
  if (lastActivity) {
    const timeSinceActivity = Date.now() - parseInt(lastActivity);
    
    if (timeSinceActivity > INACTIVITY_TIMEOUT) {
      clearKYCFormSession();
    }
  }
};

/**
 * Set up periodic cleanup of expired sessions
 * @returns {number} - Interval ID for cleanup
 */
export const setupKYCSessionCleanup = () => {
  return setInterval(cleanupExpiredKYCSession, 60000); // Check every minute
};
