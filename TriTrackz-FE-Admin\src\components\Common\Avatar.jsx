import React, { memo, useMemo, useCallback } from "react";
import { FaUser } from "react-icons/fa";

// Move size config outside component to prevent recreation on each render
const SIZE_CONFIG = {
  small: {
    width: "32px",
    height: "32px",
    fontSize: "14px",
    apiSize: "32",
  },
  medium: {
    width: "48px",
    height: "48px",
    fontSize: "18px",
    apiSize: "48",
  },
  large: {
    width: "80px",
    height: "80px",
    fontSize: "32px",
    apiSize: "80",
  },
  xlarge: {
    width: "120px",
    height: "120px",
    fontSize: "48px",
    apiSize: "120",
  },
};

// Move static styles outside component - keeping minimal inline styles for dynamic values
const FALLBACK_STYLE = {
  background: `linear-gradient(135deg, var(--triadic-purple-600), var(--triadic-purple-500))`,
  color: "white",
  boxShadow: "0 2px 8px rgba(148, 70, 148, 0.4)",
};

// Utility functions moved outside component
const getInitials = (name) => {
  if (!name) return "U";

  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    return words[0].charAt(0).toUpperCase();
  }
  return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
};

const generateAvatarUrl = (
  name,
  currentSize,
  backgroundColor,
  textColor,
  length,
  rounded
) => {
  if (!name) return null;

  const params = new URLSearchParams({
    name: name.trim(),
    size: currentSize.apiSize,
    background:
      backgroundColor === "random"
        ? "random"
        : backgroundColor.replace("#", ""),
    color: textColor.replace("#", ""),
    length: length.toString(),
    rounded: rounded.toString(),
    bold: "true",
    format: "png",
  });

  return `https://ui-avatars.com/api/?${params.toString()}`;
};

const Avatar = memo(
  ({
    src,
    alt = "Avatar",
    name,
    size = "medium",
    className = "",
    showBorder = false,
    borderColor = "var(--triadic-purple-500)",
    backgroundColor = "random",
    textColor = "ffffff",
    style,
    onClick,
    rounded = true,
    length = 1,
    ...props
  }) => {
    const currentSize = SIZE_CONFIG[size] || SIZE_CONFIG.medium;

    // Memoize the avatar URL to prevent unnecessary API calls
    const imageSource = useMemo(() => {
      return (
        src ||
        (name
          ? generateAvatarUrl(
              name,
              currentSize,
              backgroundColor,
              textColor,
              length,
              rounded
            )
          : null)
      );
    }, [src, name, currentSize, backgroundColor, textColor, length, rounded]);

    // Memoize the avatar style to prevent object recreation
    const avatarStyle = useMemo(
      () => ({
        width: currentSize.width,
        height: currentSize.height,
        fontSize: currentSize.fontSize,
        cursor: onClick ? "pointer" : "default",
        border: showBorder ? `2px solid ${borderColor}` : "none",
        ...style,
      }),
      [currentSize, onClick, showBorder, borderColor, style]
    );

    // Memoize the final style to prevent object recreation
    const finalStyle = useMemo(() => {
      return imageSource ? avatarStyle : { ...avatarStyle, ...FALLBACK_STYLE };
    }, [imageSource, avatarStyle]);

    // Memoize Bootstrap classes
    const avatarClasses = useMemo(() => {
      const baseClasses =
        "rounded-circle d-flex align-items-center justify-content-center fw-semibold flex-shrink-0 overflow-hidden";
      return `${baseClasses} ${className}`;
    }, [className]);

    // Memoize event handlers
    const handleClick = useCallback(
      (e) => {
        if (onClick) {
          onClick(e);
        }
      },
      [onClick]
    );

    const handleKeyDown = useCallback(
      (e) => {
        if (onClick && (e.key === "Enter" || e.key === " ")) {
          e.preventDefault();
          onClick(e);
        }
      },
      [onClick]
    );

    // Memoize error handler to prevent recreation
    const handleImageError = useCallback(
      (e) => {
        e.target.style.display = "none";
        e.target.parentElement.style.background = FALLBACK_STYLE.background;
        e.target.parentElement.style.color = FALLBACK_STYLE.color;
        e.target.parentElement.innerHTML = getInitials(name);
      },
      [name]
    );

    // Memoize initials to prevent recalculation
    const initials = useMemo(() => getInitials(name), [name]);

    // Memoize icon size to prevent recalculation
    const iconSize = useMemo(
      () => parseInt(currentSize.fontSize) * 0.6,
      [currentSize.fontSize]
    );

    return (
      <div
        className={avatarClasses}
        style={finalStyle}
        onClick={onClick ? handleClick : undefined}
        onKeyDown={onClick ? handleKeyDown : undefined}
        tabIndex={onClick ? 0 : -1}
        role={onClick ? "button" : "img"}
        aria-label={alt}
        {...props}
      >
        {imageSource ? (
          <img
            src={imageSource}
            alt={alt}
            className="w-100 h-100 rounded-circle"
            style={{ objectFit: "cover" }}
            onError={handleImageError}
            loading="lazy"
          />
        ) : name ? (
          initials
        ) : (
          <FaUser size={iconSize} />
        )}
      </div>
    );
  }
);

Avatar.displayName = "Avatar";

export default Avatar;
