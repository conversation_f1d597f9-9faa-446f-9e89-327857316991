import React, { useState, useEffect, useCallback, useMemo, memo } from "react";
import { useFormik, FormikProvider } from "formik";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import toast from "react-hot-toast";

import _ from "lodash";
import { extractAutoFillData } from "@utils/profileUtils";
import { setProfileData } from "@store/userSlice";

import { USER_TYPES, USER_TYPE_LABELS } from "@constants/enum";
import ROUTES from "@constants/routes";

import { useGetProfile, useUpdateProfile } from "@api/profileHooks";

import BasicDetailsForm from "./forms/BasicDetailsForm";
import BusinessAddressForm from "./forms/BusinessAddressForm";
import CompanyDetailsForm from "./forms/CompanyDetailsForm";
import PanCardDetailsForm from "./forms/PanCardDetailsForm";
import AadhaarCardDetailsForm from "./forms/AadhaarCardDetailsForm";
import BankDetailsForm from "./forms/BankDetailsForm";
import GstDetailsForm from "./forms/GstDetailsForm";
import CinNumberForm from "./forms/CinNumberForm";
import IndustryDetailsForm from "./forms/IndustryDetailsForm";

import {
  basicDetailsSchema,
  businessAddressSchema,
  companyDetailsSchema,
  panCardDetailsSchema,
  aadhaarCardDetailsSchema,
  bankDetailsSchema,
  gstDetailsSchema,
  cinNumberSchema,
  industryDetailsSchema,
} from "./validationSchemas";

// Constants moved outside component to prevent recreation
const COMPANY_USER_TYPES = [
  USER_TYPES.TRANSPORT_COMPANY,
  USER_TYPES.CARRIER,
  USER_TYPES.SHIPPER_COMPANY,
];

const VALIDATION_SCHEMA_MAP = {
  basic: basicDetailsSchema,
  company: companyDetailsSchema,
  address: businessAddressSchema,
  gst: gstDetailsSchema,
  cin: cinNumberSchema,
  pan: panCardDetailsSchema,
  aadhaar: aadhaarCardDetailsSchema,
  bank: bankDetailsSchema,
  industry: industryDetailsSchema,
};

const FIELD_PATH_MAP = {
  basic: "basicDetails",
  company: "companyDetails",
  address: "businessAddress",
  gst: "gstDetails",
  cin: "cinNumber",
  pan: "panCardDetails",
  aadhaar: "aadhaarCardDetails",
  bank: "bankDetails",
  industry: "industryDetails",
};

const FORM_INIT_DELAY = 1000; // 1 second
const VALIDATION_DEBOUNCE_MS = 1000; // 1 second

// Helper functions moved outside component to prevent recreation
const getSteps = (userType) => {
  const baseSteps = [
    { id: "basic", title: "Basic Details", component: BasicDetailsForm },
    {
      id: "address",
      title: "Business Address",
      component: BusinessAddressForm,
    },
    { id: "pan", title: "PAN Card Details", component: PanCardDetailsForm },
    {
      id: "aadhaar",
      title: "Aadhaar Card Details",
      component: AadhaarCardDetailsForm,
    },
    { id: "bank", title: "Bank Details", component: BankDetailsForm },
  ];

  // Add company-specific steps for company types
  if (COMPANY_USER_TYPES.includes(userType)) {
    baseSteps.splice(2, 0, {
      id: "company",
      title: "Company Details",
      component: CompanyDetailsForm,
    });
    baseSteps.splice(4, 0, {
      id: "gst",
      title: "GST Details",
      component: GstDetailsForm,
    });
    baseSteps.splice(5, 0, {
      id: "cin",
      title: "CIN Number",
      component: CinNumberForm,
    });
  }

  // Add industry details for Shipper Company
  if (userType === USER_TYPES.SHIPPER_COMPANY) {
    baseSteps.push({
      id: "industry",
      title: "Industry Details",
      component: IndustryDetailsForm,
    });
  }

  return baseSteps;
};

const getCurrentStepSchema = (stepIndex, steps) => {
  const step = steps[stepIndex];
  return VALIDATION_SCHEMA_MAP[step?.id] || null;
};

const getCurrentStepFieldPath = (stepIndex, steps) => {
  const step = steps[stepIndex];
  return FIELD_PATH_MAP[step?.id] || null;
};

const formatDateForAPI = (dateString) => {
  if (!dateString) return null;
  return new Date(dateString).toISOString();
};

// Memoized Step Indicator Component
const StepIndicator = memo(
  ({ steps, currentStep, completedSteps, isStepValid, onStepClick }) => {
    return (
      <div className="mb-4">
        <div className="d-flex justify-content-between align-items-center">
          {steps.map((step, index) => {
            const isCompleted = completedSteps.includes(index);
            const isCurrent = index === currentStep;
            const isValid = isStepValid(index);
            const canClick =
              isCompleted ||
              isCurrent ||
              (index === currentStep + 1 && isValid);

            return (
              <div
                key={step.id}
                className={`d-flex flex-column align-items-center p-2 rounded ${
                  isCurrent ? "bg-primary text-white" : ""
                } ${isCompleted ? "bg-success text-white" : ""} ${
                  isCurrent || isCompleted ? "border-0" : "border"
                }`}
                onClick={() => canClick && onStepClick(index)}
                style={{
                  cursor: canClick ? "pointer" : "default",
                  minWidth: "80px",
                }}
              >
                <div
                  className={`rounded-circle d-flex align-items-center justify-content-center mb-1 fw-semibold ${
                    isCurrent || isCompleted ? "" : "bg-light text-dark"
                  }`}
                  style={{
                    width: "32px",
                    height: "32px",
                    fontSize: "14px",
                  }}
                >
                  {isCompleted ? "✓" : index + 1}
                </div>
                <div className="text-center small fw-medium">{step.title}</div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }
);

StepIndicator.displayName = "StepIndicator";

const ProfileKYCForm = memo(
  ({ initialData, onSuccess = null, onCancel = null, hideHeader = false }) => {
    // Validation: This component requires initialData for edit mode
    if (!initialData) {
      console.error("ProfileKYCForm: initialData is required for edit mode");
      return (
        <div className="alert alert-danger" role="alert">
          <h4 className="alert-heading">Missing Profile Data</h4>
          <p>
            This form requires existing profile data to edit. Please ensure the
            profile data is loaded before rendering this component.
          </p>
        </div>
      );
    }

    // Validation: Ensure we have the target user's ID and userType
    if (!initialData.userId && !initialData.id) {
      console.error(
        "ProfileKYCForm: Target user ID is missing from initialData"
      );
      return (
        <div className="alert alert-danger" role="alert">
          <h4 className="alert-heading">Missing User ID</h4>
          <p>
            The target user ID is required to edit their profile. Please ensure
            the initialData contains userId or id field.
          </p>
        </div>
      );
    }

    if (!initialData.userType) {
      console.error(
        "ProfileKYCForm: Target user type is missing from initialData"
      );
      return (
        <div className="alert alert-danger" role="alert">
          <h4 className="alert-heading">Missing User Type</h4>
          <p>
            The target user type is required to render the appropriate form
            fields. Please ensure the initialData contains userType field.
          </p>
        </div>
      );
    }

    // This component is designed for EDIT MODE ONLY
    const isEditMode = true;
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);
    const [completedSteps, setCompletedSteps] = useState([]);
    const [isFormInitialized, setIsFormInitialized] = useState(false);
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const queryClient = useQueryClient();

    // For admin panel: Don't use admin user data, use the target user's data from initialData
    const targetUserId = initialData?.userId || initialData?.id;
    const [documents, setDocuments] = useState(initialData?.documents || []);

    // Memoized derived values for EDIT MODE - use target user's data
    const userType = useMemo(
      () => initialData?.userType,
      [initialData?.userType]
    );
    const steps = useMemo(() => getSteps(userType), [userType]);

    // For edit mode, use only initialData as the source
    const autoFillData = useMemo(
      () => extractAutoFillData(initialData),
      [initialData]
    );

    // Fetch profile data to ensure it's loaded (for refetch functionality)
    const { isLoading: profileLoading, refetch: refetchProfile } =
      useGetProfile(targetUserId, {
        enabled: !!targetUserId,
        onSuccess: (response) => {
          if (response?.data) {
            dispatch(setProfileData(response.data));
            // Extract documents from profile data
            if (response.data.documents) {
              setDocuments(response.data.documents);
            }
          }
        },
        onError: (error) => {
          console.error("Failed to fetch profile for KYC form:", error);
        },
      });

    // Initialize session tracking on component mount
    useEffect(() => {
      if (!sessionStorage.getItem("session-start-time")) {
        sessionStorage.setItem("session-start-time", Date.now().toString());
      }
    }, []);

    // Memoized document change handler
    const handleDocumentChange = useCallback(async () => {
      try {
        // Invalidate and refetch profile query to ensure fresh data
        console.log("Invalidating queries for user:", targetUserId);
        await queryClient.invalidateQueries(["profile", targetUserId]);

        // Also manually refetch
        if (refetchProfile) {
          console.log("Calling refetchProfile...");
          const result = await refetchProfile();
          console.log("Profile refetch result:", result);
          console.log("Profile refetch result data:", result?.data);

          // Update documents state immediately if we get new data
          const newDocuments =
            result?.data?.data?.documents || result?.data?.documents;
          if (newDocuments) {
            console.log("Updating documents from refetch:", newDocuments);
            setDocuments(newDocuments);

            // Also update Redux store
            if (result?.data?.data) {
              dispatch(setProfileData(result.data.data));
            } else if (result?.data) {
              dispatch(setProfileData(result.data));
            }
          } else {
            console.warn("No documents found in refetch result");
          }
        } else {
          console.warn("refetchProfile function not available");
        }
      } catch (error) {
        console.error("Error refetching profile:", error);
      }
    }, [refetchProfile, queryClient, targetUserId, dispatch]);

    // Update documents when initialData changes
    useEffect(() => {
      if (initialData?.documents) {
        setDocuments(initialData.documents);
      }
    }, [initialData]);

    // Edit mode: No draft functionality needed

    // Memoized success handler for API operations
    const handleSuccess = useCallback(
      async (response) => {
        console.log("Profile update successful:", response);

        // Fetch updated profile data using React Query hook
        try {
          const updatedProfile = await refetchProfile();
          if (updatedProfile?.data?.data) {
            dispatch(setProfileData(updatedProfile.data.data));
            console.log(
              "✅ Profile data updated from API:",
              updatedProfile.data.data
            );
          } else if (response?.data) {
            dispatch(setProfileData(response.data));
            console.log(
              "✅ Profile data updated from response:",
              response.data
            );
          } else {
            const fallbackProfileData = {
              ...initialData,
              status: 1,
              isComplete: true,
              kycStatus: "Completed",
            };
            dispatch(setProfileData(fallbackProfileData));
            console.log(
              "✅ Profile data updated with fallback:",
              fallbackProfileData
            );
          }
        } catch (error) {
          console.error("Error fetching updated profile:", error);
          if (response?.data) {
            dispatch(setProfileData(response.data));
            console.log(
              "✅ Profile data updated from response (fallback):",
              response.data
            );
          } else {
            const fallbackProfileData = {
              ...initialData,
              status: 1,
              isComplete: true,
              kycStatus: "Completed",
            };
            dispatch(setProfileData(fallbackProfileData));
            console.log(
              "✅ Profile data updated with fallback (error case):",
              fallbackProfileData
            );
          }
        }

        // Handle success based on mode
        if (isEditMode && onSuccess) {
          onSuccess(response);
        } else {
          navigate(ROUTES.DASHBOARD);
        }

        setIsSubmitting(false);
      },
      [initialData, refetchProfile, dispatch, isEditMode, onSuccess, navigate]
    );

    // Memoized error handler for profile updates
    const handleError = useCallback((error) => {
      const apiErrorMessage = error?.response?.data?.message;
      const defaultErrorMessage = "Failed to update profile. Please try again.";

      const displayMessage = apiErrorMessage || defaultErrorMessage;
      console.log("📢 Error message:", displayMessage);
      setIsSubmitting(false);
    }, []);

    // API hooks
    const { mutate: submitComprehensiveProfile } = useUpdateProfile({
      onSuccess: handleSuccess,
      onError: handleError,
    });

    // Memoized helper functions
    const getStepSchema = useCallback(
      (stepIndex) => getCurrentStepSchema(stepIndex, steps),
      [steps]
    );
    const getStepFieldPath = useCallback(
      (stepIndex) => getCurrentStepFieldPath(stepIndex, steps),
      [steps]
    );

    // Validate current step
    const validateCurrentStep = async (stepIndex) => {
      const schema = getStepSchema(stepIndex);
      const fieldPath = getStepFieldPath(stepIndex);
      const stepInfo = steps[stepIndex];

      console.log(`🔍 Validating step ${stepIndex} (${stepInfo?.title})`);
      console.log(`📂 Field path: ${fieldPath}`);
      console.log(`📋 Schema exists: ${!!schema}`);

      if (!schema || !fieldPath) {
        console.log(`❌ No schema or field path for step ${stepIndex}`);
        return false;
      }

      try {
        const stepData = formik.values[fieldPath];
        console.log(`📊 Step data to validate:`, stepData);

        // Only validate if we have step data
        if (!stepData || Object.keys(stepData).length === 0) {
          console.log(`⚠️ No data in step ${stepIndex}, treating as invalid`);
          throw new Error(`${stepInfo?.title || "Step"} data is required`);
        }

        await schema.validate(stepData, {
          abortEarly: false,
          context: { userType },
        });

        // Clear any existing errors for this step if validation passes
        const clearedErrors = { ...formik.errors };
        delete clearedErrors[fieldPath];
        formik.setErrors(clearedErrors);

        console.log(
          `✅ Step ${stepIndex} (${stepInfo?.title}) validation passed`
        );
        return true;
      } catch (error) {
        console.log(
          `❌ Step ${stepIndex} (${stepInfo?.title}) validation failed:`,
          error
        );

        // Set field errors for the current step ONLY
        const stepErrors = {};
        const errorMessages = [];

        if (error.inner) {
          error.inner.forEach((err) => {
            // Create nested error structure for proper display
            if (!stepErrors[fieldPath]) {
              stepErrors[fieldPath] = {};
            }
            stepErrors[fieldPath][err.path] = err.message;
            errorMessages.push(err.message);
            console.log(
              `Field error: ${fieldPath}.${err.path} = ${err.message}`
            );
          });
        } else {
          stepErrors[fieldPath] = error.message;
          errorMessages.push(error.message);
        }

        // Set errors for current step (keep existing errors from other steps)
        formik.setErrors({ ...formik.errors, ...stepErrors });

        // Mark ALL fields in this step as touched to show validation errors immediately
        const touchedFields = {};
        const stepDataKeys = Object.keys(formik.values[fieldPath] || {});
        stepDataKeys.forEach((field) => {
          touchedFields[field] = true;
        });

        // Also mark any nested fields as touched
        if (error.inner) {
          error.inner.forEach((err) => {
            touchedFields[err.path] = true;
          });
        }

        formik.setTouched({
          ...formik.touched,
          [fieldPath]: { ...formik.touched[fieldPath], ...touchedFields },
        });

        // Show toast notification for validation errors
        if (errorMessages.length > 0) {
          const stepTitle = stepInfo?.title || "Current step";
          toast.error(`Please fill all required fields in ${stepTitle}`, {
            duration: 4000,
            position: "top-center",
          });
        }

        console.log(
          `Marked fields as touched for step ${stepIndex}:`,
          touchedFields
        );
        return false;
      }
    };

    // Check if step is valid and complete
    const isStepValid = (stepIndex) => {
      const schema = getStepSchema(stepIndex);
      const fieldPath = getStepFieldPath(stepIndex);

      if (!schema || !fieldPath) return false;

      const stepData = formik.values[fieldPath];

      // Check if step data exists
      if (!stepData || typeof stepData !== "object") return false;

      try {
        // Validate the step data against its schema
        schema.validateSync(stepData, {
          abortEarly: false,
          context: { userType },
        });

        // Check if all required fields have valid values
        const hasRequiredData = Object.entries(stepData).every(
          ([key, value]) => {
            // Check if field is required in schema
            const fieldSchema = schema.fields[key];
            if (!fieldSchema) return true;

            // If field is required, check if it has a valid value
            const isRequired =
              fieldSchema.tests?.some((test) => test.name === "required") ||
              fieldSchema._exclusive?.required ||
              fieldSchema.spec?.presence === "required";

            if (isRequired) {
              // More strict validation for required fields
              if (value === null || value === undefined || value === "") {
                return false;
              }
              // For strings, also check if it's not just whitespace
              if (typeof value === "string" && value.trim() === "") {
                return false;
              }
              // For arrays, check if they have elements
              if (Array.isArray(value) && value.length === 0) {
                return false;
              }
            }

            return true;
          }
        );

        // Additional check: ensure no validation errors exist for this step
        const stepErrors = formik.errors[fieldPath];
        const hasNoErrors =
          !stepErrors ||
          (typeof stepErrors === "object" &&
            Object.keys(stepErrors).length === 0);

        return hasRequiredData && hasNoErrors;
      } catch (error) {
        // If validation fails, the step is not valid
        return false;
      }
    };

    // Remove step from completed steps if it becomes invalid
    const validateAndUpdateCompletedSteps = () => {
      const validCompletedSteps = completedSteps.filter((stepIndex) =>
        isStepValid(stepIndex)
      );
      if (validCompletedSteps.length !== completedSteps.length) {
        setCompletedSteps(validCompletedSteps);
      }
    };

    // Force validation and show all errors for current step
    const forceValidateCurrentStep = async () => {
      const fieldPath = getStepFieldPath(currentStep);
      const stepInfo = steps[currentStep];

      console.log(
        `🔧 Force validating step ${currentStep} (${stepInfo?.title})`
      );
      console.log(`📂 Field path: ${fieldPath}`);

      if (!fieldPath) {
        console.log(`❌ No field path for current step ${currentStep}`);
        return false;
      }

      // Mark all fields in current step as touched first
      const stepData = formik.values[fieldPath] || {};
      console.log(`📊 Current step data:`, stepData);

      const touchedFields = {};
      Object.keys(stepData).forEach((field) => {
        touchedFields[field] = true;
      });

      console.log(`👆 Marking fields as touched:`, touchedFields);

      formik.setTouched({
        ...formik.touched,
        [fieldPath]: { ...formik.touched[fieldPath], ...touchedFields },
      });

      // Then validate
      return await validateCurrentStep(currentStep);
    };

    // Memoized navigation functions
    const handleNext = useCallback(async () => {
      if (currentStep < steps.length - 1) {
        const currentStepInfo = steps[currentStep];
        console.log("🚀 Next button clicked");
        console.log(
          "📍 Current step:",
          currentStep,
          currentStepInfo?.title,
          currentStepInfo?.id
        );
        console.log("📊 Current form values:", formik.values);
        console.log("❌ Current form errors:", formik.errors);

        const isValid = await forceValidateCurrentStep();
        console.log("📋 Validation result:", isValid);

        if (isValid) {
          // Add current step to completed steps if not already there
          if (!completedSteps.includes(currentStep)) {
            setCompletedSteps([...completedSteps, currentStep]);
          }

          console.log(
            `✅ Moving from step ${currentStep} to step ${currentStep + 1}`
          );
          setCurrentStep(currentStep + 1);
        } else {
          // Get the current step name for better error message
          const currentStepName = steps[currentStep]?.title || "current step";
          console.warn(`⚠️ Validation failed for ${currentStepName}`);
          console.log("❌ Current form errors:", formik.errors);

          // Scroll to first error field if any
          setTimeout(() => {
            const firstErrorField = document.querySelector(
              ".is-invalid, .clean-form-error:not(:empty)"
            );
            if (firstErrorField) {
              console.log(
                "📍 Scrolling to first error field:",
                firstErrorField
              );
              firstErrorField.scrollIntoView({
                behavior: "smooth",
                block: "center",
              });
            } else {
              console.log("🔍 No error fields found in DOM");
            }
          }, 100);

          console.warn(
            `⚠️ Please fill all required fields correctly in ${currentStepName} before proceeding`
          );
        }
      }
    }, [currentStep, steps, forceValidateCurrentStep, completedSteps]);

    const handlePrevious = useCallback(() => {
      if (currentStep > 0) {
        setCurrentStep(currentStep - 1);
      }
    }, [currentStep]);

    const handleStepClick = useCallback(
      async (stepIndex) => {
        // Allow clicking on completed steps, current step, or next step if current is valid
        const canNavigate =
          completedSteps.includes(stepIndex) ||
          stepIndex === currentStep ||
          (stepIndex === currentStep + 1 && isStepValid(currentStep));

        if (canNavigate) {
          // If moving to next step and current step is valid, mark current as completed
          if (
            stepIndex > currentStep &&
            isStepValid(currentStep) &&
            !completedSteps.includes(currentStep)
          ) {
            setCompletedSteps([...completedSteps, currentStep]);
          }
          setCurrentStep(stepIndex);
        } else {
          // If trying to skip ahead, validate current step first
          if (stepIndex > currentStep) {
            const isCurrentValid = await validateCurrentStep(currentStep);
            if (!isCurrentValid) {
              const currentStepName =
                steps[currentStep]?.title || "current step";
              const targetStepName = steps[stepIndex]?.title || "step";

              // Scroll to first error field if any
              setTimeout(() => {
                const firstErrorField = document.querySelector(
                  ".is-invalid, .clean-form-error:not(:empty)"
                );
                if (firstErrorField) {
                  firstErrorField.scrollIntoView({
                    behavior: "smooth",
                    block: "center",
                  });
                }
              }, 100);

              console.warn(
                `⚠️ Please complete ${currentStepName} with valid data before accessing ${targetStepName}`
              );
              return;
            }
          }

          // Provide more specific error message for other cases
          const currentStepName = steps[currentStep]?.title || "current step";
          const targetStepName = steps[stepIndex]?.title || "step";
          console.warn(
            `⚠️ Please complete ${currentStepName} with valid data before accessing ${targetStepName}`
          );
        }
      },
      [completedSteps, currentStep, isStepValid, validateCurrentStep, steps]
    );

    // Memoized initial values based on user type
    const initialValues = useMemo(() => {
      console.log("getInitialValues - autoFillData:", autoFillData);

      const baseValues = {
        basicDetails: {
          firstName: autoFillData.firstName || null,
          lastName: autoFillData.lastName || null,
          email: autoFillData.email || null,
          phoneNumber: autoFillData.phoneNumber || null,
          userType: userType,
        },
        businessAddress: {
          address: autoFillData.address || null,
          city: autoFillData.city || null,
          state: autoFillData.state || null,
          country: autoFillData.country || "India",
          pincode: autoFillData.postalCode || null,
        },
        panCardDetails: {
          nameAsPerPan:
            autoFillData.nameAsPerPan || autoFillData.displayName || null,
          fatherOrHusbandNameInPan:
            autoFillData.fatherOrHusbandNameInPan || null,
          dateOfBirthInPan: autoFillData.dateOfBirthInPan || null,
          panNumber: autoFillData.panNumber || null,
        },
        aadhaarCardDetails: {
          aadhaarNumber: autoFillData.aadharNumber || null,
          nameAsPerAadhaar:
            autoFillData.nameAsPerAadhaar || autoFillData.displayName || null,
          fatherOrHusbandNameInAadhaar:
            autoFillData.fatherOrHusbandNameInAadhaar || null,
          dateOfBirthInAadhaar: autoFillData.dateOfBirthInAadhaar || null,
          genderInAadhaar: autoFillData.genderInAadhaar || null,
        },
        bankDetails: {
          accountNumber: autoFillData.accountNumber || null,
          accountHolderName: autoFillData.accountHolderName || null,
          bankName: autoFillData.bankName || null,
          ifscCode: autoFillData.ifscCode || null,
        },
      };

      // Add company-specific fields for company types
      if (
        [
          USER_TYPES.TRANSPORT_COMPANY,
          USER_TYPES.CARRIER,
          USER_TYPES.SHIPPER_COMPANY,
        ].includes(userType)
      ) {
        baseValues.companyDetails = {
          companyName: autoFillData.companyName || null,
          brandName: autoFillData.brandName || null,
          companyLogo: autoFillData.companyLogo || null,
          companyContactEmail: autoFillData.companyContactEmail || null,
          companyContactPhone: autoFillData.companyContactPhone || null,
        };

        baseValues.gstDetails = {
          gstNumber:
            autoFillData.gstNumber ||
            autoFillData.gstRegistrationNumber ||
            null,
          legalName: autoFillData.legalName || autoFillData.companyName || null,
          tradeName: autoFillData.tradeName || autoFillData.companyName || null,
          placeOfBusiness:
            autoFillData.placeOfBusiness || autoFillData.address || null,
          registrationDate: autoFillData.gstRegistrationDate || null,
          registrationNumber:
            autoFillData.gstRegistrationNumber ||
            autoFillData.gstNumber ||
            null,
        };

        baseValues.cinNumber = {
          cin: autoFillData.cinNumber || null,
        };

        // Add company-specific PAN fields
        baseValues.panCardDetails.companyName =
          autoFillData.companyName || null;
        baseValues.panCardDetails.dateOfIncorporation =
          autoFillData.dateOfIncorporation || null;
        baseValues.panCardDetails.businessPanNumber =
          autoFillData.businessPanNumber || null;
      }

      // Add industry details for Shipper Company
      if (userType === USER_TYPES.SHIPPER_COMPANY) {
        baseValues.industryDetails = {
          industryType: autoFillData.industryType || null,
          customIndustryType: autoFillData.customIndustryType || null,
          businessCategory: autoFillData.businessCategory || null,
          customBusinessCategory: autoFillData.customBusinessCategory || null,
        };
      }

      return baseValues;
    }, [userType, autoFillData]);

    // Memoized real-time validation for current step
    const validateCurrentStepRealTime = useCallback(async () => {
      // Don't run validation if form is not initialized or no user interaction
      if (!isFormInitialized) return;

      const schema = getStepSchema(currentStep);
      const fieldPath = getStepFieldPath(currentStep);

      if (!schema || !fieldPath) return;

      const stepData = formik.values[fieldPath];
      const stepTouched = formik.touched[fieldPath];

      // Only validate if the step has been touched by the user
      if (!stepTouched || Object.keys(stepTouched).length === 0) return;

      // Only validate if we have some data in the step
      if (!stepData || Object.keys(stepData).length === 0) return;

      try {
        await schema.validate(stepData, {
          abortEarly: false,
          context: { userType },
        });

        // Clear errors for this step if validation passes
        const clearedErrors = { ...formik.errors };
        delete clearedErrors[fieldPath];
        formik.setErrors(clearedErrors);
      } catch (error) {
        // Only set errors for fields that have been touched
        const stepErrors = {};
        if (error.inner) {
          error.inner.forEach((err) => {
            const fieldTouched = formik.touched[fieldPath]?.[err.path];

            // Only show error if field has been touched
            if (fieldTouched) {
              // Create nested error structure for proper display
              if (!stepErrors[fieldPath]) {
                stepErrors[fieldPath] = {};
              }
              stepErrors[fieldPath][err.path] = err.message;
            }
          });
        }

        // Only update errors if we have errors to set
        if (Object.keys(stepErrors).length > 0) {
          formik.setErrors({ ...formik.errors, ...stepErrors });
        }
      }
    }, [getStepSchema, getStepFieldPath, currentStep]);

    // Get initial values for edit mode
    const getFormInitialValues = useCallback(() => {
      return initialValues;
    }, [initialValues]);

    // Formik setup
    const formik = useFormik({
      initialValues: getFormInitialValues(),
      validationSchema: undefined, // Remove global validation - use step-specific validation
      validationContext: { userType },
      validateOnChange: false, // Disable to prevent premature validation
      validateOnBlur: true, // Keep blur validation for better UX
      enableReinitialize: true,
      onSubmit: async (values) => {
        console.log("🚀 Form submission started");
        console.log("📋 Form values:", values);
        console.log("✅ Form is valid:", formik.isValid);
        console.log("🔍 Form errors:", formik.errors);

        setIsSubmitting(true);

        try {
          // Format dates to ISO string
          const formatDate = (dateString) => {
            if (!dateString) return null;
            return new Date(dateString).toISOString();
          };

          // Structure the payload to match API expectations with nested objects
          const structuredData = {
            // Basic Details
            basicDetails: {
              firstName: values.basicDetails?.firstName || "",
              lastName: values.basicDetails?.lastName || "",
              email: values.basicDetails?.email || "",
              phoneNumber: values.basicDetails?.phoneNumber || "",
              userType: values.basicDetails?.userType || userType,
            },

            // Business Address
            businessAddress: {
              address: values.businessAddress?.address || "",
              city: values.businessAddress?.city || "",
              state: values.businessAddress?.state || "",
              country: values.businessAddress?.country || "",
              postalCode: values.businessAddress?.pincode || "",
            },

            // PAN Card Details
            panCardDetails: {
              nameAsPerPan: values.panCardDetails?.nameAsPerPan || "",
              fatherOrHusbandNameInPan:
                values.panCardDetails?.fatherOrHusbandNameInPan || "",
              dateOfBirthInPan: formatDate(
                values.panCardDetails?.dateOfBirthInPan
              ),
              panNumber: values.panCardDetails?.panNumber || "",
            },

            // Aadhaar Card Details
            aadhaarCardDetails: {
              aadharNumber: values.aadhaarCardDetails?.aadhaarNumber || "",
              nameAsPerAadhaar:
                values.aadhaarCardDetails?.nameAsPerAadhaar || "",
              fatherOrHusbandNameInAadhaar:
                values.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar || "",
              dateOfBirthInAadhaar: formatDate(
                values.aadhaarCardDetails?.dateOfBirthInAadhaar
              ),
              genderInAadhaar: values.aadhaarCardDetails?.genderInAadhaar || "",
            },

            // Bank Details
            bankDetails: {
              accountNumber: values.bankDetails?.accountNumber || "",
              accountHolderName: values.bankDetails?.accountHolderName || "",
              bankName: values.bankDetails?.bankName || "",
              ifscCode: values.bankDetails?.ifscCode || "",
            },
          };

          // Add company-specific fields for company types
          if (
            [
              USER_TYPES.TRANSPORT_COMPANY,
              USER_TYPES.CARRIER,
              USER_TYPES.SHIPPER_COMPANY,
            ].includes(userType)
          ) {
            // Company Details
            structuredData.companyDetails = {
              companyName: values.companyDetails?.companyName || "",
              brandName: values.companyDetails?.brandName || "",
              companyLogo: values.companyDetails?.companyLogo || "",
              companyContactEmail:
                values.companyDetails?.companyContactEmail || "",
              companyContactPhone:
                values.companyDetails?.companyContactPhone || "",
            };

            // GST Details
            structuredData.gstDetails = {
              gstNumber: values.gstDetails?.gstNumber || "",
              legalName: values.gstDetails?.legalName || "",
              tradeName: values.gstDetails?.tradeName || "",
              placeOfBusiness: values.gstDetails?.placeOfBusiness || "",
              registrationDate: formatDate(values.gstDetails?.registrationDate),
              registrationNumber: values.gstDetails?.registrationNumber || "",
            };

            // Add company-specific PAN fields
            structuredData.panCardDetails = {
              ...structuredData.panCardDetails,
              companyName: values.panCardDetails?.companyName || "",
              dateOfIncorporation: formatDate(
                values.panCardDetails?.dateOfIncorporation
              ),
              businessPanNumber: values.panCardDetails?.businessPanNumber || "",
            };

            // CIN Number
            structuredData.cinNumber = {
              cin: values.cinNumber?.cin || "",
            };
          }

          // Add industry details for Shipper Company
          if (userType === USER_TYPES.SHIPPER_COMPANY) {
            structuredData.industryDetails = {
              industryType: values.industryDetails?.industryType || "",
              customIndustryType:
                values.industryDetails?.customIndustryType || "",
              businessCategory: values.industryDetails?.businessCategory || "",
              customBusinessCategory:
                values.industryDetails?.customBusinessCategory || "",
            };
          }

          // Remove undefined fields
          const cleanedValues = JSON.parse(
            JSON.stringify(structuredData, (_, value) => {
              return value === undefined ? null : value;
            })
          );

          // Submit comprehensive profile with status: 1 for completion
          const finalProfileData = {
            ...cleanedValues,
            status: 1, // Mark as completed KYC
          };

          // Add profile ID if editing existing profile
          if (initialData?.id) {
            finalProfileData.id = initialData.id;
            console.log("📝 Adding profile ID for editing:", initialData.id);
          }

          console.log(
            "📤 Submitting final KYC with status: 1",
            finalProfileData
          );

          console.log(
            "🔧 submitComprehensiveProfile function:",
            submitComprehensiveProfile
          );
          console.log("🆔 Profile Data ID:", initialData?.id);

          if (!targetUserId) {
            throw new Error("Target User ID is missing");
          }

          if (!submitComprehensiveProfile) {
            throw new Error(
              "submitComprehensiveProfile function is not available"
            );
          }

          console.log("🚀 Calling submitComprehensiveProfile API...");
          submitComprehensiveProfile({
            userId: targetUserId,
            profileData: finalProfileData,
          });

          console.log("✅ API call initiated successfully");
        } catch (error) {
          console.error("❌ Form submission error:", error);
          console.error("📄 Error details:", error.message);
          console.error("🔍 Error stack:", error.stack);

          const errorMessage = error?.message || "Failed to update profile";
          console.error(`❌ Submission failed: ${errorMessage}`);
          setIsSubmitting(false);
        }
      },
    });

    console.log("autoFillData", formik.values, initialValues, isEditMode);

    // Real-time validation effect - only run when user stops typing and has interacted
    useEffect(() => {
      // Don't run validation on initial load or if no user interaction
      const hasInteracted = Object.keys(formik.touched).length > 0;
      if (!hasInteracted || !isFormInitialized) return;

      const timeoutId = setTimeout(() => {
        validateCurrentStepRealTime();
        // Also validate and update completed steps
        validateAndUpdateCompletedSteps();
      }, VALIDATION_DEBOUNCE_MS); // Longer debounce to prevent interference with typing

      return () => clearTimeout(timeoutId);
    }, [
      formik.values,
      currentStep,
      isFormInitialized,
      validateCurrentStepRealTime,
      validateAndUpdateCompletedSteps,
    ]);

    // Additional effect to handle form errors and update step states
    useEffect(() => {
      // Force re-render when errors change to update step visual states
      const hasErrors = Object.keys(formik.errors).length > 0;
      if (hasErrors) {
        // Trigger a state update to refresh step indicators
        setCompletedSteps((prev) => [...prev]);
      }
    }, [formik.errors]);

    // Edit mode: Form initialization handled by initialValues

    // Set form as initialized after first render
    useEffect(() => {
      const timer = setTimeout(() => {
        setIsFormInitialized(true);
      }, FORM_INIT_DELAY); // Give form time to initialize with data

      return () => clearTimeout(timer);
    }, []);

    // Check if user type is valid
    if (!userType || !Object.values(USER_TYPES).includes(userType)) {
      return (
        <div className="alert alert-danger">
          <h5>Invalid User Type</h5>
          <p>Unable to determine your user type. Please contact support.</p>
        </div>
      );
    }

    const CurrentStepComponent = steps[currentStep]?.component;

    // Show loading if profile data is still being fetched
    if (profileLoading && !initialData) {
      return (
        <div className="d-flex justify-content-center align-items-center min-vh-50">
          <div className="text-center">
            <div className="spinner-border text-primary" role="status">
              <span className="visually-hidden">Loading profile data...</span>
            </div>
            <p className="mt-3 text-muted">
              Loading your profile information...
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="container-fluid">
        {/* Edit Mode: No draft prompt needed */}

        {/* Header */}
        {!hideHeader && (
          <div className="border-bottom pb-4 mb-4">
            <div className="row">
              <div className="col-12">
                <div className="d-flex justify-content-between align-items-start mb-3">
                  <div>
                    <h1 className="h2 fw-semibold text-dark mb-2">
                      Edit Profile & KYC Information
                    </h1>
                    <p className="text-muted mb-0">
                      Update your profile and KYC information as needed. User
                      Type: <strong>{USER_TYPE_LABELS[userType]}</strong>
                    </p>
                  </div>
                </div>

                <div className="mb-3">
                  <div className="progress" style={{ height: "4px" }}>
                    <div
                      className="progress-bar bg-success"
                      role="progressbar"
                      style={{
                        width: `${
                          (completedSteps.filter((stepIndex) =>
                            isStepValid(stepIndex)
                          ).length /
                            steps.length) *
                          100
                        }%`,
                      }}
                      aria-valuenow={
                        completedSteps.filter((stepIndex) =>
                          isStepValid(stepIndex)
                        ).length
                      }
                      aria-valuemin="0"
                      aria-valuemax={steps.length}
                    ></div>
                  </div>
                  <div className="d-flex justify-content-between align-items-center mt-1">
                    <small className="text-muted">
                      {
                        completedSteps.filter((stepIndex) =>
                          isStepValid(stepIndex)
                        ).length
                      }{" "}
                      of {steps.length} steps completed
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content Layout */}
        <div className="row">
          {/* Left Side - Stepper Navigation */}
          <div className="col-12 col-lg-3">
            <div className="pe-lg-3">
              <div className="d-flex flex-column gap-3">
                {steps.map((step, index) => {
                  const isCompleted = completedSteps.includes(index);
                  const isActive = index === currentStep;
                  const isValid = isStepValid(index);
                  const isClickable =
                    isCompleted ||
                    isActive ||
                    (index === currentStep + 1 && isStepValid(currentStep));

                  // Determine step state for visual display
                  const showCompleted = isCompleted && isValid;
                  const showValid = isValid && !isActive && !isCompleted;
                  const showInvalid =
                    !isValid &&
                    !isActive &&
                    (formik.touched[getStepFieldPath(index)] ||
                      completedSteps.includes(index));

                  return (
                    <div
                      key={step.id}
                      className={`d-flex align-items-center p-3 rounded border ${
                        isActive ? "bg-primary text-white border-primary" : ""
                      } ${
                        showCompleted
                          ? "bg-success text-white border-success"
                          : ""
                      } ${showValid ? "border-success" : ""} ${
                        showInvalid ? "border-danger" : ""
                      } ${
                        !isActive &&
                        !showCompleted &&
                        !showValid &&
                        !showInvalid
                          ? "border-light"
                          : ""
                      }`}
                      onClick={() => handleStepClick(index)}
                      style={{
                        cursor: isClickable ? "pointer" : "default",
                      }}
                    >
                      <div
                        className={`rounded-circle d-flex align-items-center justify-content-center me-3 fw-semibold ${
                          isActive || showCompleted
                            ? ""
                            : showValid
                            ? "bg-success text-white"
                            : showInvalid
                            ? "bg-danger text-white"
                            : "bg-light text-dark"
                        }`}
                        style={{
                          width: "40px",
                          height: "40px",
                          fontSize: "14px",
                          minWidth: "40px",
                        }}
                      >
                        {showCompleted ? "✓" : showInvalid ? "!" : index + 1}
                      </div>
                      <div className="flex-grow-1">
                        <div
                          className={`small fw-medium text-uppercase ${
                            isActive || showCompleted
                              ? "text-white-50"
                              : "text-muted"
                          }`}
                          style={{
                            fontSize: "11px",
                            letterSpacing: "0.5px",
                          }}
                        >
                          Step {index + 1}
                        </div>
                        <div className="fw-semibold small lh-sm">
                          {step.title}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Right Side - Form */}
          <div className="col-12 col-lg-9">
            <div className="ps-lg-3">
              <FormikProvider value={formik}>
                <form
                  onSubmit={formik.handleSubmit}
                  className="h-100 d-flex flex-column"
                >
                  {/* Current Step Form */}
                  <div className="flex-grow-1 mb-4">
                    {CurrentStepComponent && (
                      <CurrentStepComponent
                        formik={formik}
                        userType={userType}
                        existingDocuments={documents}
                        onDocumentChange={handleDocumentChange}
                        isEditMode={isEditMode}
                        targetUserId={targetUserId}
                      />
                    )}
                  </div>

                  {/* Navigation - Right Aligned */}
                  <div className="border-top pt-3 mt-auto">
                    <div className="d-flex justify-content-end align-items-center gap-3">
                      <div className="text-muted small fw-medium">
                        Step {currentStep + 1} of {steps.length}
                      </div>

                      <div className="d-flex align-items-center gap-2">
                        <button
                          type="button"
                          className="btn btn-outline-secondary d-flex align-items-center"
                          style={{ minWidth: "100px" }}
                          onClick={handlePrevious}
                          disabled={currentStep === 0}
                        >
                          <FaChevronLeft className="me-1" />
                          Previous
                        </button>

                        {currentStep === steps.length - 1 ? (
                          <button
                            type="button"
                            className="btn btn-primary"
                            disabled={isSubmitting}
                            onClick={async () => {
                              console.log("🚀 Final submit button clicked");
                              console.log(
                                "📍 Current step:",
                                currentStep,
                                "Total steps:",
                                steps.length
                              );

                              // Validate current step first
                              const isCurrentStepValid =
                                await forceValidateCurrentStep();
                              if (!isCurrentStepValid) {
                                console.warn(
                                  "⚠️ Current step is not valid, cannot submit"
                                );
                                // Toast is already shown by forceValidateCurrentStep, no need for duplicate
                                return;
                              }

                              // Then submit the form
                              formik.handleSubmit();
                            }}
                          >
                            {isSubmitting ? (
                              <>
                                <span
                                  className="spinner-border spinner-border-sm me-2"
                                  role="status"
                                  aria-hidden="true"
                                ></span>
                                Submitting...
                              </>
                            ) : (
                              "Update Profile & KYC"
                            )}
                          </button>
                        ) : (
                          <button
                            type="button"
                            className="btn btn-primary d-flex align-items-center"
                            onClick={handleNext}
                          >
                            Next
                            <FaChevronRight className="ms-1" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </form>
              </FormikProvider>
            </div>
          </div>
        </div>
      </div>
    );
  }
);

ProfileKYCForm.displayName = "ProfileKYCForm";

export default ProfileKYCForm;
