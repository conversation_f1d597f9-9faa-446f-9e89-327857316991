import React from 'react';
import { FaSun, FaMoon } from 'react-icons/fa';
import { useTheme } from '@contexts/ThemeContext';

const ThemeToggle = ({ className = '' }) => {
  const { theme, toggleTheme, isDark } = useTheme();

  return (
    <div className={`theme-toggle-container ${className}`}>
      <button
        className={`theme-toggle-switch ${isDark ? 'dark' : 'light'}`}
        onClick={toggleTheme}
        title={`Switch to ${isDark ? 'light' : 'dark'} mode`}
        aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}
      >
        <div className="theme-toggle-track">
          <div className="theme-toggle-thumb">
            <div className="theme-icon-container">
              <FaSun
                size={12}
                className="theme-icon sun-icon"
              />
              <FaMoon
                size={12}
                className="theme-icon moon-icon"
              />
            </div>
          </div>
          <div className="theme-toggle-background">
            <div className="theme-bg-sun">
              <FaSun size={10} />
            </div>
            <div className="theme-bg-moon">
              <FaMoon size={10} />
            </div>
          </div>
        </div>
      </button>
    </div>
  );
};

export default ThemeToggle;
