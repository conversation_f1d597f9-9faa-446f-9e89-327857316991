import React, { useMemo, useCallback, memo } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { FaHome, FaCog, FaChartBar, FaBoxOpen, FaUser } from "react-icons/fa";
import { Sidebar, Menu, MenuItem, sidebarClasses } from "react-pro-sidebar";
import { getDisplayName } from "@utils/profileUtils";
import { Avatar } from "@components/Common";
import ROUTES from "@constants/routes";

const NewSidebar = memo(({ mobileOpen, onMobileClose }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useSelector((state) => state.user);

  // Memoize display name to prevent recalculation
  const displayName = useMemo(() => getDisplayName(user), [user]);

  // Memoize isActive function to prevent recreation on every render
  const isActive = useCallback(
    (route) => location.pathname === route,
    [location.pathname]
  );

  // Memoize menu click handler to prevent recreation
  const handleMenuClick = useCallback(
    (route) => {
      navigate(route);
      if (mobileOpen && onMobileClose) {
        onMobileClose();
      }
    },
    [navigate, mobileOpen, onMobileClose]
  );

  // Memoize menu item styles to prevent recreation on every render
  const menuItemStyles = useMemo(
    () => ({
      button: ({ active }) => ({
        color: active ? "#ffffff" : "#94a3b8",
        backgroundColor: active ? "rgba(255, 255, 255, 0.12)" : "transparent",
        "&:hover": {
          backgroundColor: "rgba(255, 255, 255, 0.08)",
          color: "#e2e8f0",
        },
        padding: "12px 16px",
        margin: "4px 8px",
        borderRadius: "8px",
        transition: "all 0.2s ease",
      }),
      icon: {
        color: "inherit",
        marginRight: "12px",
      },
      label: {
        fontWeight: "500",
        fontSize: "14px",
      },
    }),
    []
  );

  return (
    <>
      <Sidebar
        toggled={mobileOpen}
        onBackdropClick={onMobileClose}
        breakPoint="lg"
        backgroundColor="var(--bg-global)"
        rootStyles={{
          border: "none",
          borderRight: "none",
          display: "flex",
          flexDirection: "column",
          height: "100%",
          [`.${sidebarClasses.container}`]: {},
        }}
      >
        <div className="p-3">
          <div className="d-flex align-items-center">
            <div
              className="bg-primary rounded d-flex align-items-center justify-content-center flex-shrink-0"
              style={{ width: "32px", height: "32px" }}
            >
              <span>🚚</span>
            </div>
            <div className="ms-3 overflow-hidden">
              <h5 className="mb-0 text-white fw-semibold">Tritrackz</h5>
            </div>
          </div>
        </div>

        <Menu menuItemStyles={menuItemStyles}>
          <MenuItem
            icon={<FaHome size={18} />}
            active={isActive(ROUTES.DASHBOARD) || isActive("/")}
            onClick={() => handleMenuClick(ROUTES.DASHBOARD)}
          >
            Dashboard
          </MenuItem>
          <MenuItem
            icon={<FaUser size={18} />}
            active={isActive("/customers")}
            onClick={() => handleMenuClick("/customers")}
          >
            Customers
          </MenuItem>
          <MenuItem
            icon={<FaBoxOpen size={18} />}
            active={isActive("/shipments")}
            onClick={() => handleMenuClick("/shipments")}
          >
            Shipments
          </MenuItem>
          <MenuItem
            icon={<FaChartBar size={18} />}
            active={isActive("/reports")}
            onClick={() => handleMenuClick("/reports")}
          >
            Reports
          </MenuItem>
        </Menu>

        {/* Bottom section pushed down with flex-grow */}
        <div className="mt-auto position-absolute bottom-0 start-0 end-0">
          <Menu menuItemStyles={menuItemStyles}>
            <MenuItem
              icon={<FaCog size={18} />}
              active={isActive(ROUTES.PROFILE_SETTINGS)}
              onClick={() => handleMenuClick(ROUTES.PROFILE_SETTINGS)}
            >
              Settings
            </MenuItem>
          </Menu>

          <div className="d-flex align-items-center p-3 m-2 rounded-2 bg-secondary bg-opacity-25">
            <Avatar name={displayName} size="small" className="flex-shrink-0" />
            <div className="ms-3 overflow-hidden">
              <div className="fw-semibold text-white text-truncate">
                {displayName}
              </div>
              <div className="small text-muted text-truncate">Admin User</div>
            </div>
          </div>
        </div>
      </Sidebar>
    </>
  );
});

NewSidebar.displayName = "NewSidebar";

export default NewSidebar;
