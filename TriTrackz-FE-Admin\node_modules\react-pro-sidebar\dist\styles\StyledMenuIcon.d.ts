/// <reference types="react" />
import { CSSObject } from '@emotion/styled';
interface StyledMenuIconProps {
    rtl?: boolean;
    rootStyles?: CSSObject;
}
export declare const StyledMenuIcon: import("@emotion/styled").StyledComponent<{
    theme?: import("@emotion/react").Theme | undefined;
    as?: import("react").ElementType<any> | undefined;
} & StyledMenuIconProps, import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, {}>;
export {};
