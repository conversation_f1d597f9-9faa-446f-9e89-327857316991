import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  FaCar,
  FaTools,
  FaHistory,
  FaMapMarkerAlt,
  FaUserPlus,
  FaCalendarAlt,
  FaBell,
  FaQuoteLeft,
  FaFacebook,
  FaTwitter,
  FaInstagram,
  FaLinkedin,
  FaBars,
  FaTimes,
  FaPlay,
  FaArrowRight,
  FaCheckCircle,
  FaStar,
  FaRocket,
  FaShieldAlt,
  FaPhone,
  FaEnvelope,
  FaMapMarked,
  FaPaperPlane,
  FaUser,
  FaBuilding,
  FaQuestionCircle,
  FaPlus,
  FaMinus,
  FaLightbulb,
  FaCreditCard,
  FaUndo,
  FaHeadset,
} from "react-icons/fa";
import {
  MdDirectionsCar,
  MdSpeed,
  MdSecurity,
  MdAnalytics,
} from "react-icons/md";
import ROUTES from "@constants/routes";

// Import logistics videos
import logisticsVideo1 from "@assets/img/IstockBanner1.mp4";
import logisticsVideo2 from "@assets/img/Banner1.mp4";
import logisticsVideo3 from "@assets/img/banner3.mp4";

// Typewriter Hook
const useTypewriter = (text, speed = 50) => {
  const [displayText, setDisplayText] = useState("");
  const [currentIndex, setCurrentIndex] = useState(0);

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText((prev) => prev + text[currentIndex]);
        setCurrentIndex((prev) => prev + 1);
      }, speed);

      return () => clearTimeout(timeout);
    }
  }, [currentIndex, text, speed]);

  return displayText;
};

// Animation Hook for Intersection Observer
const useIntersectionObserver = (options = {}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const elementRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !hasAnimated) {
          setIsVisible(true);
          setHasAnimated(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    const currentElement = elementRef.current;
    if (currentElement) {
      observer.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        observer.unobserve(currentElement);
      }
    };
  }, [hasAnimated, options]);

  return [elementRef, isVisible];
};

const LandingPage = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [expandedFAQ, setExpandedFAQ] = useState(null);
  const navigate = useNavigate();

  // Animation refs for each section
  const [heroRef, heroVisible] = useIntersectionObserver();
  const [aboutRef, aboutVisible] = useIntersectionObserver();
  const [statsRef, statsVisible] = useIntersectionObserver();
  const [servicesRef, servicesVisible] = useIntersectionObserver();
  const [howItWorksRef, howItWorksVisible] = useIntersectionObserver();
  const [pricingRef, pricingVisible] = useIntersectionObserver();
  const [testimonialsRef, testimonialsVisible] = useIntersectionObserver();
  const [faqRef, faqVisible] = useIntersectionObserver();
  const [contactRef, contactVisible] = useIntersectionObserver();

  // Typewriter texts
  const mainText = useTypewriter(
    "Transform Your Logistics Management Experience",
    80
  );
  const subText = useTypewriter(
    "Advanced AI-powered platform for modern supply chain operations",
    60
  );

  // Auto-start typewriter after component mounts
  const [startTypewriter, setStartTypewriter] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setStartTypewriter(true), 1000);
    return () => clearTimeout(timer);
  }, []);

  // Services data
  const services = [
    {
      icon: <FaCalendarAlt />,
      title: "Schedule Shipments",
      description:
        "Book your shipment schedules with ease and convenience. Automated scheduling with preferred carriers.",
      color: "#3b82f6",
      features: ["Automated booking", "Carrier selection", "Bulk scheduling", "Priority handling"]
    },
    {
      icon: <FaTools />,
      title: "Track Deliveries",
      description:
        "Monitor your deliveries in real-time with detailed updates and notifications.",
      color: "#10b981",
      features: ["Real-time GPS tracking", "SMS/Email alerts", "Delivery confirmation", "Exception handling"]
    },
    {
      icon: <FaHistory />,
      title: "Shipment History",
      description: "Access complete delivery records and shipment history with detailed analytics.",
      color: "#f59e0b",
      features: ["Complete audit trail", "Performance metrics", "Cost analysis", "Export reports"]
    },
    {
      icon: <FaMapMarkerAlt />,
      title: "Route Optimization",
      description:
        "Find optimal routes and delivery centers with AI-powered logistics planning.",
      color: "#ef4444",
      features: ["AI route planning", "Cost optimization", "Time efficiency", "Multi-stop routing"]
    },
    {
      icon: <MdAnalytics />,
      title: "Analytics & Insights",
      description:
        "Get detailed analytics and insights to optimize your logistics operations.",
      color: "#8b5cf6",
      features: ["Performance dashboards", "Cost analytics", "Trend analysis", "Custom reports"]
    },
    {
      icon: <FaShieldAlt />,
      title: "Secure Operations",
      description:
        "Enterprise-grade security for all your logistics data and operations.",
      color: "#06b6d4",
      features: ["Data encryption", "Secure API", "Compliance ready", "Audit logs"]
    },
    {
      icon: <FaHeadset />,
      title: "24/7 Support",
      description:
        "Round-the-clock support from logistics experts to keep your operations running.",
      color: "#f97316",
      features: ["Live chat support", "Phone assistance", "Email support", "Dedicated account manager"]
    },
    {
      icon: <MdSpeed />,
      title: "Fast Processing",
      description:
        "Lightning-fast processing and instant updates for all your logistics needs.",
      color: "#84cc16",
      features: ["Instant processing", "Real-time updates", "Quick setup", "Fast API responses"]
    },
  ];

  // How it works steps
  const steps = [
    {
      icon: <FaUserPlus />,
      title: "Register Business",
      description:
        "Add your business details and create your profile in minutes.",
      step: "01",
    },
    {
      icon: <FaCalendarAlt />,
      title: "Book Shipment",
      description:
        "Schedule deliveries with certified logistics providers.",
      step: "02",
    },
    {
      icon: <FaBell />,
      title: "Track & Get Notified",
      description:
        "Receive real-time updates and notifications about your shipments.",
      step: "03",
    },
  ];

  // Testimonials data
  const testimonials = [
    {
      name: "Sarah Johnson",
      title: "Logistics Operations Manager",
      company: "Global Supply Solutions",
      city: "New York",
      image:
        "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      quote:
        "TriTrackz has completely transformed how we manage our supply chain. The real-time tracking and automated scheduling have saved us countless hours and reduced operational costs by 35%.",
      rating: 5,
      shipments: "2,500+ Monthly",
      businessType: "E-commerce Fulfillment",
      costSaving: "35%",
      efficiency: "60%",
    },
    {
      name: "Mr Prakash",
      title: "Supply Chain Director",
      company: "Pacific Logistics Group",
      city: "Los Angeles",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      quote:
        "The analytics and reporting features are incredible. We can track every shipment detail and make informed decisions about our logistics operations. The predictive analytics have prevented major delays.",
      rating: 5,
      shipments: "1,800+ Monthly",
      businessType: "Manufacturing",
      costSaving: "42%",
      efficiency: "55%",
    },
    {
      name: "Emily Rodriguez",
      title: "Transportation Coordinator",
      company: "Metro Delivery Services",
      city: "Chicago",
      image:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      quote:
        "Finding reliable carriers was always a challenge. TriTrackz's carrier network and route optimization has been a game-changer for our business operations and customer satisfaction.",
      rating: 5,
      shipments: "1,200+ Monthly",
      businessType: "Last-Mile Delivery",
      costSaving: "38%",
      efficiency: "65%",
    },
  ];



  // FAQ data
  const faqData = [
    {
      id: 1,
      icon: <FaCreditCard />,
      question: "Can I change plans anytime?",
      answer:
        "Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately and you'll only be charged the prorated difference. No hidden fees or penalties for switching plans.",
    },
    {
      id: 2,
      icon: <FaLightbulb />,
      question: "Is there a setup fee?",
      answer:
        "No setup fees whatsoever! Start using TriTrackz immediately after signing up. We believe in transparent pricing with no hidden costs or surprise charges.",
    },
    {
      id: 3,
      icon: <FaShieldAlt />,
      question: "How secure is my shipment data?",
      answer:
        "We use enterprise-grade security with 256-bit SSL encryption, regular security audits, and comply with GDPR and SOC 2 standards. Your data is stored in secure, redundant data centers with 24/7 monitoring.",
    },
    {
      id: 4,
      icon: <FaUndo />,
      question: "Do you offer refunds?",
      answer:
        "Yes, we offer a 30-day money-back guarantee for all paid plans. If you're not completely satisfied with our logistics platform, we'll refund your payment in full, no questions asked.",
    },
    {
      id: 5,
      icon: <FaHeadset />,
      question: "What kind of support do you provide?",
      answer:
        "We provide 24/7 customer support via live chat, email, and phone. Our logistics experts are always ready to help you optimize your supply chain operations with free onboarding and training sessions.",
    },
    {
      id: 6,
      icon: <FaTools />,
      question: "Can I integrate with my existing systems?",
      answer:
        "Absolutely! TriTrackz offers robust API integrations with popular ERP, WMS, and e-commerce platforms. Our technical team provides full support for custom integrations and data migration.",
    },
    {
      id: 7,
      icon: <FaMapMarkerAlt />,
      question: "Do you support international shipping?",
      answer:
        "Yes, we support global logistics operations with international carrier networks, customs documentation, and multi-currency support. Track shipments worldwide with real-time updates.",
    },
    {
      id: 8,
      icon: <FaCalendarAlt />,
      question: "How quickly can I get started?",
      answer:
        "You can be up and running in minutes! Simply sign up, add your business details, and start booking shipments immediately. Our onboarding team will help you optimize your setup within 24 hours.",
    },
  ];

  // Handle FAQ expansion
  const toggleFAQ = (faqId) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  return (
    <div className="landing-page">
      {/* Navbar */}
      <nav
        className="navbar navbar-expand-lg navbar-dark fixed-top"
        style={{
          background: "rgba(15, 23, 42, 0.95)",
          backdropFilter: "blur(20px)",
          borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
          padding: "1rem 0",
        }}
      >
        <div className="container">
          <Link className="navbar-brand fw-bold fs-3" to={ROUTES.HOME}>
            <MdDirectionsCar className="me-2" style={{ color: "#3b82f6" }} />
            Tri<span className="text-primary fw-bold fs-3">Track</span>z
          </Link>

          <button
            className="navbar-toggler border-0"
            type="button"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? <FaTimes /> : <FaBars />}
          </button>

          <div
            className={`collapse navbar-collapse ${isMenuOpen ? "show" : ""}`}
          >
            <ul className="navbar-nav mx-auto">
              {[
                "Home",
                "About Us",
                "Services",
                "How It Works",
                "Subscriptions",
                "Testimonials",
                "Contact",
              ].map((item, index) => (
                <li key={index} className="nav-item mx-2">
                  <a
                    className="nav-link fw-medium position-relative"
                    href={`#${item.toLowerCase().replace(" ", "-")}`}
                    style={{ transition: "all 0.3s ease" }}
                    onMouseEnter={(e) => {
                      e.target.style.color = "#3b82f6";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.color = "rgba(255, 255, 255, 0.9)";
                    }}
                  >
                    {item}
                  </a>
                </li>
              ))}
            </ul>

            <button
              className="btn btn-primary px-4 py-2 fw-medium"
              onClick={() => navigate(ROUTES.LOGIN)}
              style={{
                background: "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
                border: "none",
                borderRadius: "12px",
                boxShadow: "0 4px 15px rgba(59, 130, 246, 0.3)",
                transition: "all 0.3s ease",
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = "scale(1.05)";
                e.target.style.boxShadow = "0 0 25px rgba(59, 130, 246, 0.5)";
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = "scale(1)";
                e.target.style.boxShadow = "0 4px 15px rgba(59, 130, 246, 0.3)";
              }}
            >
              Login
            </button>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section
        ref={heroRef}
        id="home"
        className={`automotive-hero-section position-relative overflow-hidden ${
          heroVisible ? 'animate-fade-in' : 'animate-initial'
        }`}
        style={{ minHeight: "100vh" }}
      >
        {/* Video Background */}
        <div className="video-background">
          <video autoPlay muted loop playsInline className="hero-video">
            <source
              src={logisticsVideo1}
              type="video/mp4"
            />
            <source
              src={logisticsVideo2}
              type="video/mp4"
            />
            <source
              src={logisticsVideo3}
              type="video/mp4"
            />
          </video>

          {/* Video Overlay */}
          <div className="video-overlay"></div>
        </div>

        {/* Optimized Particles - Reduced count for better performance */}
        <div className="particles-container">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="particle"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
              }}
            />
          ))}
        </div>

        {/* Hero Content */}
        <div className="container position-relative hero-content-wrapper">
          <div className="row min-vh-100 align-items-center justify-content-center">
            <div className="col-lg-10 col-xl-8 text-center">
              <div className="hero-content-center">
                {/* Hero Badge */}
                <div className="automotive-badge mb-4">
                  <div className="badge-icon">
                    <MdDirectionsCar size={20} />
                  </div>
                  <span>Next-Generation Logistics Management</span>
                  <div className="badge-glow"></div>
                </div>

                {/* Main Heading */}
                <h1 className="automotive-hero-title mb-4">
                  Transform Your Logistics Management Experience
                </h1>

                {/* Subtitle */}
                <p className="automotive-hero-subtitle mb-5">
                  Advanced AI-powered platform for modern supply chain operations
                </p>

                {/* CTA Buttons */}
                <div className="automotive-cta-section">
                  <button
                    className="btn-automotive-primary me-3 mb-3"
                    onClick={() => navigate(ROUTES.LOGIN)}
                  >
                    <span>Start Your Journey</span>
                    <div className="btn-arrow">
                      <FaArrowRight />
                    </div>
                    <div className="btn-glow"></div>
                  </button>
                </div>
              </div>
              <div className="automotive-scroll-indicator">
                <div className="scroll-wheel">
                  <div className="scroll-dot" />
                </div>
                <div className="scroll-text">Discover More</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Us Section - Simple Clean Design */}
      <section
        ref={aboutRef}
        id="about-us"
        className={`about-section-simple py-5 ${
          aboutVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
      >
        <div className="container py-5">

          {/* Who We Are Section */}
          <div className="row align-items-center mb-5">
            <div className="col-lg-6">
              <div className="about-content-simple">
                <h2 className="about-title-simple mb-4">Who We Are</h2>
                <p className="about-text-simple mb-4">
                  TriTrackz is a leading logistics technology company dedicated to transforming
                  how businesses manage their supply chain operations. Founded in 2024, we provide
                  innovative solutions that streamline logistics processes and enhance operational efficiency.
                </p>
                <p className="about-text-simple">
                  Our platform serves thousands of businesses worldwide, from small logistics operators
                  to large enterprises, helping them reduce costs, improve delivery times, and gain
                  complete visibility into their operations.
                </p>
              </div>
            </div>
            <div className="col-lg-6">
              <div className="about-stats-simple">
                <div className="row g-4">
                  <div className="col-6">
                    <div className="stat-item-simple text-center">
                      <div className="stat-number-simple">25K+</div>
                      <div className="stat-label-simple">Shipments Delivered</div>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-item-simple text-center">
                      <div className="stat-number-simple">500+</div>
                      <div className="stat-label-simple">Enterprise Clients</div>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-item-simple text-center">
                      <div className="stat-number-simple">99.9%</div>
                      <div className="stat-label-simple">System Uptime</div>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="stat-item-simple text-center">
                      <div className="stat-number-simple">24/7</div>
                      <div className="stat-label-simple">Expert Support</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Why Choose Us Section */}
          <div className="why-choose-section-simple">
            <div className="text-center mb-5">
              <h2 className="about-title-simple mb-4">Why Choose TriTrackz?</h2>
              <p className="about-subtitle-simple mx-auto">
                Discover what makes us the preferred choice for logistics management
              </p>
            </div>

            <div className="row g-4">
              <div className="col-lg-3 col-md-6">
                <div
                  className={`feature-card-simple text-center ${
                    aboutVisible ? 'animate-fade-in-up' : 'animate-initial'
                  }`}
                  style={{
                    animationDelay: aboutVisible ? '0.1s' : '0s'
                  }}
                >
                  <div className="feature-icon-simple mb-3">
                    <MdSpeed />
                  </div>
                  <h5 className="feature-title-simple mb-3">Lightning Fast</h5>
                  <p className="feature-desc-simple">Instant processing and real-time updates for all your logistics operations</p>
                </div>
              </div>

              <div className="col-lg-3 col-md-6">
                <div
                  className={`feature-card-simple text-center ${
                    aboutVisible ? 'animate-fade-in-up' : 'animate-initial'
                  }`}
                  style={{
                    animationDelay: aboutVisible ? '0.2s' : '0s'
                  }}
                >
                  <div className="feature-icon-simple mb-3">
                    <MdSecurity />
                  </div>
                  <h5 className="feature-title-simple mb-3">Secure & Reliable</h5>
                  <p className="feature-desc-simple">Enterprise-grade security with 99.9% uptime guarantee</p>
                </div>
              </div>

              <div className="col-lg-3 col-md-6">
                <div
                  className={`feature-card-simple text-center ${
                    aboutVisible ? 'animate-fade-in-up' : 'animate-initial'
                  }`}
                  style={{
                    animationDelay: aboutVisible ? '0.3s' : '0s'
                  }}
                >
                  <div className="feature-icon-simple mb-3">
                    <MdAnalytics />
                  </div>
                  <h5 className="feature-title-simple mb-3">Smart Analytics</h5>
                  <p className="feature-desc-simple">AI-powered insights and predictive analytics for better decisions</p>
                </div>
              </div>

              <div className="col-lg-3 col-md-6">
                <div
                  className={`feature-card-simple text-center ${
                    aboutVisible ? 'animate-fade-in-up' : 'animate-initial'
                  }`}
                  style={{
                    animationDelay: aboutVisible ? '0.4s' : '0s'
                  }}
                >
                  <div className="feature-icon-simple mb-3">
                    <FaHeadset />
                  </div>
                  <h5 className="feature-title-simple mb-3">24/7 Support</h5>
                  <p className="feature-desc-simple">Always available expert assistance when you need it most</p>
                </div>
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* Statistics Section */}
      <section
        ref={statsRef}
        className={`py-5 ${
          statsVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
        style={{ background: "#0f172a" }}
      >
        <div className="container py-5">
          <div className="text-center mb-5">
            <div className="stats-badge mb-4">
              <MdAnalytics className="me-2" />
              Our Impact
            </div>
            <h2 className="display-5 fw-bold text-white mb-4">
              Trusted by Industry Leaders
            </h2>
            <p className="lead text-white-50">
              Numbers that speak for our logistics excellence
            </p>
          </div>

          <div className="row g-4">
            <div className="col-lg-3 col-md-6">
              <div
                className={`stat-card-modern ${
                  statsVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: statsVisible ? '0.1s' : '0s'
                }}
              >
                <div className="stat-icon-modern">
                  <FaCar />
                </div>
                <div className="stat-number-modern">25K+</div>
                <div className="stat-label-modern">Shipments Delivered</div>
                <div className="stat-description">Successfully delivered across global networks</div>
              </div>
            </div>

            <div className="col-lg-3 col-md-6">
              <div
                className={`stat-card-modern ${
                  statsVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: statsVisible ? '0.2s' : '0s'
                }}
              >
                <div className="stat-icon-modern">
                  <FaBuilding />
                </div>
                <div className="stat-number-modern">500+</div>
                <div className="stat-label-modern">Enterprise Clients</div>
                <div className="stat-description">Businesses trust our logistics platform</div>
              </div>
            </div>

            <div className="col-lg-3 col-md-6">
              <div
                className={`stat-card-modern ${
                  statsVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: statsVisible ? '0.3s' : '0s'
                }}
              >
                <div className="stat-icon-modern">
                  <MdSpeed />
                </div>
                <div className="stat-number-modern">99.9%</div>
                <div className="stat-label-modern">System Uptime</div>
                <div className="stat-description">Reliable platform performance guaranteed</div>
              </div>
            </div>

            <div className="col-lg-3 col-md-6">
              <div
                className={`stat-card-modern ${
                  statsVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: statsVisible ? '0.4s' : '0s'
                }}
              >
                <div className="stat-icon-modern">
                  <FaShieldAlt />
                </div>
                <div className="stat-number-modern">24/7</div>
                <div className="stat-label-modern">Expert Support</div>
                <div className="stat-description">Round-the-clock logistics assistance</div>
              </div>
            </div>
          </div>

          {/* Company Values */}
          <div className="values-section mt-5 pt-5">
            <div className="text-center mb-5">
              <h3 className="display-6 fw-bold text-white mb-4">Our Core Values</h3>
              <p className="lead text-white-50">
                The principles that drive our logistics excellence
              </p>
            </div>

            <div className="row g-4">
              <div className="col-lg-4">
                <div className="value-card-modern">
                  <div className="value-icon-modern">
                    <FaCheckCircle />
                  </div>
                  <h5 className="value-title">Excellence</h5>
                  <p className="value-description">
                    Delivering superior quality in every logistics solution and exceeding customer expectations consistently.
                  </p>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="value-card-modern">
                  <div className="value-icon-modern">
                    <FaRocket />
                  </div>
                  <h5 className="value-title">Innovation</h5>
                  <p className="value-description">
                    Pioneering cutting-edge logistics technology and continuously improving our platform capabilities.
                  </p>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="value-card-modern">
                  <div className="value-icon-modern">
                    <FaHeadset />
                  </div>
                  <h5 className="value-title">Support</h5>
                  <p className="value-description">
                    Providing exceptional customer service and dedicated support for all logistics operations.
                  </p>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="value-card-modern">
                  <div className="value-icon-modern">
                    <MdSecurity />
                  </div>
                  <h5 className="value-title">Security</h5>
                  <p className="value-description">
                    Ensuring enterprise-grade security and data protection for all logistics information.
                  </p>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="value-card-modern">
                  <div className="value-icon-modern">
                    <MdSpeed />
                  </div>
                  <h5 className="value-title">Efficiency</h5>
                  <p className="value-description">
                    Optimizing logistics processes for maximum efficiency and cost-effective operations.
                  </p>
                </div>
              </div>

              <div className="col-lg-4">
                <div className="value-card-modern">
                  <div className="value-icon-modern">
                    <FaLightbulb />
                  </div>
                  <h5 className="value-title">Transparency</h5>
                  <p className="value-description">
                    Maintaining clear communication and transparent pricing in all logistics partnerships.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section - Completely Redesigned */}
      <section
        ref={servicesRef}
        id="services"
        className={`services-section-redesigned ${
          servicesVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
      >
        <div className="container">
          {/* Header Section */}
          <div className="services-header">
            <div className="row align-items-center">
              <div className="col-lg-6">
                <div className="services-intro">
                  <span className="services-label">What We Offer</span>
                  <h2 className="services-main-title">
                    Powerful Logistics
                    <span className="title-highlight"> Solutions</span>
                  </h2>
                  <p className="services-subtitle">
                    Transform your supply chain with our comprehensive suite of intelligent logistics tools designed for modern businesses.
                  </p>
                </div>
              </div>
              <div className="col-lg-6">
                <div className="services-stats">
                  <div className="stat-item-new">
                    <div className="stat-number">8+</div>
                    <div className="stat-label">Core Services</div>
                  </div>
                  <div className="stat-item-new">
                    <div className="stat-number">99.9%</div>
                    <div className="stat-label">Uptime</div>
                  </div>
                  <div className="stat-item-new">
                    <div className="stat-number">24/7</div>
                    <div className="stat-label">Support</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Services Grid */}
          <div className="services-grid">
            {services.map((service, index) => (
              <div
                key={index}
                className={`service-item-new ${
                  servicesVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: servicesVisible ? `${index * 0.1}s` : '0s'
                }}
              >
                <div className="service-card-new">
                  {/* Service Number */}
                  <div className="service-number">
                    {String(index + 1).padStart(2, '0')}
                  </div>

                  {/* Service Icon */}
                  <div className="service-icon-new" style={{ '--service-color': service.color }}>
                    {React.cloneElement(service.icon, { size: 24 })}
                  </div>

                  {/* Service Content */}
                  <div className="service-info">
                    <h3 className="service-name">{service.title}</h3>
                    <p className="service-desc">{service.description}</p>

                    {/* Key Features */}
                    <ul className="service-features-new">
                      {service.features.slice(0, 2).map((feature, featureIndex) => (
                        <li key={featureIndex}>{feature}</li>
                      ))}
                    </ul>
                  </div>

                  {/* Action Button */}
                  <div className="service-action">
                    <button className="service-btn" style={{ '--service-color': service.color }}>
                      <span>Explore</span>
                      <FaArrowRight />
                    </button>
                  </div>

                  {/* Background Pattern */}
                  <div className="service-pattern" style={{ '--service-color': service.color }}></div>
                </div>
              </div>
            ))}
          </div>

          {/* Bottom CTA */}
          <div className="services-bottom-cta">
            <div className="cta-content-new">
              <div className="cta-text">
                <h3>Ready to optimize your logistics?</h3>
                <p>Join 500+ companies already using TriTrackz</p>
              </div>
              <div className="cta-actions">
                <button
                  className="cta-btn-primary"
                  onClick={() => navigate(ROUTES.REGISTER)}
                >
                  Get Started Free
                </button>
                <button className="cta-btn-secondary">
                  Book a Demo
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section
        ref={howItWorksRef}
        id="how-it-works"
        className={`py-5 ${
          howItWorksVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
        style={{ background: "#f8fafc" }}
      >
        <div className="container py-5">
          <div className="text-center mb-5">
            <h2 className="display-5 fw-bold text-dark mb-4">How It Works</h2>
            <p className="lead text-muted">
              Get started with TriTrackz in three simple steps
            </p>
          </div>

          <div className="row g-5">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`col-lg-4 ${
                  howItWorksVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: howItWorksVisible ? `${index * 0.2}s` : '0s'
                }}
              >
                <div className="text-center position-relative">
                  <div className="position-relative mb-4">
                    <div
                      className="rounded-circle d-flex align-items-center justify-content-center mx-auto"
                      style={{
                        width: "100px",
                        height: "100px",
                        background:
                          "linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",
                        color: "white",
                      }}
                    >
                      {React.cloneElement(step.icon, { size: 40 })}
                    </div>
                    <div
                      className="position-absolute top-0 end-0 rounded-circle d-flex align-items-center justify-content-center fw-bold"
                      style={{
                        width: "30px",
                        height: "30px",
                        background: "#f59e0b",
                        color: "white",
                        fontSize: "14px",
                      }}
                    >
                      {step.step}
                    </div>
                  </div>
                  <h5 className="fw-bold mb-3">{step.title}</h5>
                  <p className="text-muted">{step.description}</p>

                  {index < steps.length - 1 && (
                    <div
                      className="position-absolute d-none d-lg-block"
                      style={{
                        top: "50px",
                        right: "-50px",
                        width: "100px",
                        height: "2px",
                        background:
                          "linear-gradient(90deg, #3b82f6 0%, transparent 100%)",
                      }}
                    />
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Subscription Plans Section - Redesigned */}
      <section
        ref={pricingRef}
        id="subscriptions"
        className={`pricing-section-modern py-5 ${
          pricingVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
      >
        <div className="container py-5">
          {/* Section Header */}
          <div className="text-center mb-5">
            <div className="pricing-badge-modern mb-4">
              <FaStar className="me-2" />
              Pricing Plans
            </div>
            <h2 className="pricing-title-modern mb-4">
              Choose Your <span className="text-primary">Perfect Plan</span>
            </h2>
            <p className="pricing-subtitle-modern">
              Transparent pricing that scales with your business. Start free and upgrade as you grow.
            </p>
          </div>

          {/* Pricing Toggle */}
          <div className="pricing-toggle-modern text-center mb-5">
            <div className="toggle-container">
              <span className="toggle-label">Monthly</span>
              <div className="toggle-switch">
                <input type="checkbox" id="pricing-toggle" />
                <label htmlFor="pricing-toggle"></label>
              </div>
              <span className="toggle-label">
                Annual <span className="save-badge">Save 20%</span>
              </span>
            </div>
          </div>

          {/* Pricing Cards */}
          <div className="row g-4 justify-content-center">
            {/* Starter Plan */}
            <div className="col-lg-4 col-md-6">
              <div
                className={`pricing-card-modern starter-modern ${
                  pricingVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: pricingVisible ? '0.1s' : '0s'
                }}
              >
                <div className="pricing-header-modern">
                  <div className="plan-icon-modern starter-icon">
                    <FaCar />
                  </div>
                  <h3 className="plan-name-modern">Starter</h3>
                  <p className="plan-desc-modern">Perfect for small businesses getting started</p>
                </div>

                <div className="pricing-amount-modern">
                  <div className="price-modern">
                    <span className="currency-modern">$</span>
                    <span className="amount-modern">0</span>
                    <span className="period-modern">/month</span>
                  </div>
                  <p className="price-note-modern">Free forever • No credit card required</p>
                </div>

                <div className="features-modern">
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Up to 10 shipments per month</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Basic shipment tracking</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Mobile app access</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Email support</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Basic reporting</span>
                  </div>
                </div>

                <button
                  className="pricing-btn-modern starter-btn-modern"
                  onClick={() => navigate(ROUTES.REGISTER)}
                >
                  Get Started Free
                  <FaArrowRight className="btn-arrow" />
                </button>
              </div>
            </div>

            {/* Professional Plan */}
            <div className="col-lg-4 col-md-6">
              <div
                className={`pricing-card-modern professional-modern popular-modern ${
                  pricingVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: pricingVisible ? '0.2s' : '0s'
                }}
              >
                <div className="popular-badge-modern">
                  <FaStar className="me-1" />
                  Most Popular
                </div>

                <div className="pricing-header-modern">
                  <div className="plan-icon-modern professional-icon">
                    <FaTools />
                  </div>
                  <h3 className="plan-name-modern">Professional</h3>
                  <p className="plan-desc-modern">Best for growing logistics businesses</p>
                </div>

                <div className="pricing-amount-modern">
                  <div className="price-modern">
                    <span className="currency-modern">$</span>
                    <span className="amount-modern">49</span>
                    <span className="period-modern">/month</span>
                  </div>
                  <p className="price-note-modern">Billed annually • 14-day free trial</p>
                </div>

                <div className="features-modern">
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Up to 500 shipments per month</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Advanced tracking & analytics</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>API integrations</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Priority support</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Custom reports & dashboards</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Multi-user access</span>
                  </div>
                </div>

                <button
                  className="pricing-btn-modern professional-btn-modern"
                  onClick={() => navigate(ROUTES.REGISTER)}
                >
                  Start Free Trial
                  <FaArrowRight className="btn-arrow" />
                </button>
              </div>
            </div>

            {/* Enterprise Plan */}
            <div className="col-lg-4 col-md-6">
              <div
                className={`pricing-card-modern enterprise-modern ${
                  pricingVisible ? 'animate-fade-in-up' : 'animate-initial'
                }`}
                style={{
                  animationDelay: pricingVisible ? '0.3s' : '0s'
                }}
              >
                <div className="pricing-header-modern">
                  <div className="plan-icon-modern enterprise-icon">
                    <MdDirectionsCar />
                  </div>
                  <h3 className="plan-name-modern">Enterprise</h3>
                  <p className="plan-desc-modern">For large-scale operations</p>
                </div>

                <div className="pricing-amount-modern">
                  <div className="price-modern">
                    <span className="currency-modern">$</span>
                    <span className="amount-modern">149</span>
                    <span className="period-modern">/month</span>
                  </div>
                  <p className="price-note-modern">Custom pricing available</p>
                </div>

                <div className="features-modern">
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Unlimited shipments</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Advanced integrations</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Dedicated account manager</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>Custom features & workflows</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>SLA guarantee (99.9% uptime)</span>
                  </div>
                  <div className="feature-modern">
                    <FaCheckCircle className="check-icon" />
                    <span>White-label options</span>
                  </div>
                </div>

                <button className="pricing-btn-modern enterprise-btn-modern">
                  Contact Sales
                  <FaArrowRight className="btn-arrow" />
                </button>
              </div>
            </div>
          </div>

          {/* Pricing Footer */}
          <div className="pricing-footer-modern text-center mt-5">
            <div className="pricing-guarantee">
              <FaShieldAlt className="guarantee-icon" />
              <div className="guarantee-text">
                <h5>30-Day Money-Back Guarantee</h5>
                <p>Try TriTrackz risk-free. If you're not satisfied, get a full refund within 30 days.</p>
              </div>
            </div>

            <div className="pricing-features-grid mt-5">
              <div className="row g-4">
                <div className="col-md-3 col-6">
                  <div className="pricing-feature-item">
                    <FaShieldAlt className="feature-icon-small" />
                    <span>Secure & Reliable</span>
                  </div>
                </div>
                <div className="col-md-3 col-6">
                  <div className="pricing-feature-item">
                    <FaHeadset className="feature-icon-small" />
                    <span>24/7 Support</span>
                  </div>
                </div>
                <div className="col-md-3 col-6">
                  <div className="pricing-feature-item">
                    <MdSpeed className="feature-icon-small" />
                    <span>Fast Setup</span>
                  </div>
                </div>
                <div className="col-md-3 col-6">
                  <div className="pricing-feature-item">
                    <FaCheckCircle className="feature-icon-small" />
                    <span>No Hidden Fees</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Stories Section - Small Clean Cards */}
      <section
        ref={testimonialsRef}
        id="testimonials"
        className={`py-5 ${
          testimonialsVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
        style={{ background: "#0f172a" }}
      >
        <div className="container py-5">
          <div className="text-center mb-5">
            <div className="testimonial-badge mb-4">
              <FaQuoteLeft className="me-2" />
              Customer Stories
            </div>
            <h2 className="display-5 fw-bold text-white mb-4">
              What Our Clients Say
            </h2>
            <p className="lead text-white-50 mb-5">
              Real experiences from logistics professionals worldwide
            </p>
          </div>

          {/* Small Testimonial Cards Grid */}
          <div className="row g-4 mb-5">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="col-lg-4 col-md-6">
                <div
                  className={`testimonial-card-small ${
                    testimonialsVisible ? 'animate-fade-in-up' : 'animate-initial'
                  }`}
                  style={{
                    animationDelay: testimonialsVisible ? `${index * 0.2}s` : '0s'
                  }}
                >
                  {/* Quote Icon */}
                  <div className="quote-icon-small">
                    <FaQuoteLeft />
                  </div>

                  {/* Testimonial Quote */}
                  <blockquote className="testimonial-quote-small">
                    "{testimonial.quote.length > 120 ? testimonial.quote.substring(0, 120) + '...' : testimonial.quote}"
                  </blockquote>

                  {/* Rating Stars */}
                  <div className="rating-small mb-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <FaStar key={i} className="star-small" />
                    ))}
                  </div>

                  {/* Author Info */}
                  <div className="author-section-small">
                    <div className="author-image-small">
                      <img
                        src={testimonial.image}
                        alt={testimonial.name}
                        className="author-avatar-small"
                      />
                    </div>
                    <div className="author-details-small">
                      <h6 className="author-name-small">{testimonial.name}</h6>
                      <p className="author-title-small">{testimonial.title}</p>
                      <p className="author-company-small">{testimonial.company}</p>
                    </div>
                  </div>

                  {/* Key Stats */}
                  <div className="stats-small">
                    <div className="stat-small">
                      <span className="stat-value-small">{testimonial.costSaving}</span>
                      <span className="stat-label-small">Cost Saved</span>
                    </div>
                    <div className="stat-small">
                      <span className="stat-value-small">{testimonial.efficiency}</span>
                      <span className="stat-label-small">Efficiency</span>
                    </div>
                  </div>

                  {/* Business Type Badge */}
                  <div className="business-type-badge">
                    {testimonial.businessType}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Client Logos */}
          <div className="client-logos mt-5">
            <div className="text-center mb-4">
              <h5 className="text-white-50">Trusted by leading companies</h5>
            </div>
            <div className="logos-grid">
              <div className="logo-item">
                <div className="logo-placeholder">
                  <FaBuilding size={24} />
                  <span>Global Supply</span>
                </div>
              </div>
              <div className="logo-item">
                <div className="logo-placeholder">
                  <FaBuilding size={24} />
                  <span>Pacific Logistics</span>
                </div>
              </div>
              <div className="logo-item">
                <div className="logo-placeholder">
                  <FaBuilding size={24} />
                  <span>Metro Delivery</span>
                </div>
              </div>
              <div className="logo-item">
                <div className="logo-placeholder">
                  <FaBuilding size={24} />
                  <span>Express Freight</span>
                </div>
              </div>
              <div className="logo-item">
                <div className="logo-placeholder">
                  <FaBuilding size={24} />
                  <span>Swift Transport</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section
        ref={faqRef}
        className={`simple-faq-section py-5 ${
          faqVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
      >
        <div className="container py-5">
          <div className="row justify-content-center">
            <div className="col-lg-8">
              {/* FAQ Header */}
              <div className="text-center mb-5">
                <div className="faq-badge mb-4">
                  <FaQuestionCircle className="me-2" />
                  Frequently Asked Questions
                </div>
                <h2 className="display-5 fw-bold text-dark mb-4">
                  Got Questions? We've Got Answers
                </h2>
                <p className="lead text-muted">
                  Find answers to common questions about TriTrackz logistics platform
                </p>
              </div>

              {/* FAQ Items */}
              <div className="simple-faq-list">
                {faqData.map((faq, index) => (
                  <div
                    key={faq.id}
                    className={`simple-faq-item ${
                      faqVisible ? 'animate-fade-in-up' : 'animate-initial'
                    }`}
                    style={{
                      animationDelay: faqVisible ? `${index * 0.1}s` : '0s'
                    }}
                  >
                    <div
                      className="faq-question-header"
                      onClick={() => toggleFAQ(faq.id)}
                    >
                      <div className="faq-question-content">
                        <div className="faq-icon-simple">{faq.icon}</div>
                        <h5 className="faq-question-title">
                          {faq.question}
                        </h5>
                      </div>
                      <div className="faq-toggle-simple">
                        {expandedFAQ === faq.id ? <FaMinus /> : <FaPlus />}
                      </div>
                    </div>

                    {expandedFAQ === faq.id && (
                      <div className="faq-answer-simple">
                        <div className="faq-answer-content-simple">
                          <p>{faq.answer}</p>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {/* Simple CTA */}
              <div className="text-center mt-5">
                <div className="simple-faq-cta">
                  <h5 className="fw-bold mb-3">Still have questions?</h5>
                  <p className="text-muted mb-4">
                    Our logistics experts are here to help you get started
                  </p>
                  <div className="d-flex gap-3 justify-content-center flex-wrap">
                    <button
                      className="btn btn-primary px-4 py-2"
                      onClick={() => navigate(ROUTES.REGISTER)}
                    >
                      Get Started Free
                    </button>
                    <button className="btn btn-outline-primary px-4 py-2">
                      Contact Support
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Get In Touch Section - Trendy Design */}
      <section
        ref={contactRef}
        id="contact"
        className={`contact-section-trendy py-5 ${
          contactVisible ? 'animate-fade-in-up' : 'animate-initial'
        }`}
      >
        <div className="container py-5">

          {/* Section Header */}
          <div className="text-center mb-5">
            <div className="contact-badge mb-4">
              <FaEnvelope className="me-2" />
              Get In Touch
            </div>
            <h2 className="contact-title mb-4">
              Let's Start a <span className="text-gradient-contact">Conversation</span>
            </h2>
            <p className="contact-subtitle mx-auto">
              Ready to transform your logistics operations? We'd love to hear from you.
              Send us a message and we'll respond within 24 hours.
            </p>
          </div>

          <div className="row g-5 align-items-start">

            {/* Contact Form */}
            <div className="col-lg-8">
              <div className="contact-form-card">
                <div className="form-header mb-4">
                  <h4 className="landig-form-title">Send us a Message</h4>
                  <p className="form-subtitle">Fill out the form below and we'll get back to you soon</p>
                </div>

                <form className="contact-form">
                  <div className="row g-4">
                    <div className="col-md-6">
                      <div className="form-group-trendy">
                        <label className="form-label-trendy">
                          <FaUser className="label-icon" />
                          Full Name
                        </label>
                        <input
                          type="text"
                          className="form-input-trendy"
                          placeholder="Enter your full name"
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="form-group-trendy">
                        <label className="form-label-trendy">
                          <FaEnvelope className="label-icon" />
                          Email Address
                        </label>
                        <input
                          type="email"
                          className="form-input-trendy"
                          placeholder="Enter your email address"
                          required
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="form-group-trendy">
                        <label className="form-label-trendy">
                          <FaPhone className="label-icon" />
                          Phone Number
                        </label>
                        <input
                          type="tel"
                          className="form-input-trendy"
                          placeholder="Enter your phone number"
                        />
                      </div>
                    </div>
                    <div className="col-md-6">
                      <div className="form-group-trendy">
                        <label className="form-label-trendy">
                          <FaBuilding className="label-icon" />
                          Company Name
                        </label>
                        <input
                          type="text"
                          className="form-input-trendy"
                          placeholder="Enter your company name"
                        />
                      </div>
                    </div>
                    <div className="col-12">
                      <div className="form-group-trendy">
                        <label className="form-label-trendy">
                          <FaPaperPlane className="label-icon" />
                          Message
                        </label>
                        <textarea
                          className="form-textarea-trendy"
                          rows="5"
                          placeholder="Tell us about your logistics needs and how we can help..."
                          required
                        ></textarea>
                      </div>
                    </div>
                  </div>

                  <div className="form-actions mt-4">
                    <button type="submit" className="btn-contact-primary">
                      <FaPaperPlane className="me-2" />
                      Send Message
                    </button>
                    <p className="form-note mt-3">
                      By submitting this form, you agree to our privacy policy and terms of service.
                    </p>
                  </div>
                </form>
              </div>
            </div>

            {/* Contact Info Sidebar */}
            <div className="col-lg-4">
              <div className="contact-info-card">
                <div className="info-header mb-4">
                  <h4 className="info-title">Contact Information</h4>
                  <p className="info-subtitle">Get in touch with us through any of these channels</p>
                </div>

                <div className="contact-info-list">
                  <div className="Landing-info-item">
                    <div className="info-icon">
                      <FaPhone />
                    </div>
                    <div className="info-content">
                      <h6 className="info-label">Phone</h6>
                      <p className="info-value">+****************</p>
                      <span className="info-note">Mon-Fri 9AM-6PM EST</span>
                    </div>
                  </div>

                  <div className="Landing-info-item">
                    <div className="info-icon">
                      <FaEnvelope />
                    </div>
                    <div className="info-content">
                      <h6 className="info-label">Email</h6>
                      <p className="info-value"><EMAIL></p>
                      <span className="info-note">We'll respond within 24 hours</span>
                    </div>
                  </div>

                  <div className="Landing-info-item">
                    <div className="info-icon">
                      <FaMapMarked />
                    </div>
                    <div className="info-content">
                      <h6 className="info-label">Address</h6>
                      <p className="info-value">123 Logistics Avenue<br />Tech City, TC 12345</p>
                      <span className="info-note">Visit us during business hours</span>
                    </div>
                  </div>

                  <div className="Landing-info-item">
                    <div className="info-icon">
                      <FaHeadset />
                    </div>
                    <div className="info-content">
                      <h6 className="info-label">24/7 Support</h6>
                      <p className="info-value"><EMAIL></p>
                      <span className="info-note">Emergency support available</span>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="quick-actions mt-4">
                  <h6 className="quick-title">Quick Actions</h6>
                  <div className="action-buttons">
                    <button
                      className="btn-action-secondary"
                      onClick={() => navigate(ROUTES.REGISTER)}
                    >
                      <FaArrowRight className="me-2" />
                      Start Free Trial
                    </button>
                    <button className="btn-action-outline">
                      <FaCalendarAlt className="me-2" />
                      Schedule Demo
                    </button>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </section>

      {/* Footer - Detailed Design with Background */}
      <footer className="footer-detailed">
        {/* Background Image with Overlay */}
        <div className="footer-background">
          <div className="footer-overlay"></div>
        </div>

        <div className="container position-relative">
          {/* Main Footer Content */}
          <div className="footer-main py-5">
            <div className="row g-4">

              {/* Company Info */}
              <div className="col-lg-4 col-md-6">
                <div className="footer-section">
                  <div className="footer-brand mb-4">
                    <MdDirectionsCar className="me-2" style={{ color: "#3b82f6" }} size={32} />
                    <span className="footer-brand-text">
                      Tri<span className="text-primary">Track</span>z
                    </span>
                  </div>
                  <p className="footer-description mb-4">
                    Leading logistics technology company transforming supply chain operations
                    with innovative AI-powered solutions for businesses worldwide.
                  </p>
                  <div className="footer-contact">
                    <div className="contact-item mb-2">
                      <FaPhone className="contact-icon me-2" />
                      <span>+****************</span>
                    </div>
                    <div className="contact-item mb-2">
                      <FaEnvelope className="contact-icon me-2" />
                      <span><EMAIL></span>
                    </div>
                    <div className="contact-item">
                      <FaMapMarked className="contact-icon me-2" />
                      <span>123 Logistics Ave, Tech City, TC 12345</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="col-lg-2 col-md-6">
                <div className="footer-section">
                  <h5 className="footer-title mb-4">Quick Links</h5>
                  <ul className="footer-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about-us">About Us</a></li>
                    <li><a href="#services">Services</a></li>
                    <li><a href="#pricing">Pricing</a></li>
                    <li><a href="#contact">Contact</a></li>
                  </ul>
                </div>
              </div>

              {/* Services */}
              <div className="col-lg-2 col-md-6">
                <div className="footer-section">
                  <h5 className="footer-title mb-4">Services</h5>
                  <ul className="footer-links">
                    <li><a href="#">Fleet Management</a></li>
                    <li><a href="#">Route Optimization</a></li>
                    <li><a href="#">Real-time Tracking</a></li>
                    <li><a href="#">Analytics & Reports</a></li>
                    <li><a href="#">API Integration</a></li>
                  </ul>
                </div>
              </div>

              {/* Support */}
              <div className="col-lg-2 col-md-6">
                <div className="footer-section">
                  <h5 className="footer-title mb-4">Support</h5>
                  <ul className="footer-links">
                    <li><a href="#">Help Center</a></li>
                    <li><a href="#">Documentation</a></li>
                    <li><a href="#">API Docs</a></li>
                    <li><a href="#">System Status</a></li>
                    <li><a href="#">24/7 Support</a></li>
                  </ul>
                </div>
              </div>

              {/* Newsletter */}
              <div className="col-lg-2 col-md-6">
                <div className="footer-section">
                  <h5 className="footer-title mb-4">Stay Updated</h5>
                  <p className="footer-newsletter-text mb-3">
                    Subscribe to get the latest updates and logistics insights.
                  </p>
                  <div className="footer-newsletter">
                    <div className="newsletter-input-group">
                      <input
                        type="email"
                        className="newsletter-input"
                        placeholder="Enter your email"
                      />
                      <button className="newsletter-btn">
                        <FaPaperPlane />
                      </button>
                    </div>
                  </div>

                  {/* Social Links */}
                  <div className="footer-social mt-4">
                    <a href="#" className="social-link">
                      <FaFacebook />
                    </a>
                    <a href="#" className="social-link">
                      <FaTwitter />
                    </a>
                    <a href="#" className="social-link">
                      <FaInstagram />
                    </a>
                    <a href="#" className="social-link">
                      <FaLinkedin />
                    </a>
                  </div>
                </div>
              </div>

            </div>
          </div>

          {/* Footer Bottom */}
          <div className="footer-bottom py-4">
            <div className="row align-items-center">
              <div className="col-md-6">
                <p className="footer-copyright mb-0">
                  © 2024 TriTrackz. All rights reserved.
                </p>
              </div>
              <div className="col-md-6">
                <div className="footer-legal">
                  <a href="#" className="legal-link">Privacy Policy</a>
                  <a href="#" className="legal-link">Terms of Service</a>
                  <a href="#" className="legal-link">Cookie Policy</a>
                </div>
              </div>
            </div>
          </div>

        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
