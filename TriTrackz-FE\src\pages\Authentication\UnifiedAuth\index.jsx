import React, { useState, useEffect, useRef } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  setPhoneNumber,
  setPhoneVerified,
  setOtpRequestId,
  setSelectedUserType,
  setUser,
  setToken,
  setProfileStatus,
  setHasExistingCredentials,
} from "@store/userSlice";
import toast from "react-hot-toast";
import { showToast } from "@utils/toastUtils";
import ROUTES from "@constants/routes";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FaArrowRight, FaShieldAlt, FaCheck } from "react-icons/fa";
import { useMobileRegister, useVerifyOTP, useCheckUserTypes } from "@api/authHooks";
import UserTypeSelector from "@components/UserTypeSelector";


const UnifiedAuth = () => {
  const [step, setStep] = useState(1); 
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [timeLeft, setTimeLeft] = useState(600); // 10 minutes
  const [canResend, setCanResend] = useState(false);
  const [receivedOtp, setReceivedOtp] = useState(null);
  const [lastPayload, setLastPayload] = useState(null); // Store last payload for resend
  const [existingUserTypes, setExistingUserTypes] = useState([]); // Store existing user types
  const [showUserTypeConfirmation, setShowUserTypeConfirmation] = useState(false); // Show confirmation dialog
  const [showLoginOptions, setShowLoginOptions] = useState(false); // Show login options (OTP vs Password)
  const [MaskNumber, setMaskNumber] = useState(); // Show back button

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const inputRefs = useRef([]);

  // React Query hooks
  const checkUserTypesMutation = useCheckUserTypes({
    onSuccess: (response) => {
      if (response.success && response.userTypes && response.userTypes.length > 0) {
        // Check if selected user type already exists in the user's registered types
        const isExistingUserType = response.userTypes.includes(selectedUserType);

        if (isExistingUserType) {
          // User type already exists, show login options (OTP vs Password)
          setMaskNumber(response.mobileNumber);
          setStep(4); // Step 4: Login Options
        } else {
          // User type doesn't exist, show confirmation for new registration
          // Set hasExistingCredentials to true since user has other user types
          dispatch(setHasExistingCredentials(true));
          setExistingUserTypes(response.userTypes);
          setStep(5); // Step 5: User Type Confirmation
        }
      } else {
        // No existing registrations, proceed with OTP
        dispatch(setHasExistingCredentials(false));
        proceedWithOTP();
      }
    },
    onError: (error) => {
      // If API fails, proceed with OTP (assume no existing registrations)
      dispatch(setHasExistingCredentials(false));
      proceedWithOTP();
    }
  });

  const mobileRegisterMutation = useMobileRegister({
    onSuccess: (response) => {
      if (response.success) {
        // Store the user ID as request ID for OTP verification
        dispatch(setOtpRequestId(response.userId));

        // Store received OTP for display
        if (response.otp) {
          setReceivedOtp(response.otp);
        }

        showToast.success(response.message || "OTP sent to your registered mobile number. Please use the OTP to verify and complete your registration.");
        setStep(3);
        setTimeLeft(600); // Always set to 10 minutes
        setCanResend(false);
      } else {
        toast.error(response.message || "Failed to send OTP");
      }
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || "Failed to send OTP");
    }
  });

  const verifyOTPMutation = useVerifyOTP({
    onSuccess: (response) => {

      if (response.accessToken) {
        dispatch(setPhoneVerified(true));
        dispatch(setToken(response.accessToken));

        // Check if current userType exists in otherUserTypes array
        const currentUserTypeExists = response.otherUserTypes &&
          Array.isArray(response.otherUserTypes) &&
          response.otherUserTypes.includes(selectedUserType);

        // Additional check: if userType and otherUserTypes contain the same value
        const userTypeMatchesOtherTypes = response.userType === selectedUserType &&
          response.otherUserTypes &&
          Array.isArray(response.otherUserTypes) &&
          response.otherUserTypes.includes(response.userType);

        // Check if user has existing credentials (password field present)
        const hasExistingCredentials = Boolean(response.password);

        if (currentUserTypeExists || userTypeMatchesOtherTypes) {

          const userData = {
            id: response.userId,
            firstName: response.firstName,
            lastName: response.lastName,
            email: response.email,
            mobileNumber: response.mobileNumber,
            userType: selectedUserType, // Use the selected user type
            phoneNumber: phoneNumber,
            // Additional fields from response
            username: response.username,
            accessToken: response.accessToken,
            refreshToken: response.refreshToken
          };

          dispatch(setUser(userData));
          dispatch(setToken(response.accessToken));

          // Set profile status as complete since user has existing data
          dispatch(setProfileStatus({
            isProfileCompleted: true,
            isKYCCompleted: true,
            profileCompletionPercentage: 100
          }));

          showToast.success(`Welcome back ${response.firstName}! Logged in as ${getUserTypeLabel(selectedUserType)}`);
          navigate(ROUTES.DASHBOARD);
        } else {
          // Check if user has existing credentials
          // If user has otherUserTypes, they already have credentials and shouldn't see password fields
          const hasExistingCredentials = Boolean(
            response.otherUserTypes &&
            Array.isArray(response.otherUserTypes) &&
            response.otherUserTypes.length > 0
          );

          dispatch(setHasExistingCredentials(hasExistingCredentials));

          // Check if basic user information is available
          if (response.firstName) {
            // User has basic info, prefill registration form
            toast.success("Please complete your profile for the selected role.");
            navigate(ROUTES.BASIC_INFO, {
              state: {
                prefillData: {
                  firstName: response.firstName,
                  lastName: response.lastName || '',
                  email: response.email || '',
                  phoneNumber: phoneNumber,
                  userId: response.userId,
                  userType: selectedUserType,
                  hasExistingCredentials: hasExistingCredentials
                }
              }
            });
          } else {
            // No basic info, show registration form
            toast.success("Mobile number verified! Please complete your profile.");
            navigate(ROUTES.BASIC_INFO, {
              state: {
                userData: {
                  userId: response.userId,
                  userType: selectedUserType,
                  phoneNumber: phoneNumber,
                  hasExistingCredentials: hasExistingCredentials
                }
              }
            });
          }
        }
      } else {
        toast.error(response.message || "Invalid OTP");
        setOtp(["", "", "", "", "", ""]);
        inputRefs.current[0]?.focus();
      }
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || "Verification failed. Please try again.");
      setOtp(["", "", "", "", "", ""]);
      inputRefs.current[0]?.focus();
    }
  });
  
  const {
    phoneNumber,
    selectedUserType
  } = useSelector((state) => state.user.authData);

  // Helper function to get user type label
  const getUserTypeLabel = (userTypeId) => {
    const userTypes = {
      2: "Transport Company",
      3: "Broker",
      4: "Carrier",
    
      6: "Shipper Company", // Legacy mapping - should be cleaned up in database
      7: "Shipper Company",
      8: "Shipper Individual"
    };
    return userTypes[userTypeId] || `User Type ${userTypeId}`;
  };



  // Helper function to proceed with OTP after user type check
  const proceedWithOTP = () => {
    const fullPhoneNumber = `+${phoneFormik.values.phoneNumber}`;
    dispatch(setPhoneNumber(fullPhoneNumber));

    // Call the mobile register API using React Query
    const payload = {
      mobileNumber: fullPhoneNumber,
      countryCode: "IN",
      userType: selectedUserType
    };

    // Store payload for resend functionality
    setLastPayload(payload);

    mobileRegisterMutation.mutate(payload);
  };

  // Handle user type confirmation
  const handleUserTypeConfirmation = (proceed) => {
    if (proceed) {
      proceedWithOTP();
    } else {
      setStep(2); // Go back to phone input
    }
  };

  // Handle login option selection
  const handleLoginOptionSelect = (option) => {
    if (option === 'otp') {
      proceedWithOTP();
    } else if (option === 'password') {
      // Ensure phone number and user type are stored in Redux before navigation
      const fullPhoneNumber = `+${phoneFormik.values.phoneNumber}`;
      dispatch(setPhoneNumber(fullPhoneNumber));
      dispatch(setSelectedUserType(selectedUserType));

      navigate(ROUTES.PASSWORD_LOGIN);
    }
  };

  // Timer countdown for OTP
  useEffect(() => {
    if (step === 3 && timeLeft > 0) {
      const timer = setTimeout(() => setTimeLeft(timeLeft - 1), 1000);
      return () => clearTimeout(timer);
    } else if (timeLeft === 0) {
      setCanResend(true);
    }
  }, [step, timeLeft]);

  // Phone number validation
  const phoneValidationSchema = Yup.object({
    phoneNumber: Yup.string()
      .matches(/^91[6-9][0-9]{9}$/, "Please enter a valid 10-digit mobile number starting with 6, 7, 8, or 9")
      .required("Mobile number is required"),
  });

  const phoneFormik = useFormik({
    initialValues: {
      phoneNumber: "",
    },
    validationSchema: phoneValidationSchema,
    onSubmit: (values) => {
      if (!selectedUserType) {
        toast.error("Please select your role first");
        return;
      }

      const fullPhoneNumber = `+${values.phoneNumber}`;

      // First check if user has existing registrations
      const checkPayload = {
        mobileNumber: fullPhoneNumber,
        countryCode: "IN"
      };

      checkUserTypesMutation.mutate(checkPayload);
    },
  });

  // Handle user type selection
  const handleUserTypeSelect = (userType) => {
    dispatch(setSelectedUserType(userType));
  };

  // Handle resend OTP
  const handleResendOTP = () => {
    if (!lastPayload) {
      toast.error("Unable to resend. Please try again from the beginning.");
      return;
    }

    // Clear current OTP
    setOtp(["", "", "", "", "", ""]);
    setReceivedOtp(null);

    // Reset timer and disable resend
    setTimeLeft(600); // 10 minutes
    setCanResend(false);

    // Call the mobile register API again with the same payload
    mobileRegisterMutation.mutate(lastPayload);
  };

  // Handle OTP input
  const handleOtpChange = (index, value) => {
    if (value.length > 1) return;
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  // Handle OTP verification
  const handleVerifyOTP = () => {
    const otpString = otp.join("");

    if (otpString.length !== 6) {
      toast.error("Please enter the complete 6-digit OTP");
      return;
    }

    // Call the verify OTP API using React Query
    // Try sending just the 10-digit number (without +91)
    const mobileOnly = phoneNumber.replace('+91', '');
    const payload = {
      mobileNumber: mobileOnly,
      userType: selectedUserType,
      otpToken: otpString,
      deviceInfo: "web-browser"
    };

    verifyOTPMutation.mutate(payload);
  };

  // Format time display
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  // Format phone number for display
  const maskPhoneNumber = (phone) => {
    if (!phone) return "";
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length >= 10) {
      return `${cleaned.slice(0, -10)}****${cleaned.slice(-4)}`;
    }
    return phone;
  };



  return (
    <div className="auth-form">
      {/* Header */}
      <div className="auth-welcome-text mb-3">
        <h2 className="fw-bold" style={{ color: "var(--text-primary)" }}>Welcome to TriTrackz</h2>
        <p style={{ color: "var(--text-secondary)" }}>
          {step === 1 && "Select your role to get started"}
          {step === 2 && "Enter your mobile number to continue"}
          {step === 3 && "Enter the verification code sent to your phone"}
          {step === 4 && "Choose your preferred login method"}
          {step === 5 && "You have existing accounts with us"}
        </p>
      </div>

      {/* Step 1: User Type Selection */}
      {step === 1 && (
        <div>
          <UserTypeSelector
            selectedUserType={selectedUserType}
            onUserTypeSelect={handleUserTypeSelect}
            onNavigateNext={() => setStep(2)}
          />
        </div>
      )}

      {/* Step 2: Phone Number */}
      {step === 2 && (
        <form onSubmit={phoneFormik.handleSubmit}>
          {/* Selected User Type Display */}
          <div className="selected-role-simple mb-4 mt-5">
            <div className="d-flex justify-content-between align-items-center">
              <div className="d-flex align-items-center gap-3">
                <div className="role-info-icon">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"/>
                    <path d="m9,12 2,2 4,-4"/>
                  </svg>
                </div>
                <div>
                  <small className="role-label-simple">SELECTED ROLE</small>
                  <div className="role-value-simple">{getUserTypeLabel(selectedUserType)}</div>
                </div>
              </div>
              <button
                type="button"
                className="change-btn-simple"
                onClick={() => setStep(1)}
              >
                Change
              </button>
            </div>
          </div>

          {/* Phone Number Input */}
          <div className="mb-4">
            

            <div className="phone-input-container">
              <div className={`phone-input-wrapper ${phoneFormik.touched.phoneNumber && phoneFormik.errors.phoneNumber ? 'error' : ''}`}>
                <div className="country-code-display">
                  <img
                    src="https://flagcdn.com/w20/in.png"
                    alt="India"
                    className="country-flag"
                  />
                  <span className="country-code">+91</span>
                </div>
                <div className="phone-number-input">
                  <input
                    type="tel"
                    className="form-control phone-input"
                    name="phoneNumber"
                    value={phoneFormik.values.phoneNumber ? phoneFormik.values.phoneNumber.replace('91', '') : ''}
                    onChange={(e) => {
                      const value = e.target.value.replace(/\D/g, '');
                      if (value.length <= 10) {
                        phoneFormik.setFieldValue('phoneNumber', value ? `91${value}` : '');
                      }
                    }}
                    onBlur={() => phoneFormik.setFieldTouched('phoneNumber', true)}
                    placeholder="Enter 10-digit mobile number"
                    maxLength="10"
                    style={{
                      border: 'none',
                      outline: 'none',
                      backgroundColor: 'transparent',
                      color: 'var(--text-primary)',
                      fontSize: '16px',
                      fontWeight: '500',
                      padding: '0',
                      width: '100%'
                    }}
                  />
                </div>
              </div>
            </div>

            {phoneFormik.touched.phoneNumber && phoneFormik.errors.phoneNumber && (
              <div className="phone-error-message mt-2">
                <small>{phoneFormik.errors.phoneNumber}</small>
              </div>
            )}
          </div>

          <button
            type="submit"
            className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
            disabled={checkUserTypesMutation.isPending || mobileRegisterMutation.isPending || !phoneFormik.isValid}
            style={{
              borderRadius: "12px",
              fontSize: "1.1rem",
              background: "var(--triadic-red-50)",
              border: "none",
              color: "var(--triadic-red-900)",
              boxShadow: "0 4px 15px rgba(229, 191, 193, 0.3)"
            }}
          >
            {checkUserTypesMutation.isPending ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" />
                Checking registrations...
              </>
            ) : mobileRegisterMutation.isPending ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" />
                Sending OTP...
              </>
            ) : (
              <>
                Send Verification Code

              </>
            )}
          </button>
        </form>
      )}

      {/* Step 3: OTP Verification */}
      {step === 3 && (
        <div>
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded" style={{ borderColor: "var(--border-secondary)" }}>
              <div>
                <small style={{ color: "var(--text-secondary)" }}>Code sent to:</small>
                <div style={{ color: "var(--text-primary)", fontWeight: "500", fontSize: "0.95rem" }}>
                  {maskPhoneNumber(phoneNumber)}
                </div>
              </div>
              <button
                type="button"
                className="change-btn-simple"
                onClick={() => setStep(2)}
              >
                Change
              </button>
            </div>
          </div>

          <div className="my-3">
           
            <div className="d-flex justify-content-between gap-2 my-3">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  ref={(el) => (inputRefs.current[index] = el)}
                  type="text"
                  className="form-control text-center fw-bold"
                  style={{ width: "50px", height: "50px", fontSize: "1.2rem" }}
                  value={digit}
                  onChange={(e) => handleOtpChange(index, e.target.value)}
                  maxLength="1"
                />
              ))}
            </div>
          </div>

          {/* OTP Display and Auto-fill */}
          {receivedOtp && (
            <div className="mb-3 p-3 rounded" style={{ backgroundColor: "rgba(var(--success-rgb), 0.1)", border: "1px solid rgba(var(--success-rgb), 0.2)" }}>
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  <small style={{ color: "var(--success-600)" }}>
                    <strong>Your OTP Code:</strong>
                  </small>
                  <div className="fw-bold mt-1" style={{ color: "var(--success-700)", fontSize: "1.1rem", letterSpacing: "2px" }}>
                    {receivedOtp}
                  </div>
                </div>
                <button
                  type="button"
                  className="btn btn-sm auth-secondary-btn"
                  style={{
                    borderRadius: "8px",
                    fontWeight: "500",
                    fontSize: "0.875rem",
                    padding: "0.5rem 1rem",
                    background: "var(--primary-300)",
                    border: "2px solid var(--primary-300)",
                    color: "#fff"
                  }}
                  onClick={() => {
                    const otpArray = receivedOtp.split('');
                    setOtp(otpArray);
                    inputRefs.current[0]?.focus();
                  }}
                >
                  Auto-fill
                </button>
              </div>
            </div>
          )}

          <div className="text-center mb-4">
            {!canResend ? (
              <p className="mb-2" style={{ color: "var(--text-secondary)" }}>
                Resend code in <strong style={{ color: "var(--text-primary)" }}>{formatTime(timeLeft)}</strong>
              </p>
            ) : (
              <button
                type="button"
                className="btn btn-link p-0 auth-link-btn"
                style={{
                  color: "var(--triadic-red-600)",
                  fontSize: "0.875rem",
                  fontWeight: "500",
                  textDecoration: "none"
                }}
                onClick={handleResendOTP}
                disabled={mobileRegisterMutation.isPending}
              >
                {mobileRegisterMutation.isPending ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" style={{ width: "12px", height: "12px" }} />
                    Sending...
                  </>
                ) : (
                  "Resend Verification Code"
                )}
              </button>
            )}
          </div>

          <button
            type="button"
            className="btn btn-primary w-100 py-3 fw-semibold auth-submit-btn"
            onClick={handleVerifyOTP}
            disabled={verifyOTPMutation.isPending || otp.join("").length !== 6}
          
          >
            {verifyOTPMutation.isPending ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" />
                Verifying...
              </>
            ) : (
              <>
                Verify & Continue
               
              </>
            )}
          </button>
        </div>
      )}

      {/* Step 4: Login Options */}
      {step === 4 && (
        <div>
          {/* Selected User Type Display */}
          

          {/* Mobile Number Display with Change Button */}
          <div className="mb-4">
            <div className="d-flex justify-content-between align-items-center p-3 border rounded" style={{ borderColor: "var(--border-secondary)" }}>
              <div>
                <small style={{ color: "var(--text-secondary)" }}>Mobile:</small>
                <div style={{ color: "var(--text-primary)", fontWeight: "500", fontSize: "0.95rem" }}>
                  {maskPhoneNumber(MaskNumber)}
                </div>
              </div>
              <button
                type="button"
                className="change-btn-simple"
                onClick={() => setStep(2)}
              >
                Change
              </button>
            </div>
          </div>

          {/* Login Options */}
          <div className="d-flex flex-column gap-3 mb-4">
            <button
              type="button"
              className="btn d-flex align-items-center gap-3 py-3 px-4"
              onClick={() => handleLoginOptionSelect('otp')}
              disabled={mobileRegisterMutation.isPending}
              style={{
                borderRadius: "12px",
                border: "2px solid var(--border-primary)",
                backgroundColor: "var(--surface-primary)",
                color: "var(--text-primary)",
                fontWeight: "500",
                fontSize: "1rem",
                textAlign: "left",
                opacity: mobileRegisterMutation.isPending ? 0.7 : 1
              }}
            >
              {mobileRegisterMutation.isPending ? (
                <>
                  <div className="spinner-border spinner-border-sm" role="status" style={{ width: "20px", height: "20px" }}>
                    <span className="visually-hidden">Loading...</span>
                  </div>
                  <div>
                    <div className="fw-semibold">Sending OTP...</div>
                    <small style={{ color: "var(--text-secondary)" }}>Please wait</small>
                  </div>
                </>
              ) : (
                <>
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ color: "var(--text-secondary)" }}>
                    <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                  </svg>
                  <div>
                    <div className="fw-semibold">Login with OTP</div>
                    <small style={{ color: "var(--text-secondary)" }}>Get verification code on your mobile</small>
                  </div>
                </>
              )}
            </button>

            <button
              type="button"
              className="btn d-flex align-items-center gap-3 py-3 px-4"
              onClick={() => handleLoginOptionSelect('password')}
              style={{
                borderRadius: "12px",
                border: "2px solid var(--border-primary)",
                backgroundColor: "var(--surface-primary)",
                color: "var(--text-primary)",
                fontWeight: "500",
                fontSize: "1rem",
                textAlign: "left"
              }}
            >
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ color: "var(--text-secondary)" }}>
                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                <circle cx="12" cy="16" r="1"/>
                <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
              </svg>
              <div>
                <div className="fw-semibold">Login with Password</div>
                <small style={{ color: "var(--text-secondary)" }}>Use your existing password</small>
              </div>
            </button>
          </div>

         
        </div>
      )}

      {/* Step 5: User Type Confirmation */}
      {step === 5 && (
        <div>
          {/* Header with Account Count - Following Reference Pattern */}
          <div className="d-flex align-items-center justify-content-between mb-4">
            <div>
              <h6 className="fw-semibold mb-0" style={{ color: "white", fontSize: "1rem" }}>
                Your Existing Accounts
              </h6>
            </div>
            <span style={{
              color: "rgba(255, 255, 255, 0.7)",
              fontSize: "0.875rem",
              fontWeight: "400"
            }}>
              {existingUserTypes.length} account{existingUserTypes.length > 1 ? 's' : ''}
            </span>
          </div>

          {/* Existing Accounts List - Dark Theme Pattern */}
          <div className="mb-4">
            {existingUserTypes.map((userType, index) => (
              <div key={index} className="d-flex align-items-center py-3 px-3 mb-2 rounded" style={{
                backgroundColor: "rgba(255, 255, 255, 0.05)",
                border: "1px solid rgba(255, 255, 255, 0.1)",
                borderRadius: "12px"
              }}>
                <div className="d-flex align-items-center gap-3 flex-grow-1">
                  <div style={{
                    width: "8px",
                    height: "8px",
                    borderRadius: "50%",
                    backgroundColor: "white"
                  }}></div>
                  <span className="fw-medium" style={{
                    color: "white",
                    fontSize: "0.95rem"
                  }}>
                    {getUserTypeLabel(userType)}
                  </span>
                </div>
                {userType === selectedUserType && (
                  <span style={{
                    backgroundColor: "rgba(255, 255, 255, 0.15)",
                    color: "white",
                    fontSize: "0.7rem",
                    padding: "4px 8px",
                    borderRadius: "8px",
                    fontWeight: "600",
                    letterSpacing: "0.5px"
                  }}>
                    SELECTED
                  </span>
                )}
              </div>
            ))}
          </div>

          {/* New Registration Info - Light Background Pattern */}
          <div className="mb-4 p-4 rounded d-flex align-items-start gap-3" style={{
               backgroundColor: "rgba(255, 255, 255, 0.05)",
                border: "1px solid rgba(255, 255, 255, 0.1)",
            borderRadius: "12px"
          }}>
           
            <div>
              <h3 className="mb-1 fw-semibold" style={{
               
                fontSize: "0.95rem",
                lineHeight: "1.3"
              }}>
                New Registration: {getUserTypeLabel(selectedUserType)}
              </h3>
              <small style={{
                color: "#718096",
                fontSize: "0.85rem",
                lineHeight: "1.4"
              }}>
                This will create a new account for this role
              </small>
            </div>
          </div>

          {/* Action Buttons - Following Reference Pattern */}
          <div className="d-flex gap-3 mb-4">
            <button
              type="button"
              className="btn flex-fill py-3"
              onClick={() => handleUserTypeConfirmation(false)}
              style={{
                borderRadius: "12px",
                fontWeight: "500",
                fontSize: "0.95rem",
                border: "1px solid rgba(255, 255, 255, 0.2)",
                backgroundColor: "rgba(255, 255, 255, 0.05)",
                color: "white"
              }}
            >
             Back
            </button>
            <button
              type="button"
              className="btn btn-primary flex-fill py-3"
              onClick={() => handleUserTypeConfirmation(true)}
              disabled={checkUserTypesMutation.isPending || mobileRegisterMutation.isPending}
             
            >
              {mobileRegisterMutation.isPending ? (
                <>
                  <span className="spinner-border spinner-border-sm me-2" style={{ width: "16px", height: "16px" }} />
                  Creating...
                </>
              ) : (
                "Continue"
              )}
            </button>
          </div>

          {/* Back Link */}
          
        </div>
      )}

      {/* User Type Confirmation Modal - Clean & Simple */}
      {showUserTypeConfirmation && (
        <div className="modal-overlay" style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1050
        }}>
          <div className="modal-content" style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            padding: '1.5rem',
            maxWidth: '400px',
            width: '90%',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
          }}>
            {/* Simple Header */}
            <div className="text-center mb-4">
              <h5 className="fw-bold mb-2" style={{ color: '#2D3748', fontSize: '1.25rem' }}>
                Account Found
              </h5>
              <p className="mb-0" style={{ color: '#718096', fontSize: '0.9rem' }}>
                You already have these accounts:
              </p>
            </div>

            {/* Existing User Types - Simple List */}
            <div className="mb-4">
              {existingUserTypes.map((userType, index) => (
                <div key={index} className="d-flex align-items-center justify-content-between py-2 px-3 mb-2 rounded" style={{
                  backgroundColor: '#F7FAFC',
                  border: '1px solid #E2E8F0'
                }}>
                  <span className="fw-medium" style={{ color: '#2D3748', fontSize: '0.9rem' }}>
                    {getUserTypeLabel(userType)}
                  </span>
                  {userType === selectedUserType && (
                    <span style={{
                      backgroundColor: '#FED7D7',
                      color: '#C53030',
                      fontSize: '0.7rem',
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontWeight: '600'
                    }}>
                      SELECTED
                    </span>
                  )}
                </div>
              ))}
            </div>

            {/* New Registration Info */}
            <div className="mb-4 p-3 rounded" style={{
              backgroundColor: '#EBF8FF',
              border: '1px solid #BEE3F8'
            }}>
              <p className="mb-1 fw-semibold" style={{ color: '#2B6CB0', fontSize: '0.9rem' }}>
                Register as: {getUserTypeLabel(selectedUserType)}
              </p>
              <small style={{ color: '#4299E1' }}>
                This will create a new account for this role.
              </small>
            </div>

            {/* Action Buttons */}
            <div className="d-flex gap-3">
              <button
                type="button"
                className="btn flex-fill py-2"
                onClick={() => handleUserTypeConfirmation(false)}
                style={{
                  borderRadius: '10px',
                  fontWeight: '500',
                  fontSize: '0.9rem',
                  border: '2px solid #E2E8F0',
                  backgroundColor: '#F7FAFC',
                  color: '#4A5568'
                }}
              >
                Cancel
              </button>
              <button
                type="button"
                className="btn flex-fill py-2"
                onClick={() => handleUserTypeConfirmation(true)}
                disabled={checkUserTypesMutation.isPending || mobileRegisterMutation.isPending}
                style={{
                  borderRadius: '10px',
                  fontWeight: '500',
                  fontSize: '0.9rem',
                  border: '2px solid #4299E1',
                  backgroundColor: '#4299E1',
                  color: 'white'
                }}
              >
                {mobileRegisterMutation.isPending ? (
                  <>
                    <span className="spinner-border spinner-border-sm me-2" style={{ width: '14px', height: '14px' }} />
                    Creating...
                  </>
                ) : (
                  'Continue'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Login Options Modal - Clean & Simple */}
      {showLoginOptions && (
        <div className="modal-overlay" style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1050
        }}>
          <div className="modal-content" style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            padding: '1.5rem',
            maxWidth: '380px',
            width: '90%',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.12)'
          }}>
            {/* Simple Header */}
            <div className="text-center mb-4">
              <h5 className="fw-bold mb-2" style={{ color: '#2D3748', fontSize: '1.25rem' }}>
                Welcome Back!
              </h5>
              <p className="mb-0" style={{ color: '#718096', fontSize: '0.9rem' }}>
                Account found as <span className="fw-semibold" style={{ color: '#2D3748' }}>{getUserTypeLabel(selectedUserType)}</span>
              </p>
            </div>

            {/* Login Options */}
            <div className="d-flex flex-column gap-3 mb-4">
              <button
                type="button"
                className="btn d-flex align-items-center justify-content-center gap-3 py-3"
                onClick={() => handleLoginOptionSelect('otp')}
                disabled={mobileRegisterMutation.isPending}
                style={{
                  borderRadius: '12px',
                  border: '2px solid #E2E8F0',
                  backgroundColor: '#F7FAFC',
                  color: '#2D3748',
                  fontWeight: '500',
                  fontSize: '0.95rem',
                  transition: 'all 0.2s ease',
                  opacity: mobileRegisterMutation.isPending ? 0.7 : 1
                }}
                onMouseEnter={(e) => {
                  if (!mobileRegisterMutation.isPending) {
                    e.target.style.borderColor = '#CBD5E0';
                    e.target.style.backgroundColor = '#EDF2F7';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!mobileRegisterMutation.isPending) {
                    e.target.style.borderColor = '#E2E8F0';
                    e.target.style.backgroundColor = '#F7FAFC';
                  }
                }}
              >
                {mobileRegisterMutation.isPending ? (
                  <>
                    <div className="spinner-border spinner-border-sm" role="status" style={{ width: "18px", height: "18px" }}>
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    Sending OTP...
                  </>
                ) : (
                  <>
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ color: '#4A5568' }}>
                      <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                    </svg>
                    Login with OTP
                  </>
                )}
              </button>

              <button
                type="button"
                className="btn d-flex align-items-center justify-content-center gap-3 py-3"
                onClick={() => handleLoginOptionSelect('password')}
                style={{
                  borderRadius: '12px',
                  border: '2px solid #E2E8F0',
                  backgroundColor: '#F7FAFC',
                  color: '#2D3748',
                  fontWeight: '500',
                  fontSize: '0.95rem',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.target.style.borderColor = '#CBD5E0';
                  e.target.style.backgroundColor = '#EDF2F7';
                }}
                onMouseLeave={(e) => {
                  e.target.style.borderColor = '#E2E8F0';
                  e.target.style.backgroundColor = '#F7FAFC';
                }}
              >
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ color: '#4A5568' }}>
                  <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
                  <circle cx="12" cy="16" r="1"/>
                  <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                </svg>
                Login with Password
              </button>
            </div>

           
          </div>
        </div>
      )}

    </div>
  );
};

export default UnifiedAuth;
