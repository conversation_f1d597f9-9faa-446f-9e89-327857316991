import React, { useState, useEffect } from "react";
import { Field, ErrorMessage } from "formik";
import { useGstVerification } from "@api/gstVerificationHooks";
import { DOCUMENT_TYPES } from "@api/documentUploadHooks";
import { DATE_FIELD_TYPES } from "@utils/dateValidationUtils";
import DateInput from "@components/Common/DateInput";
import DocumentUpload from "./DocumentUpload";
import toast from "react-hot-toast";

const GstDetailsForm = ({
  formik,
  existingDocuments = [],
  onDocumentChange,
  targetUserId = null,
}) => {
  // GST verification state
  const [gstVerificationStatus, setGstVerificationStatus] = useState(null); // null, 'success', 'error'
  const [lastVerifiedGst, setLastVerifiedGst] = useState("");
  const [isUserInitiated, setIsUserInitiated] = useState(false); // Track if verification is user-initiated

  // GST verification mutation
  const gstVerificationMutation = useGstVerification({
    onSuccess: (response) => {
      console.log("GST verification API response:", response);

      if (
        response?.success === true &&
        response?.data?.verification === "success"
      ) {
        console.log("GST verification successful");

        const gstData = response.data?.data;
        console.log("gstData", gstData);

        if (gstData) {
          const company = gstData.company || {};
          const gst = gstData.gst || {};
          const address = gstData.address?.principle || {};
          const jurisdiction = gstData.jurisdiction || {};

          // --- Company Info ---
          formik.setFieldValue("gstDetails.legalName", company.name || "");
          formik.setFieldValue("gstDetails.tradeName", company.tradeName || "");
          formik.setFieldValue(
            "gstDetails.constitutionOfBusiness",
            company.constitutionOfBusiness || ""
          );
          formik.setFieldValue(
            "gstDetails.natureOfBusinessActivity",
            company.natureOfBusinessActivity || ""
          );
          formik.setFieldValue(
            "gstDetails.taxPayerType",
            company.taxPayerType || ""
          );
          formik.setFieldValue(
            "gstDetails.companyStatus",
            company.status || ""
          );
          formik.setFieldValue("gstDetails.companyState", company.state || "");

          // --- GST Info ---
          formik.setFieldValue("gstDetails.gstin", gst.id || "");
          formik.setFieldValue(
            "gstDetails.dateOfCancellation",
            gst.dateOfCancellation || ""
          );
          if (gst.registrationDate) {
            const [day, month, year] = gst.registrationDate.split("/");
            const formattedDate = `${year}-${month.padStart(
              2,
              "0"
            )}-${day.padStart(2, "0")}`;
            formik.setFieldValue("gstDetails.registrationDate", formattedDate);
          } else {
            formik.setFieldValue("gstDetails.registrationDate", "");
          }

          // --- Address ---
          const fullAddress = [
            address.buildingName,
            address.buildingNumber,
            address.floorNo,
            address.street,
            address.location,
            address.district,
            address.stateCode,
            address.pinCode,
          ]
            .filter(Boolean)
            .join(", ");
          formik.setFieldValue("gstDetails.placeOfBusiness", fullAddress);

          // --- Jurisdiction ---
          formik.setFieldValue(
            "gstDetails.jurisdictionCentre",
            jurisdiction.centre || ""
          );
          formik.setFieldValue(
            "gstDetails.jurisdictionCentreCode",
            jurisdiction.centreCode || ""
          );
          formik.setFieldValue(
            "gstDetails.jurisdictionState",
            jurisdiction.state || ""
          );
          formik.setFieldValue(
            "gstDetails.jurisdictionStateCode",
            jurisdiction.stateCode || ""
          );

          setGstVerificationStatus("success");
        } else {
          console.error("GST data not found in response");
          setGstVerificationStatus("error");
          toast.error("GST verified but failed to extract data.");
        }
      } else {
        console.log("GST verification failed");
        setGstVerificationStatus("error");
        clearGstFormFields();
        toast.error(
          response?.data?.message ||
            "GST verification failed. Please check the GST number."
        );
      }
    },
    onError: (error) => {
      console.error("GST verification API error:", error);
      console.error("Error details:", error.response?.data || error.message);
      setGstVerificationStatus("error");
      clearGstFormFields();
      toast.error("Failed to verify GST. Please try again.");
    },
  });

  // Helper function to clear fields on failure
  const clearGstFormFields = () => {
    const fieldsToClear = [
      "legalName",
      "tradeName",
      "constitutionOfBusiness",
      "natureOfBusinessActivity",
      "taxPayerType",
      "companyStatus",
      "companyState",
      "gstin",
      "dateOfCancellation",
      "registrationDate",
      "placeOfBusiness",
      "jurisdictionCentre",
      "jurisdictionCentreCode",
      "jurisdictionState",
      "jurisdictionStateCode",
    ];
    fieldsToClear.forEach((field) =>
      formik.setFieldValue(`gstDetails.${field}`, "")
    );
  };

  // Function to verify GST
  const verifyGst = (gstNumber) => {
    if (!gstNumber || gstNumber.length !== 15) return;

    // Avoid duplicate verification
    if (gstNumber === lastVerifiedGst && gstVerificationStatus === "success") {
      return;
    }

    setLastVerifiedGst(gstNumber);

    // Call verification API
    gstVerificationMutation.mutate({
      gstin: gstNumber.toUpperCase(),
      consent: "Y",
      reason: "Testing GST validation",
    });
  };

  // Watch for GST number changes - only verify on user input, not initial load
  useEffect(() => {
    const gstNumber = formik.values.gstDetails?.gstNumber;

    // Only verify if user has interacted with the form and GST is complete
    if (isUserInitiated && gstNumber && gstNumber.length === 15) {
      // Valid GST format, trigger verification
      const timeoutId = setTimeout(() => {
        verifyGst(gstNumber);
      }, 500); // Debounce for 500ms

      return () => clearTimeout(timeoutId);
    } else if (!gstNumber || gstNumber.length < 15) {
      setGstVerificationStatus(null);
      setLastVerifiedGst("");
      // Clear auto-filled fields when GST is invalid/incomplete (only if user initiated)
      if (
        isUserInitiated &&
        (formik.values.gstDetails?.legalName ||
          formik.values.gstDetails?.tradeName)
      ) {
        formik.setFieldValue("gstDetails.legalName", "");
        formik.setFieldValue("gstDetails.tradeName", "");
        formik.setFieldValue("gstDetails.registrationDate", "");
        formik.setFieldValue("gstDetails.placeOfBusiness", "");
      }
    }
  }, [formik.values.gstDetails?.gstNumber, isUserInitiated]);
  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">GST Details</h3>
        <p className="text-muted mb-0">
          Please provide your GST registration information
        </p>
      </div>
      <div className="row">
        <div className="col-md-6 mb-3">
          <label
            htmlFor="gstDetails.gstNumber"
            className="form-label fw-medium"
          >
            GST Number <span className="text-danger">*</span>
          </label>
          <div className="position-relative">
            <Field
              type="text"
              name="gstDetails.gstNumber"
              className={`form-control ${
                formik.touched.gstDetails?.gstNumber &&
                formik.errors.gstDetails?.gstNumber
                  ? "is-invalid"
                  : gstVerificationStatus === "success"
                  ? "is-valid"
                  : gstVerificationStatus === "error"
                  ? "is-invalid"
                  : ""
              }`}
              placeholder="Enter GST number"
              style={{ textTransform: "uppercase", paddingRight: "40px" }}
              maxLength={15}
              onChange={(e) => {
                setIsUserInitiated(true); // Mark as user-initiated
                formik.handleChange(e);
              }}
            />

            {/* Verification Status Indicator */}
            <div
              className="position-absolute"
              style={{
                right: "10px",
                top: "50%",
                transform: "translateY(-50%)",
              }}
            >
              {gstVerificationMutation.isPending && (
                <div
                  className="spinner-border spinner-border-sm text-primary"
                  role="status"
                  style={{ width: "16px", height: "16px" }}
                >
                  <span className="visually-hidden">Verifying...</span>
                </div>
              )}
              {gstVerificationStatus === "success" && (
                <i
                  className="fas fa-check text-success"
                  title="GST verified successfully"
                  style={{ fontSize: "18px" }}
                ></i>
              )}
              {gstVerificationStatus === "error" && (
                <i
                  className="fas fa-times text-danger"
                  title="GST verification failed"
                  style={{ fontSize: "18px" }}
                ></i>
              )}
            </div>
          </div>

          <ErrorMessage
            name="gstDetails.gstNumber"
            component="div"
            className="invalid-feedback"
          />

          {/* Verification Status Message */}
          {gstVerificationStatus === "success" && (
            <small className="text-success mt-1 d-block">
              <i className="fas fa-check me-1"></i>
              GST verified successfully
            </small>
          )}
          {gstVerificationStatus === "error" && (
            <small className="text-danger mt-1 d-block">
              <i className="fas fa-times me-1"></i>
              GST verification failed. Please check the number.
            </small>
          )}
          {gstVerificationMutation.isPending && (
            <small className="text-info mt-1 d-block">
              <i className="fas fa-spinner fa-spin me-1"></i>
              Verifying GST number...
            </small>
          )}
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="gstDetails.legalName"
            className="form-label fw-medium"
          >
            Legal Name <span className="text-danger">*</span>
            {gstVerificationStatus === "success" && (
              <small className="text-success ms-2">
                <i className="fas fa-magic me-1"></i>
                Auto-filled from GST
              </small>
            )}
          </label>
          <Field
            type="text"
            name="gstDetails.legalName"
            className={`form-control ${
              formik.touched.gstDetails?.legalName &&
              formik.errors.gstDetails?.legalName
                ? "is-invalid"
                : formik.touched.gstDetails?.legalName &&
                  formik.values.gstDetails?.legalName
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter legal name"
          />
          <ErrorMessage
            name="gstDetails.legalName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="gstDetails.tradeName"
            className="form-label fw-medium"
          >
            Trade Name <span className="text-danger">*</span>
            {gstVerificationStatus === "success" && (
              <small className="text-success ms-2">
                <i className="fas fa-magic me-1"></i>
                Auto-filled from GST
              </small>
            )}
          </label>
          <Field
            type="text"
            name="gstDetails.tradeName"
            className={`form-control ${
              formik.touched.gstDetails?.tradeName &&
              formik.errors.gstDetails?.tradeName
                ? "is-invalid"
                : formik.touched.gstDetails?.tradeName &&
                  formik.values.gstDetails?.tradeName
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter trade name"
          />
          <ErrorMessage
            name="gstDetails.tradeName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="gstDetails.placeOfBusiness"
            className="form-label fw-medium"
          >
            Place of Business <span className="text-danger">*</span>
            {gstVerificationStatus === "success" && (
              <small className="text-success ms-2">
                <i className="fas fa-magic me-1"></i>
                Auto-filled from GST
              </small>
            )}
          </label>
          <Field
            type="text"
            name="gstDetails.placeOfBusiness"
            className={`form-control ${
              formik.touched.gstDetails?.placeOfBusiness &&
              formik.errors.gstDetails?.placeOfBusiness
                ? "is-invalid"
                : formik.touched.gstDetails?.placeOfBusiness &&
                  formik.values.gstDetails?.placeOfBusiness
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter place of business"
          />
          <ErrorMessage
            name="gstDetails.placeOfBusiness"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <DateInput
            name="gstDetails.registrationDate"
            label={
              <span>
                Registration Date <span className="text-danger">*</span>
                {gstVerificationStatus === "success" && (
                  <small className="text-success ms-2">
                    <i className="fas fa-magic me-1"></i>
                    Auto-filled from GST
                  </small>
                )}
              </span>
            }
            required={true}
            fieldType={DATE_FIELD_TYPES.REGISTRATION_DATE}
            formik={formik}
            helpText="Date of business registration"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="gstDetails.registrationNumber"
            className="form-label fw-medium"
          >
            Registration Number <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="gstDetails.registrationNumber"
            className={`form-control ${
              formik.touched.gstDetails?.registrationNumber &&
              formik.errors.gstDetails?.registrationNumber
                ? "is-invalid"
                : formik.touched.gstDetails?.registrationNumber &&
                  formik.values.gstDetails?.registrationNumber
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter registration number"
          />
          <ErrorMessage
            name="gstDetails.registrationNumber"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-12 mb-3">
          {/* GST Certificate Upload */}
          <DocumentUpload
            documentType={DOCUMENT_TYPES.GST_CERTIFICATE}
            title="GST Certificate"
            accept=".pdf,.jpg,.jpeg,.png"
            existingDocuments={existingDocuments}
            onDocumentChange={onDocumentChange}
            targetUserId={targetUserId}
          />
        </div>
      </div>
    </div>
  );
};

export default GstDetailsForm;
