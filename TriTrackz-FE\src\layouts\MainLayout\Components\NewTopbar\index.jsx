import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ell,
  <PERSON>a<PERSON>ser,
  FaSignOutAlt,
  FaSun,
  FaMoon,
  FaExclamationTriangle,
} from "react-icons/fa";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useTheme } from "@contexts/ThemeContext";
import { getDisplayName, getUserRoleLabel, getProfileStatusBadge } from "@utils/profileUtils";
import ModernActionPopup from "@components/ModernActionPopup";
import ROUTES from "@constants/routes";

const NewTopbar = ({
  pageTitle = "Dashboard",
  onToggleSidebar,
  onToggleMobileSidebar,
  onLogout,
}) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);
  const [showModernPopup, setShowModernPopup] = useState(false);
  const { toggleTheme, isDark } = useTheme();
  const { isKYCCompleted } = useSelector((state) => state.user.profileStatus);
  const { profileData } = useSelector((state) => state.user);
  const navigate = useNavigate();

  // Get display name and role from profile data
  const displayName = getDisplayName(profileData);
  const userRole = getUserRoleLabel(profileData);

  // Check if we should show status indicator
  const shouldShowStatusIndicator = profileData && typeof profileData.status === 'number';

  const notificationRef = useRef(null);
  const profileRef = useRef(null);

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
    setShowProfileMenu(false);
  };

  const toggleProfileMenu = () => {
    setShowProfileMenu(!showProfileMenu);
    setShowNotifications(false);
  };

  const handleKYCClick = () => {
    setShowModernPopup(true);
    setShowProfileMenu(false);
    setShowNotifications(false);
  };

  const handleProfileSettingsClick = () => {
    navigate(ROUTES.PROFILE_SETTINGS);
    setShowProfileMenu(false);
  };

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setShowNotifications(false);
      }
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setShowProfileMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="new-topbar">
      <div className="topbar-left">
        {/* Mobile Menu Toggle */}
        <button
          className="mobile-menu-btn d-lg-none"
          onClick={onToggleMobileSidebar}
        >
          <FaBars size={16} />
        </button>

        {/* Desktop Sidebar Toggle */}
        <button
          className="sidebar-toggle-btn d-none d-lg-block"
          onClick={onToggleSidebar}
        >
          <FaBars size={16} />
        </button>
      </div>

      <div className="topbar-right">
        {/* Notifications */}
        <div className="profile-info d-none d-md-block">
          <span className="profile-role">
            {!isKYCCompleted && (
              <button
                className="kyc-badge kyc-badge-clickable"
                onClick={handleKYCClick}
                type="button"
              >
                <FaExclamationTriangle size={10} className="me-1" />
                KYC Pending
              </button>
            )}
          </span>
        </div>
        <div className="notification-wrapper" ref={notificationRef}>
          <button className="notification-btn" onClick={toggleNotifications}>
            <FaBell size={16} />
            <span className="notification-badge">3</span>
          </button>

          {showNotifications && (
            <div
              className="notification-dropdown"
              style={{
                position: "absolute",
                top: "100%",
                right: "0",
                background: "#1a365d",
                borderRadius: "12px",
                width: "320px",
                zIndex: 9999,
                marginTop: "8px",
              }}
            >
              <div className="notification-header">
                <h6>Notifications</h6>
              </div>
              <div className="notification-list">
                <div className="notification-item text-center py-4">
                  <div className="notification-content">
                    <p className="text-muted">No new notifications</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Profile Avatar */}
        <div className="profile-wrapper" ref={profileRef}>
          <button className="profile-btn" onClick={toggleProfileMenu}>
            <div className="profile-avatar" style={{ position: 'relative' }}>
              <img
                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=40&h=40&fit=crop&crop=face"
                alt="Profile"
                className="avatar-img"
                style={{ borderRadius: '50%' }} // Ensure image itself is rounded
              />
              {/* Status indicator dot */}
              {shouldShowStatusIndicator && (
                <div
                  className="status-indicator"
                  style={{
                    position: 'absolute',
                    top: '-4px',
                    right: '-4px',
                    width: '14px',
                    height: '14px',
                    borderRadius: '50%',
                    border: '2px solid white',
                    backgroundColor: profileData.status === 0 ? '#dc3545' : '#28a745',
                    zIndex: 1001, // Higher z-index to ensure visibility
                    boxShadow: '0 2px 6px rgba(0, 0, 0, 0.3)' // More prominent shadow
                  }}
                  title={getProfileStatusBadge(profileData).text}
                />
              )}
            </div>
          </button>

          {showProfileMenu && (
            <div
              className="profile-dropdown"
              style={{
                position: "absolute",
                top: "100%",
                right: "0",
                background: "#1a365d",

                borderRadius: "12px",
                width: "280px",
                zIndex: 9999,
                marginTop: "8px",
              }}
            >
              <div className="profile-dropdown-header">
                <div className="profile-avatar-large">
                  <img
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face"
                    alt="Profile"
                    className="avatar-img"
                  />
                </div>
                <div className="profile-details">
                  <h6>{displayName}</h6>
                  <p>{userRole}</p>
                  {profileData && (
                    <div className="profile-status-badge mt-1">
                      <span className={`badge ${getProfileStatusBadge(profileData).class} badge-sm`}>
                        {getProfileStatusBadge(profileData).text}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <div className="profile-menu">
                <div
                  className="profile-menu-item"
                  onClick={handleProfileSettingsClick}
                  style={{ cursor: 'pointer' }}
                >
                  <FaUser size={14} />
                  <span>Profile Settings</span>
                </div>
                <div
                  className="profile-menu-item theme-menu-item"
                  onClick={toggleTheme}
                >
                  {isDark ? <FaSun size={14} /> : <FaMoon size={14} />}
                  <span>{isDark ? "Light Mode" : "Dark Mode"}</span>
                </div>
                <a href="#" className="profile-menu-item" onClick={onLogout}>
                  <FaSignOutAlt size={14} />
                  <span>Logout</span>
                </a>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Modern Action Popup */}
      <ModernActionPopup
        isOpen={showModernPopup}
        onClose={() => setShowModernPopup(false)}
      />
    </div>
  );
};

export default NewTopbar;
