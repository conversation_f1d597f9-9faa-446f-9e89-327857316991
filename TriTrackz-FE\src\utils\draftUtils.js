/**
 * Utility functions for managing KYC form draft data in localStorage
 */
import _ from 'lodash';

const DRAFT_STORAGE_KEY = 'tritrackz-kyc-draft';
const DRAFT_METADATA_KEY = 'tritrackz-kyc-draft-meta';

/**
 * Save draft data to localStorage
 * @param {string} userId - User ID
 * @param {Object} formData - Form data to save
 * @param {number} currentStep - Current step index
 * @param {Array} completedSteps - Array of completed step indices
 */
export const saveDraftToStorage = (userId, formData, currentStep, completedSteps) => {
  try {
    const sessionStartTime = sessionStorage.getItem('session-start-time') || Date.now().toString();

    const draftData = {
      userId,
      formData,
      currentStep,
      completedSteps,
      timestamp: new Date().toISOString(),
      sessionStartTime: parseInt(sessionStartTime),
      version: '1.0' // For future compatibility
    };

    localStorage.setItem(`${DRAFT_STORAGE_KEY}-${userId}`, JSON.stringify(draftData));
    
    // Save metadata for quick access
    const metadata = {
      userId,
      timestamp: draftData.timestamp,
      currentStep,
      totalSteps: completedSteps.length
    };
    localStorage.setItem(`${DRAFT_METADATA_KEY}-${userId}`, JSON.stringify(metadata));
    
    return true;
  } catch (error) {
    console.error('Error saving draft to localStorage:', error);
    return false;
  }
};

/**
 * Load draft data from localStorage
 * @param {string} userId - User ID
 * @returns {Object|null} Draft data or null if not found
 */
export const loadDraftFromStorage = (userId) => {
  try {
    const draftDataStr = localStorage.getItem(`${DRAFT_STORAGE_KEY}-${userId}`);
    if (!draftDataStr) {
      return null;
    }

    const draftData = JSON.parse(draftDataStr);
    
    // Validate draft data structure
    if (!draftData.userId || !draftData.formData || typeof draftData.currentStep !== 'number') {

      removeDraftFromStorage(userId);
      return null;
    }

    // Check if draft is not too old (7 days)
    const draftAge = new Date() - new Date(draftData.timestamp);
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
    
    if (draftAge > maxAge) {
      console.info('Draft data is too old, removing from storage');
      removeDraftFromStorage(userId);
      return null;
    }

    return draftData;
  } catch (error) {
    console.error('Error loading draft from localStorage:', error);
    removeDraftFromStorage(userId); // Clean up corrupted data
    return null;
  }
};

/**
 * Remove draft data from localStorage
 * @param {string} userId - User ID
 */
export const removeDraftFromStorage = (userId) => {
  try {
    localStorage.removeItem(`${DRAFT_STORAGE_KEY}-${userId}`);
    localStorage.removeItem(`${DRAFT_METADATA_KEY}-${userId}`);
    return true;
  } catch (error) {
    console.error('Error removing draft from localStorage:', error);
    return false;
  }
};

/**
 * Check if draft exists for user
 * @param {string} userId - User ID
 * @returns {boolean} True if draft exists
 */
export const hasDraftInStorage = (userId) => {
  try {
    const draftData = loadDraftFromStorage(userId);
    return draftData !== null;
  } catch (error) {
    console.error('Error checking draft existence:', error);
    return false;
  }
};

/**
 * Get draft metadata without loading full data
 * @param {string} userId - User ID
 * @returns {Object|null} Draft metadata or null
 */
export const getDraftMetadata = (userId) => {
  try {
    const metadataStr = localStorage.getItem(`${DRAFT_METADATA_KEY}-${userId}`);
    if (!metadataStr) {
      return null;
    }

    return JSON.parse(metadataStr);
  } catch (error) {
    console.error('Error loading draft metadata:', error);
    return null;
  }
};

/**
 * Compare two form data objects to detect changes using Lodash deep comparison
 * @param {Object} data1 - First data object
 * @param {Object} data2 - Second data object
 * @returns {boolean} True if data has changed
 */
export const hasFormDataChanged = (data1, data2) => {
  try {
    // Use Lodash deep comparison for accurate object comparison
    return !_.isEqual(data1, data2);
  } catch (error) {

    return true; // Assume changed if comparison fails
  }
};

/**
 * Clean up old draft data for all users (maintenance function)
 * @param {number} maxAgeInDays - Maximum age in days (default: 7)
 */
export const cleanupOldDrafts = (maxAgeInDays = 7) => {
  try {
    const maxAge = maxAgeInDays * 24 * 60 * 60 * 1000;
    const now = new Date();
    
    // Get all localStorage keys
    const keys = Object.keys(localStorage);
    
    // Find and clean up old draft keys
    keys.forEach(key => {
      if (key.startsWith(DRAFT_STORAGE_KEY) || key.startsWith(DRAFT_METADATA_KEY)) {
        try {
          const data = JSON.parse(localStorage.getItem(key));
          if (data.timestamp) {
            const age = now - new Date(data.timestamp);
            if (age > maxAge) {
              localStorage.removeItem(key);

            }
          }
        } catch (error) {
          // Remove corrupted entries
          localStorage.removeItem(key);

        }
      }
    });
  } catch (error) {

  }
};

/**
 * Get storage usage information
 * @returns {Object} Storage usage stats
 */
export const getDraftStorageInfo = () => {
  try {
    const keys = Object.keys(localStorage);
    const draftKeys = keys.filter(key => 
      key.startsWith(DRAFT_STORAGE_KEY) || key.startsWith(DRAFT_METADATA_KEY)
    );
    
    let totalSize = 0;
    draftKeys.forEach(key => {
      totalSize += localStorage.getItem(key).length;
    });
    
    return {
      draftCount: draftKeys.filter(key => key.startsWith(DRAFT_STORAGE_KEY)).length,
      totalKeys: draftKeys.length,
      totalSizeBytes: totalSize,
      totalSizeKB: Math.round(totalSize / 1024 * 100) / 100
    };
  } catch (error) {

    return { draftCount: 0, totalKeys: 0, totalSizeBytes: 0, totalSizeKB: 0 };
  }
};
