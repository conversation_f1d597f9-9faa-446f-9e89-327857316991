import ROUTES from "@constants/routes";
import AuthLayout from "@layouts/AuthLayout";
import MainLayout from "@layouts/MainLayout";
import Dashboard from "@pages/Dashboard";
import Shipments from "@pages/Shipments";
import Reports from "@pages/Reports";
import ProfileSettings from "@pages/ProfileSettings";

import PrivateRoute from "./PrivateRoute";
import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";
import Login from "@pages/Authentication/Login";
import ForgotPassword from "@pages/Authentication/ForgotPassword";
import SetPassword from "@pages/Authentication/SetPassword";
import Customers from "@pages/Customers";
import ViewUser from "@pages/Customers/ViewUser";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Authentication Routes */}
        <Route element={<AuthLayout />}>
          <Route path={ROUTES.LOGIN} element={<Login />} />
          <Route path={ROUTES.FORGOT_PASSWORD} element={<ForgotPassword />} />
          <Route path={ROUTES.SET_PASSWORD} element={<SetPassword />} />
        </Route>

        {/* Protected Main Application Routes */}
        <Route element={<PrivateRoute />}>
          <Route element={<MainLayout />}>
            {/* Dashboard - Always accessible after login */}
            <Route path={ROUTES.DASHBOARD} element={<Dashboard />} />

            {/* Profile Settings - Always accessible after login */}
            <Route
              path={ROUTES.PROFILE_SETTINGS}
              element={<ProfileSettings />}
            />

            <Route path={ROUTES.CUSTOMERS} element={<Customers />} />
            <Route path="/customers/view/:userId" element={<ViewUser />} />

            {/* Other routes - Now directly accessible */}
            <Route path="/shipments" element={<Shipments />} />
            <Route path="/reports" element={<Reports />} />
          </Route>
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
