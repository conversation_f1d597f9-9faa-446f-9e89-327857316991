import React, { useState, useEffect } from 'react';
import { Field, ErrorMessage } from 'formik';
import { USER_TYPES } from '@constants/enum';
import { usePanVerification } from '@api/panVerificationHooks';
import { DOCUMENT_TYPES } from '@api/documentUploadHooks';
import { DATE_FIELD_TYPES } from '@utils/dateValidationUtils';
import DateInput from '@components/Common/DateInput';
import DocumentUpload from './DocumentUpload';
import toast from 'react-hot-toast';

const PanCardDetailsForm = ({ formik, userType, existingDocuments = [], onDocumentChange }) => {
  const isCompany = [USER_TYPES.TRANSPORT_COMPANY, USER_TYPES.CARRIER, USER_TYPES.SHIPPER_COMPANY].includes(userType);

  // PAN verification state for personal PAN
  const [panVerificationStatus, setPanVerificationStatus] = useState(null); // null, 'success', 'error'
  const [lastVerifiedPan, setLastVerifiedPan] = useState('');
  const [isUserInitiated, setIsUserInitiated] = useState(false); // Track if verification is user-initiated

  // Business PAN verification state
  const [businessPanVerificationStatus, setBusinessPanVerificationStatus] = useState(null);
  const [lastVerifiedBusinessPan, setLastVerifiedBusinessPan] = useState('');
  const [isBusinessPanUserInitiated, setIsBusinessPanUserInitiated] = useState(false);

// Personal PAN verification mutation
const panVerificationMutation = usePanVerification({
  onSuccess: (response) => {
    console.log('Personal PAN verification API response:', response);

    if (response?.success === true && response?.data?.verification === 'SUCCESS') {


      const panData = response.data?.data;
      const fullName = panData?.full_Name;

      if (fullName) {
        formik.setFieldValue('panCardDetails.nameAsPerPan', fullName);
        setPanVerificationStatus('success');
      } else {

        setPanVerificationStatus('error');
        toast.error('PAN verified but failed to extract name data.');
      }
    } else {

      setPanVerificationStatus('error');
      // Clear the name field on error
      formik.setFieldValue('panCardDetails.nameAsPerPan', '');
      toast.error(response?.data?.message || 'PAN verification failed. Please check the PAN number.');
    }
  },
  onError: (error) => {
    setPanVerificationStatus('error');
    // Clear the name field on API error
    formik.setFieldValue('panCardDetails.nameAsPerPan', '');
    toast.error('Failed to verify PAN. Please try again.');
  }
});

// Business PAN verification mutation
const businessPanVerificationMutation = usePanVerification({
  onSuccess: (response) => {
    if (response?.success === true && response?.data?.verification === 'SUCCESS') {

      const panData = response.data?.data;
      const fullName = panData?.full_Name;

      if (fullName) {
        // Auto-fill company name from business PAN verification
        formik.setFieldValue('panCardDetails.companyName', fullName);
        toast.success('Business PAN verified successfully!');
      }

      setBusinessPanVerificationStatus('success');
    } else {

      setBusinessPanVerificationStatus('error');
      // Clear the company name field on verification failure
      formik.setFieldValue('panCardDetails.companyName', '');
      toast.error('Business PAN verification failed. Please check the PAN number.');
    }
  },
  onError: (error) => {

    setBusinessPanVerificationStatus('error');
    // Clear the company name field on API error
    formik.setFieldValue('panCardDetails.companyName', '');
    toast.error('Failed to verify business PAN. Please try again.');
  }
});

  // Function to verify personal PAN
  const verifyPan = (panNumber) => {
    if (!panNumber || panNumber.length !== 10) {
      setPanVerificationStatus(null);
      return;
    }

    // Only verify if PAN has changed
    if (panNumber === lastVerifiedPan) {
      return;
    }

    setLastVerifiedPan(panNumber);
    setPanVerificationStatus(null);

    // Call verification API
    panVerificationMutation.mutate({
      pan: panNumber.toUpperCase(),
      consent: "Y",
      reason: "KYC onboarding"
    });
  };

  // Function to verify business PAN
  const verifyBusinessPan = (panNumber) => {
    if (!panNumber || panNumber.length !== 10) {
      setBusinessPanVerificationStatus(null);
      return;
    }

    // Only verify if business PAN has changed
    if (panNumber === lastVerifiedBusinessPan) {
      return;
    }

    setLastVerifiedBusinessPan(panNumber);
    setBusinessPanVerificationStatus(null);

    // Call verification API
    businessPanVerificationMutation.mutate({
      pan: panNumber.toUpperCase(),
      consent: "Y",
      reason: "KYC onboarding"
    });
  };

  // Watch for PAN number changes - only verify on user input, not initial load
  useEffect(() => {
    const panNumber = formik.values.panCardDetails?.panNumber;

    // Only verify if user has interacted with the form and PAN is complete
    if (isUserInitiated && panNumber && panNumber.length === 10) {
      // Debounce verification call
      const timeoutId = setTimeout(() => {
        verifyPan(panNumber);
      }, 1000); // 1 second delay

      return () => clearTimeout(timeoutId);
    } else if (!panNumber || panNumber.length < 10) {
      setPanVerificationStatus(null);
      setLastVerifiedPan('');
      // Clear name field when PAN is invalid/incomplete (only if user initiated)
      if (isUserInitiated && formik.values.panCardDetails?.nameAsPerPan) {
        formik.setFieldValue('panCardDetails.nameAsPerPan', '');
      }
    }
  }, [formik.values.panCardDetails?.panNumber, isUserInitiated]);

  // Watch for business PAN number changes - only verify on user input, not initial load
  useEffect(() => {
    const businessPanNumber = formik.values.panCardDetails?.businessPanNumber;

    // Only verify if user has interacted with the form and business PAN is complete
    if (isBusinessPanUserInitiated && businessPanNumber && businessPanNumber.length === 10) {
      // Debounce verification call
      const timeoutId = setTimeout(() => {
        verifyBusinessPan(businessPanNumber);
      }, 1000); // 1 second delay

      return () => clearTimeout(timeoutId);
    } else if (!businessPanNumber || businessPanNumber.length < 10) {
      setBusinessPanVerificationStatus(null);
      setLastVerifiedBusinessPan('');
      // Clear company name field when business PAN is invalid/incomplete (only if user initiated)
      if (isBusinessPanUserInitiated && formik.values.panCardDetails?.companyName) {
        formik.setFieldValue('panCardDetails.companyName', '');
      }
    }
  }, [formik.values.panCardDetails?.businessPanNumber, isBusinessPanUserInitiated]);

  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">PAN Card Details</h3>
        <p className="clean-section-subtitle">Please provide your PAN card information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          {/* PAN Number Field - First */}
          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.panNumber" className="clean-form-label">
              PAN Number <span className="text-danger">•</span>
            </label>
            <div className="position-relative">
              <Field
                type="text"
                name="panCardDetails.panNumber"
                className={`clean-form-control ${
                  formik.touched.panCardDetails?.panNumber && formik.errors.panCardDetails?.panNumber
                    ? 'is-invalid'
                    : panVerificationStatus === 'success'
                    ? 'is-valid'
                    : panVerificationStatus === 'error'
                    ? 'is-invalid'
                    : ''
                }`}
                placeholder="Enter PAN number"
                style={{ textTransform: 'uppercase', paddingRight: '40px' }}
                maxLength={10}
                onChange={(e) => {
                  setIsUserInitiated(true); // Mark as user-initiated
                  formik.handleChange(e);
                }}
              />

              {/* Verification Status Indicator */}
              <div className="position-absolute" style={{ right: '10px', top: '50%', transform: 'translateY(-50%)' }}>
                {panVerificationMutation.isPending && (
                  <div className="spinner-border spinner-border-sm text-primary" role="status" style={{ width: '16px', height: '16px' }}>
                    <span className="visually-hidden">Verifying...</span>
                  </div>
                )}
                {panVerificationStatus === 'success' && (
                  <i className="fas fa-check text-success" title="PAN verified successfully" style={{ fontSize: '18px' }}></i>
                )}
                {panVerificationStatus === 'error' && (
                  <i className="fas fa-times text-danger" title="PAN verification failed" style={{ fontSize: '18px' }}></i>
                )}
              </div>
            </div>

            <ErrorMessage
              name="panCardDetails.panNumber"
              component="div"
              className="clean-form-error"
            />

            {/* Verification Status Message */}
            {panVerificationStatus === 'success' && (
              <small className="text-success mt-1 d-block">
                <i className="fas fa-check me-1"></i>
                PAN verified successfully
              </small>
            )}
            {panVerificationStatus === 'error' && (
              <small className="text-danger mt-1 d-block">
                <i className="fas fa-times me-1"></i>
                PAN verification failed. Please check the number.
              </small>
            )}
            {panVerificationMutation.isPending && (
              <small className="text-info mt-1 d-block">
                <i className="fas fa-spinner fa-spin me-1"></i>
                Verifying PAN number...
              </small>
            )}
          </div>

          {/* Name as per PAN Field - Second */}
          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.nameAsPerPan" className="clean-form-label">
              Name as per PAN <span className="text-danger">•</span>
              {panVerificationStatus === 'success' && (
                <small className="text-success ms-2">
                  <i className="fas fa-magic me-1"></i>
                  Auto-filled from PAN
                </small>
              )}
            </label>
            <Field
              type="text"
              name="panCardDetails.nameAsPerPan"
              className={`clean-form-control ${
                formik.touched.panCardDetails?.nameAsPerPan && formik.errors.panCardDetails?.nameAsPerPan
                  ? 'is-invalid'
                  : formik.touched.panCardDetails?.nameAsPerPan && formik.values.panCardDetails?.nameAsPerPan
                  ? 'is-valid'
                  : ''
              }`}
              placeholder="Enter name as per PAN"
            />

            <ErrorMessage
              name="panCardDetails.nameAsPerPan"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="panCardDetails.fatherOrHusbandNameInPan" className="clean-form-label">
              Father/Husband Name in PAN <span className="text-danger">•</span>
            </label>
            <Field
              type="text"
              name="panCardDetails.fatherOrHusbandNameInPan"
              className={`clean-form-control ${
                formik.touched.panCardDetails?.fatherOrHusbandNameInPan && formik.errors.panCardDetails?.fatherOrHusbandNameInPan
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter father/husband name"
            />
            <ErrorMessage
              name="panCardDetails.fatherOrHusbandNameInPan"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <DateInput
              name="panCardDetails.dateOfBirthInPan"
              label="Date of Birth in PAN"
              required={true}
              fieldType={DATE_FIELD_TYPES.DATE_OF_BIRTH}
              formik={formik}
              helpText="Must be at least 18 years old"
            />
          </div>

          {/* Company-specific PAN fields */}
          {isCompany && (
            <>
             <div className="col-md-6 mb-3">
                <label htmlFor="panCardDetails.businessPanNumber" className="clean-form-label">
                  Business PAN Number <span className="text-danger">•</span>
                </label>
                <div className="position-relative">
                  <Field
                    type="text"
                    name="panCardDetails.businessPanNumber"
                    className={`clean-form-control ${
                      formik.touched.panCardDetails?.businessPanNumber && formik.errors.panCardDetails?.businessPanNumber
                        ? 'is-invalid'
                        : businessPanVerificationStatus === 'success'
                        ? 'is-valid'
                        : businessPanVerificationStatus === 'error'
                        ? 'is-invalid'
                        : ''
                    }`}
                    placeholder="Enter business PAN number"
                    style={{ textTransform: 'uppercase' }}
                    onFocus={() => setIsBusinessPanUserInitiated(true)}
                    onChange={(e) => {
                      formik.handleChange(e);
                      setIsBusinessPanUserInitiated(true);
                    }}
                  />

                  {/* Business PAN Verification Status Indicator */}
                  <div className="position-absolute" style={{ right: '10px', top: '50%', transform: 'translateY(-50%)' }}>
                    {businessPanVerificationMutation.isPending && (
                      <div className="spinner-border spinner-border-sm text-primary" role="status" style={{ width: '16px', height: '16px' }}>
                        <span className="visually-hidden">Verifying...</span>
                      </div>
                    )}
                    {businessPanVerificationStatus === 'success' && (
                      <i className="fas fa-check text-success" title="Business PAN verified successfully" style={{ fontSize: '18px' }}></i>
                    )}
                    {businessPanVerificationStatus === 'error' && (
                      <i className="fas fa-times text-danger" title="Business PAN verification failed" style={{ fontSize: '18px' }}></i>
                    )}
                  </div>
                </div>

                <ErrorMessage
                  name="panCardDetails.businessPanNumber"
                  component="div"
                  className="clean-form-error"
                />

                {/* Business PAN Verification Status Message */}
                {businessPanVerificationStatus === 'success' && (
                  <small className="text-success mt-1 d-block">
                    <i className="fas fa-check me-1"></i>
                    Business PAN verified successfully
                  </small>
                )}
                {businessPanVerificationStatus === 'error' && (
                  <small className="text-danger mt-1 d-block">
                    <i className="fas fa-times me-1"></i>
                    Business PAN verification failed. Please check the number.
                  </small>
                )}
                {businessPanVerificationMutation.isPending && (
                  <small className="text-info mt-1 d-block">
                    <i className="fas fa-spinner fa-spin me-1"></i>
                    Verifying business PAN number...
                  </small>
                )}
              </div>
              <div className="col-md-6 mb-3">
                <label htmlFor="panCardDetails.companyName" className="clean-form-label">
                  Company Name in PAN <span className="text-danger">•</span>
                </label>
                <Field
                  type="text"
                  name="panCardDetails.companyName"
                  className={`clean-form-control ${
                    formik.touched.panCardDetails?.companyName && formik.errors.panCardDetails?.companyName
                      ? 'is-invalid'
                      : ''
                  }`}
                  placeholder="Enter company name as per PAN"
                />
                <ErrorMessage
                  name="panCardDetails.companyName"
                  component="div"
                  className="clean-form-error"
                />
              </div>

              <div className="col-md-6 mb-3">
                <DateInput
                  name="panCardDetails.dateOfIncorporation"
                  label="Date of Incorporation"
                  required={true}
                  fieldType={DATE_FIELD_TYPES.DATE_OF_INCORPORATION}
                  formik={formik}
                  helpText="Date when company was incorporated"
                />
              </div>

             
            </>
          )}

          {/* PAN Card Upload */}
          <DocumentUpload
            documentType={DOCUMENT_TYPES.PAN_CARD}
            title="PAN Card"
            accept=".pdf,.jpg,.jpeg,.png"
            existingDocuments={existingDocuments}
            onDocumentChange={onDocumentChange}
          />
        </div>
      </div>
    </div>
  );
};

export default PanCardDetailsForm;
