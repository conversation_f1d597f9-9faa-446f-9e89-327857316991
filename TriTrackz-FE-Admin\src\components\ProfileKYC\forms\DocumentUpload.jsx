import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import {
  useDocumentUpload,
  useDocumentDownload,
  useDocumentDelete,
  validateFile,
  getUserIdFromStorage,
  DOCUMENT_TYPES,
} from "@api/documentUploadHooks";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import {
  FaCheckCircle,
  FaDownload,
  FaTrash,
  FaFileAlt,
  FaTimes,
  FaExclamationCircle,
  FaExclamationTriangle,
} from "react-icons/fa";

const DocumentUpload = ({
  documentType,
  title,
  accept = ".pdf,.jpg,.jpeg,.png",
  existingDocuments = [],
  onDocumentChange,
  optional = false,
  targetUserId = null,
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadStatus, setUploadStatus] = useState(null); // 'success', 'error', null
  const [apiMessage, setApiMessage] = useState("");
  const [existingDocument, setExistingDocument] = useState(null);
  const [downloadLoading, setDownloadLoading] = useState({});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const fileInputRef = useRef(null);

  // Get user from Redux store
  const { user } = useSelector((state) => state.user);

  // Document download and delete hooks
  const documentDownloadMutation = useDocumentDownload();
  const documentDeleteMutation = useDocumentDelete({
    onSuccess: (response) => {
      console.log("Document deleted successfully:", response);
      setExistingDocument(null);
      setApiMessage(response?.message || "Document deleted successfully");
      setUploadStatus("success");
      // Notify parent component about document change
      if (onDocumentChange) {
        onDocumentChange();
      }
    },
    onError: (error) => {
      console.error("Document delete error:", error);
      setApiMessage(error.message || "Failed to delete document");
      setUploadStatus("error");
    },
  });

  // Find existing document for this document type
  useEffect(() => {
    const docTypeMap = {
      [DOCUMENT_TYPES.GST_CERTIFICATE]: "GstCertificate",
      [DOCUMENT_TYPES.PAN_CARD]: "PanCard",
      [DOCUMENT_TYPES.AADHAR_CARD]: "AadharCard",
      [DOCUMENT_TYPES.TRADE_LICENSE]: "TradeLicense",
      [DOCUMENT_TYPES.BUSINESS_LICENSE]: "BusinessLicense",
    };

    const targetDocType = docTypeMap[documentType];
    console.log("targetDocType:", targetDocType);

    const existingDoc = existingDocuments.find((doc) => {
      console.log("Checking document:", doc);
      return doc.documentType === targetDocType;
    });

    console.log("Found existing document:", existingDoc);

    if (existingDoc) {
      setExistingDocument(existingDoc);
      setUploadStatus("success");
      setApiMessage("Document already uploaded");
    } else {
      setExistingDocument(null);
      setUploadStatus(null);
      setApiMessage("");
    }
  }, [existingDocuments, documentType, title]);

  // Try to get user ID from different possible locations
  const getUserId = () => {
    // If targetUserId is provided (admin editing another user), use it
    if (targetUserId) {
      console.log("Using target user ID for document upload:", targetUserId);
      return targetUserId;
    }

    // Otherwise, try different possible user ID locations from Redux (for self-editing)
    const possibleIds = [
      user?.id,
      user?.userId,
      user?.user?.id,
      user?.user?.userId,
      user?.data?.id,
      user?.data?.userId,
    ];

    console.log("Possible user IDs from Redux:", possibleIds);

    let validId = possibleIds.find(
      (id) => id && id !== null && id !== undefined
    );

    // If no ID found in Redux, try localStorage as fallback
    if (!validId) {
      console.log("No user ID found in Redux, trying localStorage...");
      validId = getUserIdFromStorage();
      console.log("User ID from localStorage:", validId);
    }

    console.log("Final selected user ID:", validId);
    return validId;
  };

  // Document upload mutation
  const documentUploadMutation = useDocumentUpload({
    onSuccess: (response) => {
      console.log("Document upload API response:", response);

      const isSuccess = response?.success === true;

      if (isSuccess) {
        console.log("Document upload successful");

        setSelectedFile(null);
        setUploadStatus("success");
        setApiMessage(response?.message || "Document uploaded successfully");

        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }

        toast.success(`${title} uploaded successfully!`, {
          duration: 4000,
          position: "top-right",
        });

        if (onDocumentChange) {
          console.log("Calling onDocumentChange to refresh profile data");
          setTimeout(() => {
            console.log("Executing onDocumentChange after delay");
            onDocumentChange();
          }, 500);
        } else {
          console.warn("onDocumentChange callback not provided");
        }
      } else {
        console.warn("Document upload failed: success flag is false", response);
        setUploadStatus("error");
        setApiMessage(response?.message || `Failed to upload ${title}`);
      }
    },

    onError: (error) => {
      console.error("Document upload error:", error);

      let errorMessage = `Failed to upload ${title}`;

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const { errors } = error.response.data;
        if (Array.isArray(errors) && errors.length > 0) {
          errorMessage = errors[0];
        } else if (typeof errors === "object") {
          errorMessage = Object.values(errors)[0];
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      setUploadStatus("error");
      setApiMessage(errorMessage);
    },
  });

  // Handle file selection and auto-upload
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Reset previous status
    setUploadStatus(null);
    setApiMessage("");

    // Validate file
    const validation = validateFile(file, 5); // 5MB limit
    if (!validation.isValid) {
      setUploadStatus("error");
      setApiMessage(validation.errors.join(", "));
      return;
    }

    setSelectedFile(file);

    // Auto-upload the file after validation
    const userId = getUserId();
    if (!userId) {
      toast.error("User ID not found. Please login again.");
      console.error("Unable to find user ID in any location");
      return;
    }

    console.log("Auto-uploading document:", {
      fileName: file.name,
      fileSize: file.size,
      documentType: documentType,
      userId: userId,
    });

    // Upload document automatically
    documentUploadMutation.mutate({
      file: file,
      documentType: documentType,
      userId: userId,
    });
  };

  // Handle file upload
  const handleUpload = () => {
    if (!selectedFile) {
      toast.error("Please select a file first");
      return;
    }

    const userId = getUserId();
    if (!userId) {
      toast.error("User ID not found. Please login again.");
      console.error("Unable to find user ID in any location");
      return;
    }

    console.log("Uploading document:", {
      fileName: selectedFile.name,
      fileSize: selectedFile.size,
      documentType: documentType,
      userId: userId,
    });

    // Upload document
    documentUploadMutation.mutate({
      file: selectedFile,
      documentType: documentType,
      userId: userId,
    });
  };

  // Handle file removal
  const handleRemove = () => {
    setSelectedFile(null);
    setUploadStatus(null);
    setApiMessage("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle document download
  const handleDownload = async (documentFile) => {
    const userId = getUserId();
    if (!userId) {
      toast.error("Unable to download document. User ID not found.");
      return;
    }

    const documentKey = `${documentFile.documentType}-${documentFile.fileName}`;
    setDownloadLoading((prev) => ({ ...prev, [documentKey]: true }));

    try {
      toast.loading("Preparing download...", { id: `download-${documentKey}` });

      const docTypeMap = {
        GstCertificate: 0,
        PanCard: 2,
        AadharCard: 4,
      };

      const result = await documentDownloadMutation.mutateAsync({
        documentId: documentFile.documentId,
        documentType: docTypeMap[documentFile.documentType],
      });

      const fileName =
        documentFile.fileName?.replace(/[^a-zA-Z0-9.\-_]/g, "_") ||
        "downloaded-file";

      // Create download link
      const url = window.URL.createObjectURL(result.blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Document "${fileName}" downloaded successfully!`, {
        id: `download-${documentKey}`,
      });
    } catch (error) {
      console.error("Error downloading document:", error);
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to download document. Please try again.";
      toast.error(errorMessage, { id: `download-${documentKey}` });
    } finally {
      setDownloadLoading((prev) => ({ ...prev, [documentKey]: false }));
    }
  };

  // Handle document delete
  const handleDelete = (document) => {
    setDocumentToDelete(document);
    setShowDeleteConfirm(true);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Confirmation popup component using ModernActionPopup design
  const DeleteConfirmationPopup = () => {
    if (!showDeleteConfirm || !documentToDelete) return null;

    const handleCancel = () => {
      setShowDeleteConfirm(false);
      setDocumentToDelete(null);
    };

    const handleDelete = async () => {
      try {
        const docTypeMap = {
          GstCertificate: 0,
          PanCard: 2,
          AadharCard: 4,
          TradeLicense: 1,
          BusinessLicense: 3,
        };

        await documentDeleteMutation.mutateAsync({
          documentId: documentToDelete.documentId,
        });

        setShowDeleteConfirm(false);
        setDocumentToDelete(null);
      } catch (error) {
        console.error("Delete error:", error);
      }
    };

    return createPortal(
      <div
        className="modal-backdrop d-flex align-items-center justify-content-center"
        style={{ zIndex: 9999 }}
      >
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            {/* Close Button */}
            <button
              className="btn-close position-absolute top-0 end-0 m-3"
              onClick={handleCancel}
              aria-label="Close"
              disabled={documentDeleteMutation.isPending}
            ></button>

            {/* Content */}
            <div className="modal-body p-4">
              <div className="text-center">
                <h1 className="h4 fw-bold text-uppercase mb-3">
                  DELETE DOCUMENT
                </h1>
                <p className="text-muted mb-4">
                  Are you sure you want to delete this document?
                </p>

                {/* Document Preview */}
                <div className="d-flex align-items-center justify-content-center p-3 mb-4 border-2 border-dashed border-secondary bg-light rounded-3">
                  <FaFileAlt className="me-3 text-primary" size={24} />
                  <div className="text-start">
                    <div className="fw-medium text-primary">
                      {documentToDelete.fileName}
                    </div>
                    <small className="text-muted">
                      This action cannot be undone
                    </small>
                  </div>
                </div>

                <p className="text-muted small mb-4">
                  Once deleted, you'll need to upload a new document to complete
                  your profile verification.
                </p>

                {/* Action Buttons */}
                <div className="d-flex gap-3 justify-content-center">
                  <button
                    className="btn btn-outline-secondary px-4 rounded-3"
                    onClick={handleCancel}
                    disabled={documentDeleteMutation.isPending}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn btn-danger px-4"
                    onClick={handleDelete}
                    disabled={documentDeleteMutation.isPending}
                  >
                    {documentDeleteMutation.isPending ? (
                      <>
                        <span
                          className="spinner-border spinner-border-sm me-2"
                          role="status"
                          aria-hidden="true"
                        ></span>
                        Deleting...
                      </>
                    ) : (
                      <>
                        <FaTrash className="me-2" />
                        Delete Document
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
        ,
      </div>
    );
  };

  return (
    <>
      <DeleteConfirmationPopup />
      <div className="col-md-6 mb-3">
        <label className="form-label fw-medium">
          {title}{" "}
          {optional ? (
            <span className="text-muted">(Optional)</span>
          ) : (
            <span className="text-danger">*</span>
          )}
        </label>

        {/* Existing Document Display */}
        {existingDocument && (
          <div className="card border-success mb-3">
            <div className="card-body d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <FaFileAlt className="text-success me-3" size={24} />
                <div>
                  <div className="fw-medium ">{existingDocument.fileName}</div>
                  <div className="d-flex align-items-center text-success small">
                    <FaCheckCircle className="me-1" />
                    <span>Uploaded successfully</span>
                  </div>
                </div>
              </div>
              <div className="d-flex gap-2">
                <button
                  type="button"
                  className="btn btn-outline-success btn-sm"
                  onClick={() => handleDownload(existingDocument)}
                  disabled={
                    downloadLoading[
                      `${existingDocument.documentType}-${existingDocument.fileName}`
                    ] || documentDownloadMutation.isPending
                  }
                  title="Download document"
                >
                  {downloadLoading[
                    `${existingDocument.documentType}-${existingDocument.fileName}`
                  ] || documentDownloadMutation.isPending ? (
                    <span
                      className="spinner-border spinner-border-sm"
                      role="status"
                      aria-hidden="true"
                    ></span>
                  ) : (
                    <FaDownload />
                  )}
                </button>
                <button
                  type="button"
                  className="btn btn-outline-danger btn-sm"
                  onClick={() => handleDelete(existingDocument)}
                  disabled={documentDeleteMutation.isPending}
                  title="Delete and upload new"
                >
                  {documentDeleteMutation.isPending ? (
                    <span
                      className="spinner-border spinner-border-sm"
                      role="status"
                      aria-hidden="true"
                    ></span>
                  ) : (
                    <FaTrash />
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* File Input - Hide when document exists */}
        {!existingDocument && (
          <div className="mb-2">
            <input
              ref={fileInputRef}
              type="file"
              accept={accept}
              onChange={handleFileSelect}
              className="form-control"
              disabled={documentUploadMutation.isPending}
            />
          </div>
        )}

        {/* Selected File Info */}
        {selectedFile && (
          <div className="card border-primary mb-3">
            <div className="card-body d-flex align-items-center justify-content-between">
              <div className="d-flex align-items-center">
                <div className="text-primary me-3">
                  <FaFileAlt size={24} />
                </div>
                <div>
                  <div className="fw-medium text-dark">{selectedFile.name}</div>
                  <div className="text-muted small">
                    {formatFileSize(selectedFile.size)}
                  </div>
                </div>
              </div>

              <div className="d-flex align-items-center gap-2">
                {/* Status Icon */}
                {uploadStatus === "success" && (
                  <div className="text-success">
                    <FaCheckCircle size={20} />
                  </div>
                )}
                {uploadStatus === "error" && (
                  <div className="text-danger">
                    <FaTimes size={20} />
                  </div>
                )}

                {documentUploadMutation.isPending && (
                  <div className="d-flex align-items-center text-primary">
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    <small>Uploading...</small>
                  </div>
                )}

                {!documentUploadMutation.isPending &&
                  uploadStatus !== "success" && (
                    <button
                      type="button"
                      className="btn btn-outline-danger btn-sm"
                      onClick={handleRemove}
                      title="Remove file"
                    >
                      <FaTimes />
                    </button>
                  )}
              </div>
            </div>
          </div>
        )}

        {/* API Message Display - Only show error messages, success is shown in document card */}
        {apiMessage && uploadStatus === "error" && (
          <div className="alert alert-danger d-flex align-items-center mb-2">
            <FaExclamationCircle className="me-2" />
            <span>{apiMessage}</span>
          </div>
        )}

        {/* Help Text - Only show when no document exists */}
        {!existingDocument && (
          <small className="text-muted">
            Supported formats: PDF, JPG, JPEG, PNG (Max 5MB)
          </small>
        )}
      </div>
    </>
  );
};

export default DocumentUpload;
