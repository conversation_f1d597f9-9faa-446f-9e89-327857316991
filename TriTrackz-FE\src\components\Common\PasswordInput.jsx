import React, { useState } from 'react';
import { <PERSON>a<PERSON>ye, FaEyeSlash } from 'react-icons/fa';

const PasswordInput = ({
  name,
  value,
  onChange,
  onBlur,
  placeholder = "Enter password",
  className = "",
  isInvalid = false,
  disabled = false,
  showStrengthIndicator = false,
  style = {},
  autoComplete = "current-password",
  ...props
}) => {
  const [showPassword, setShowPassword] = useState(false);

  // Password strength calculation
  const calculatePasswordStrength = (password) => {
    if (!password) return { strength: 0, label: "", color: "" };
    
    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      numbers: /\d/.test(password),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    // Calculate score
    if (checks.length) score += 20;
    if (checks.lowercase) score += 20;
    if (checks.uppercase) score += 20;
    if (checks.numbers) score += 20;
    if (checks.special) score += 20;
    
    // Determine strength level
    if (score < 40) {
      return { strength: score, label: "Weak", color: "#dc3545" }; // Red
    } else if (score < 60) {
      return { strength: score, label: "Fair", color: "#fd7e14" }; // Orange
    } else if (score < 80) {
      return { strength: score, label: "Good", color: "#ffc107" }; // Yellow
    } else {
      return { strength: score, label: "Strong", color: "#198754" }; // Green
    }
  };

  const passwordStrength = showStrengthIndicator ? calculatePasswordStrength(value) : null;

  const inputStyle = {
    paddingRight: "45px",
    ...style
  };

  return (
    <div className="password-input-wrapper">
      <div className="position-relative">
        <input
          type={showPassword ? "text" : "password"}
          name={name}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
          disabled={disabled}
          autoComplete={autoComplete}
          className={`form-control password-input-with-toggle ${className} ${isInvalid ? 'is-invalid' : ''}`}
          style={inputStyle}
          {...props}
        />
        <button
          type="button"
          className="btn password-eye-toggle"
          onClick={() => setShowPassword(!showPassword)}
          disabled={disabled}
          tabIndex={-1}
        >
          {showPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
        </button>
      </div>

      {/* Password Strength Indicator */}
      {showStrengthIndicator && value && passwordStrength && (
        <div className="mt-2">
          <div className="d-flex justify-content-between align-items-center mb-1">
            <small style={{ color: "var(--text-secondary)", fontSize: "0.75rem" }}>
              Password Strength
            </small>
            <small style={{ 
              color: passwordStrength.color, 
              fontSize: "0.75rem",
              fontWeight: "600"
            }}>
              {passwordStrength.label}
            </small>
          </div>
          <div 
            className="progress" 
            style={{ 
              height: "4px", 
              backgroundColor: "var(--border-secondary)",
              borderRadius: "2px"
            }}
          >
            <div
              className="progress-bar"
              role="progressbar"
              style={{
                width: `${passwordStrength.strength}%`,
                backgroundColor: passwordStrength.color,
                transition: "all 0.3s ease",
                borderRadius: "2px"
              }}
              aria-valuenow={passwordStrength.strength}
              aria-valuemin="0"
              aria-valuemax="100"
            ></div>
          </div>
          
          {/* Password Requirements */}
          {passwordStrength.strength < 100 && (
            <div className="mt-2">
              <small style={{ color: "var(--text-secondary)", fontSize: "0.7rem" }}>
                Password should contain: uppercase, lowercase, numbers, and special characters
              </small>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PasswordInput;
