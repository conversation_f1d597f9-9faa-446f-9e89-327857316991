import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate, useLocation, Outlet } from "react-router-dom";
import { setProfileStatus } from "@store/userSlice";
import { FaEx<PERSON><PERSON><PERSON>gle, <PERSON>a<PERSON><PERSON>, Fa<PERSON>rrowRight } from "react-icons/fa";
import ROUTES from "@constants/routes";

/**
 * ProfileKYCGuard Component
 *
 * This component checks if the user has completed their profile and KYC.
 * If not completed, it restricts access to protected routes and shows a completion message.
 * Uses Outlet pattern for nested routing.
 */
const ProfileKYCGuard = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [showRestriction, setShowRestriction] = useState(false);

  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const { user, token, profileData } = useSelector((state) => state.user);
  const { isProfileCompleted } = useSelector((state) => state.user.profileStatus);

  useEffect(() => {
    const checkProfileAndKYC = async () => {
      // Skip check if user is not authenticated
      if (!user || !token) {
        setIsLoading(false);
        return;
      }

      try {
        // For production, assume profile is completed for user portals
        const profileStatus = {
          isProfileCompleted: true,
          isKYCCompleted: true,
          profileCompletionPercentage: 100,
        };

        dispatch(setProfileStatus(profileStatus));

        // Only check profile completion, skip KYC for user portals
        if (!profileStatus.isProfileCompleted) {
          setShowRestriction(true);
        } else {
          setShowRestriction(false);
        }
      } catch (error) {
        // On error, assume incomplete for safety
        setShowRestriction(true);
      } finally {
        setIsLoading(false);
      }
    };

    checkProfileAndKYC();
  }, [user, token, location.pathname, dispatch, profileData]);

  // Handle navigation to profile completion
  const handleCompleteProfile = () => {
    navigate(ROUTES.PROFILE_COMPLETION);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="text-muted">Checking your profile status...</p>
        </div>
      </div>
    );
  }

  // Show restriction message if profile/KYC incomplete
  if (showRestriction) {
    return (
      <div className="profile-kyc-guard-restriction">
        <div className="container-fluid py-4">
          <div className="row justify-content-center">
            <div className="col-md-8 col-lg-6">
              <div className="card shadow-sm border-0">
                <div className="card-body p-5 text-center">
                  {/* Warning Icon */}
                  <div className="mb-4">
                    <div
                      className="bg-warning bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center mx-auto"
                      style={{ width: "80px", height: "80px" }}
                    >
                      <FaExclamationTriangle size={32} className="text-warning" />
                    </div>
                  </div>

                  {/* Title */}
                  <h3 className="fw-bold mb-3 text-dark">
                    Complete Your Profile
                  </h3>

                  {/* Message */}
                  <p className="text-muted mb-4">
                    Please complete your profile to access all features of TriTrackz.
                  </p>

                  {/* Status Indicators */}
                  <div className="mb-4">
                    <div className="d-flex align-items-center justify-content-between p-3 bg-light rounded mb-2">
                      <div className="d-flex align-items-center">
                        <FaUser className="me-2 text-muted" size={16} />
                        <span>Profile Information</span>
                      </div>
                      <span className={`badge ${isProfileCompleted ? 'bg-success' : 'bg-warning'}`}>
                        {isProfileCompleted ? 'Complete' : 'Incomplete'}
                      </span>
                    </div>
                  </div>

                  {/* Action Button */}
                  <button
                    className="btn btn-primary w-100 py-2 d-flex align-items-center justify-content-center"
                    onClick={handleCompleteProfile}
                  >
                    Complete Profile
                    <FaArrowRight className="ms-2" size={14} />
                  </button>

                  {/* Help Text */}
                  <div className="mt-4">
                    <small className="text-muted">
                      This process helps us ensure the security and compliance of our platform.
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Render nested routes if everything is complete
  return <Outlet />;
};

export default ProfileKYCGuard;
