import { useMutation } from "@tanstack/react-query";
import { API_ENDPOINTS } from "@constants/apiNamevariables";
import { authAPI } from "./axiosInstance";

// Check user types
export const useCheckUserTypes = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.CHECK_USER_TYPES, {
        mobileNumber: data.mobileNumber,
        countryCode: data.countryCode || "IN",
      }),
    ...options,
  });

// Mobile registration
export const useMobileRegister = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.MOBILE_REGISTER, {
        mobileNumber: data.mobileNumber,
        countryCode: data.countryCode || "IN",
        userType: data.userType,
      }),
    ...options,
  });

// OTP verification
export const useVerifyOTP = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.VERIFY_OTP, {
        mobileNumber: data.mobileNumber,
        userType: data.userType,
        otpToken: data.otpToken,
        deviceInfo: data.deviceInfo || "web-browser",
      }),
    ...options,
  });

// Login
export const useLogin = (options) =>
  useMutation({
    mutationFn: (data) => authAPI.post(API_ENDPOINTS.LOGIN, data),
    ...options,
  });

// Mobile verification (for profile)
export const useMobileVerify = (options) =>
  useMutation({
    mutationFn: (data) => authAPI.post(API_ENDPOINTS.MOBILE_VERIFY, data),
    ...options,
  });

// Resend OTP
export const useResendOTP = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.MOBILE_REGISTER, {
        mobileNumber: data.mobileNumber,
        countryCode: data.countryCode || "IN",
        userType: data.userType,
        isResend: true,
      }),
    ...options,
  });

// Forgot Password (Request reset)
export const useForgotPassword = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.FORGOT_PASSWORD, {
        email: data.email,
      }),
    ...options,
  });

// Set New Password (Reset password with token)
export const useSetPassword = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.SET_PASSWORD, {
        email: data.email,
        token: data.token,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
      }),
    ...options,
  });

// Change Password (for logged in users)
export const useChangePassword = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.CHANGE_PASSWORD, {
        mobileNumber: data.mobileNumber,
        email: data.email,
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
        userType: data.userType,
        isAdmin: data.isAdmin || false,
      }),
    ...options,
  });
