import Axios from "axios";
import { store } from "@store/store";

// Auth API instance
const authAPI = Axios.create({
  baseURL: import.meta.env.VITE_APP_AUTH_URL + '/api',
});

// Base API instance (e.g., profile or others)
const baseAPI = Axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_URL + '/api',
});

// Request interceptor
const setAuthHeader = (config) => {
  const token = store.getState().user.token;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
};

// Response interceptor
const handleResponse = (response) => {
  if (response.status === 200 && !response.headers.get("content-disposition")) {
    return response.data;
  }
  return response;
};

// Attach interceptors
[authAPI, baseAPI].forEach((instance) => {
  instance.interceptors.request.use(setAuthHeader);
  instance.interceptors.response.use(handleResponse);
});

// Named exports
export { authAPI, baseAPI };
