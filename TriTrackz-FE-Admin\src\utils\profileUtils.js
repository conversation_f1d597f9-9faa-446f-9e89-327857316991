/**
 * Profile utility functions for handling profile data operations
 */

/**
 * Extract auto-fill data from comprehensive profile response
 * @param {Object} profileData - Profile data from API response
 * @returns {Object} - Auto-fill data for forms
 */
export const extractAutoFillData = (profileData) => {
  if (!profileData) return {};

  // Helper function to format date from ISO string to YYYY-MM-DD format for date inputs
  const formatDateForInput = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "";
      return date.toISOString().split("T")[0]; // Returns YYYY-MM-DD format
    } catch (error) {
      return "";
    }
  };

  // Format phone number for form input (ensure it has country code for react-phone-input-2)
  let formattedPhone = "";
  if (profileData.phoneNumber) {
    const cleaned = profileData.phoneNumber.replace(/\D/g, "");
    if (cleaned.length === 10) {
      formattedPhone = `91${cleaned}`; // Add country code for react-phone-input-2
    } else if (cleaned.length === 12 && cleaned.startsWith("91")) {
      formattedPhone = cleaned; // Already has country code
    } else {
      formattedPhone = profileData.phoneNumber; // Use as is
    }
  }

  // Format company contact phone
  let formattedCompanyPhone = "";
  if (profileData.companyContactPhone) {
    const cleaned = profileData.companyContactPhone.replace(/\D/g, "");
    if (cleaned.length === 10) {
      formattedCompanyPhone = `91${cleaned}`;
    } else if (cleaned.length === 12 && cleaned.startsWith("91")) {
      formattedCompanyPhone = cleaned;
    } else {
      formattedCompanyPhone = profileData.companyContactPhone;
    }
  }

  // Format dates
  const formatDate = (dateString) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toISOString().split("T")[0]; // YYYY-MM-DD format
    } catch (error) {
      console.warn("Error formatting date:", dateString, error);
      return "";
    }
  };

  return {
    // Basic Details
    // Basic Details
    firstName: profileData.firstName || "",
    lastName: profileData.lastName || "",
    displayName: profileData.displayName || "",
    fullName: profileData.fullName || "",
    email: profileData.email || "",
    phoneNumber: formattedPhone,
    userType: profileData.userType || null,

    // Company Details

    // Company Details
    companyName: profileData.companyName || "",
    brandName: profileData.brandName || "",
    legalName: profileData.legalName || "",
    tradeName: profileData.tradeName || "",
    companyContactEmail: profileData.companyContactEmail || "",
    companyContactPhone: formattedCompanyPhone,
    dateOfIncorporation: formatDate(profileData.dateOfIncorporation),

    // Address Details
    address: profileData.address || "",
    city: profileData.city || "",
    state: profileData.state || "",
    postalCode: profileData.postalCode || "",
    country: profileData.country || "",
    region: profileData.region || "",
    placeOfBusiness: profileData.placeOfBusiness || "",

    // GST Details
    gstNumber: profileData.gstNumber || "",
    gstName: profileData.gstName || "",
    gstRegistrationDate: formatDate(profileData.gstRegistrationDate),

    // PAN Details
    panNumber: profileData.panNumber || "",
    businessPanNumber: profileData.businessPanNumber || "",
    nameAsPerPan: profileData.nameAsPerPan || "",
    fatherOrHusbandNameInPan: profileData.fatherOrHusbandNameInPan || "",
    dateOfBirthInPan: formatDate(profileData.dateOfBirthInPan),

    // Aadhaar Details
    aadharNumber: profileData.aadharNumber || "",
    nameAsPerAadhaar: profileData.nameAsPerAadhaar || "",
    fatherOrHusbandNameInAadhaar:
      profileData.fatherOrHusbandNameInAadhaar || "",
    dateOfBirthInAadhaar: formatDate(profileData.dateOfBirthInAadhaar),
    genderInAadhaar: profileData.genderInAadhaar || "",

    // Bank Details
    accountNumber: profileData.accountNumber || "",
    accountHolderName: profileData.accountHolderName || "",
    bankName: profileData.bankName || "",
    ifscCode: profileData.ifscCode || "",

    // Other Details
    licenseNumber: profileData.licenseNumber || "",
    cinNumber: profileData.cinNumber || "",
    industryType: profileData.industryType || "",
    businessCategory: profileData.businessCategory || "",

    // Status Fields
    status: profileData.status || 0,
    isComplete: profileData.isComplete || false,
    kycStatus: profileData.kycStatus || "NotStarted",

    // Documents
    documents: profileData.documents || [],
  };
};

/**
 * Check if mobile number should be disabled (already filled and verified)
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether mobile number field should be disabled
 */
export const shouldDisableMobileNumber = (profileData) => {
  try {
    return !!(
      profileData?.phoneNumber && profileData.phoneNumber.trim() !== ""
    );
  } catch (error) {
    return false;
  }
};

/**
 * Check if profile is incomplete (status = 0)
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether profile is incomplete
 */
export const isProfileIncomplete = (profileData) => {
  try {
    return profileData?.status === 0;
  } catch (error) {
    return true; // Default to incomplete for safety
  }
};

/**
 * Check if KYC popup should be shown
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether KYC popup should be shown
 */
export const shouldShowKYCPopup = (profileData) => {
  return isProfileIncomplete(profileData);
};

/**
 * Check if profile is completed (status >= 1)
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether profile is completed
 */
export const isProfileCompleted = (profileData) => {
  try {
    return profileData?.status >= 1;
  } catch (error) {
    return false; // Default to incomplete for safety
  }
};

/**
 * Check if user is in edit mode (editing a completed profile)
 * @param {Object} profileData - Profile data from API response
 * @param {boolean} isEditMode - Explicit edit mode flag
 * @returns {boolean} - Whether user is editing a completed profile
 */
export const isEditingCompletedProfile = (profileData, isEditMode = false) => {
  try {
    // If explicitly in edit mode and profile is completed
    if (isEditMode && isProfileCompleted(profileData)) {
      return true;
    }

    // If profile is completed and has substantial data, assume edit mode
    if (isProfileCompleted(profileData) && profileData) {
      return true;
    }

    return false;
  } catch (error) {
    return false;
  }
};

/**
 * Check if auto-save should be enabled for the current profile state
 * @param {Object} profileData - Profile data from API response
 * @param {boolean} isEditMode - Explicit edit mode flag
 * @returns {boolean} - Whether auto-save should be enabled
 */
export const shouldEnableAutosave = (profileData, isEditMode = false) => {
  try {
    // If no profile data, allow auto-save (new form)
    if (!profileData) return true;

    // If profile is incomplete (status 0), allow auto-save
    if (isProfileIncomplete(profileData)) return true;

    // If profile is completed (status >= 1), disable auto-save to prevent
    // status from changing back to 0 during editing
    if (isProfileCompleted(profileData)) {
      console.log(
        "Auto-save disabled: Profile is completed (status:",
        profileData.status,
        ")"
      );
      return false;
    }

    // Default to allowing auto-save
    return true;
  } catch (error) {
    console.error("Error checking auto-save eligibility:", error);
    return true; // Default to allowing auto-save for safety
  }
};

/**
 * Get profile completion status message
 * @param {Object} profileData - Profile data from API response
 * @returns {string} - Status message
 */
export const getProfileStatusMessage = (profileData) => {
  if (!profileData) return "Profile data not available";

  switch (profileData.status) {
    case 0:
      return "Profile incomplete - Please complete your profile and KYC verification";
    case 1:
      return "Profile and KYC completed";
    case 2:
      return "Profile under review";
    case 3:
      return "Profile approved";
    case 4:
      return "Profile rejected - Please contact support";
    default:
      return "Profile status unknown";
  }
};

/**
 * Get profile status badge info for UI display
 * @param {Object} profileData - Profile data from API response
 * @returns {Object} - Badge info with class, text, and color
 */
export const getProfileStatusBadge = (profileData) => {
  if (!profileData)
    return { class: "bg-secondary", text: "Unknown", color: "secondary" };

  switch (profileData.status) {
    case 0:
      return { class: "bg-danger", text: "Incomplete", color: "danger" };
    case 1:
      return { class: "bg-success", text: "Complete", color: "success" };
    case 2:
      return { class: "bg-warning", text: "Under Review", color: "warning" };
    case 3:
      return { class: "bg-success", text: "Approved", color: "success" };
    case 4:
      return { class: "bg-danger", text: "Rejected", color: "danger" };
    default:
      return { class: "bg-secondary", text: "Unknown", color: "secondary" };
  }
};

/**
 * Format display name from profile data
 * @param {Object} profileData - Profile data from API response
 * @returns {string} - Formatted display name
 */
export const getDisplayName = (profileData) => {
  try {
    if (!profileData) return "User";

    if (profileData.displayName && profileData.displayName.trim()) {
      return profileData.displayName.trim();
    }

    if (profileData.firstName && profileData.lastName) {
      return `${profileData.firstName.trim()} ${profileData.lastName.trim()}`;
    }

    if (profileData.firstName && profileData.firstName.trim()) {
      return profileData.firstName.trim();
    }

    return "User";
  } catch (error) {
    return "User";
  }
};

/**
 * Get user role/type label from profile data
 * @param {Object} profileData - Profile data from API response
 * @returns {string} - User role label
 */
export const getUserRoleLabel = (profileData) => {
  if (!profileData?.userType) return "User";

  const userTypeLabels = {
    1: "Admin",
    2: "Transport Company",
    3: "Broker",
    4: "Carrier",
    5: "Driver",
    6: "Shipper Company", // Legacy mapping - should be cleaned up in database
    7: "Shipper Company",
    8: "Shipper Individual",
  };

  return userTypeLabels[profileData.userType] || "User";
};

/**
 * Check if profile data is loaded and valid
 * @param {Object} profileData - Profile data from API response
 * @returns {boolean} - Whether profile data is valid
 */
export const isValidProfileData = (profileData) => {
  return !!(profileData && profileData.id && profileData.userId);
};

/**
 * Get profile completion percentage
 * @param {Object} profileData - Profile data from API response
 * @returns {number} - Completion percentage (0-100)
 */
export const getProfileCompletionPercentage = (profileData) => {
  if (!profileData) return 0;

  let completionScore = 0;
  const totalFields = 10;

  // Basic info fields
  if (profileData.firstName) completionScore++;
  if (profileData.lastName) completionScore++;
  if (profileData.email) completionScore++;
  if (profileData.phoneNumber) completionScore++;

  // Address fields
  if (profileData.address) completionScore++;
  if (profileData.city) completionScore++;
  if (profileData.state) completionScore++;
  if (profileData.postalCode) completionScore++;

  // Document fields
  if (profileData.panNumber) completionScore++;
  if (profileData.aadharNumber) completionScore++;

  return Math.round((completionScore / totalFields) * 100);
};

/**
 * Phone number utility functions
 */

/**
 * Format phone number for API payload
 * @param {string} phoneNumber - Phone number from form (can be with or without country code)
 * @returns {Object} - Formatted phone data for API
 */
export const formatPhoneForAPI = (phoneNumber) => {
  try {
    if (!phoneNumber) {
      return {
        mobileNumber: "",
        countryCode: "IN",
      };
    }

    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, "");

    let formattedNumber = "";

    if (cleaned.length === 10) {
      // 10 digits - add country code
      formattedNumber = `+91${cleaned}`;
    } else if (cleaned.length === 12 && cleaned.startsWith("91")) {
      // 12 digits starting with 91 - already has country code
      formattedNumber = `+${cleaned}`;
    } else if (cleaned.length === 13 && cleaned.startsWith("91")) {
      // 13 digits starting with 91 (might have extra digit)
      formattedNumber = `+${cleaned.substring(0, 12)}`;
    } else {
      // Invalid format - return as is with +91 prefix
      formattedNumber = `+91${cleaned.slice(-10)}`;
    }

    return {
      mobileNumber: formattedNumber,
      countryCode: "IN",
    };
  } catch (error) {
    return {
      mobileNumber: phoneNumber || "",
      countryCode: "IN",
    };
  }
};

/**
 * Extract 10-digit phone number from any format
 * @param {string} phoneNumber - Phone number in any format
 * @returns {string} - 10-digit phone number
 */
export const extractTenDigitPhone = (phoneNumber) => {
  try {
    if (!phoneNumber) return "";

    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, "");

    if (cleaned.length >= 10) {
      // Return last 10 digits
      return cleaned.slice(-10);
    }

    return cleaned;
  } catch (error) {
    return "";
  }
};

/**
 * Validate 10-digit Indian mobile number
 * @param {string} phoneNumber - Phone number to validate
 * @returns {boolean} - Whether the number is valid
 */
export const isValidIndianMobile = (phoneNumber) => {
  try {
    const tenDigit = extractTenDigitPhone(phoneNumber);
    return /^[6-9]\d{9}$/.test(tenDigit);
  } catch (error) {
    return false;
  }
};

/**
 * Format phone number for display
 * @param {string} phoneNumber - Phone number to format
 * @returns {string} - Formatted phone number for display
 */
export const formatPhoneForDisplay = (phoneNumber) => {
  try {
    if (!phoneNumber) return "";

    const tenDigit = extractTenDigitPhone(phoneNumber);
    if (tenDigit.length === 10) {
      return `+91 ${tenDigit.slice(0, 5)} ${tenDigit.slice(5)}`;
    }

    return phoneNumber;
  } catch (error) {
    return phoneNumber || "";
  }
};

/**
 * Format form data for API submission
 * @param {Object} formData - Form data from ProfileKYC form
 * @param {string} userType - User type for conditional formatting
 * @returns {Object} - Formatted data for API submission
 */
export const formatFormDataForAPI = (formData, userType = null) => {
  try {
    if (!formData) return {};

    // Helper function to format dates to ISO string
    const formatDate = (dateString) => {
      if (!dateString) return null;
      return new Date(dateString).toISOString();
    };

    // Determine user type from form data if not provided
    const effectiveUserType = userType || formData.basicDetails?.userType;

    // Structure the payload to match API expectations with nested objects
    const structuredData = {
      // Basic Details
      basicDetails: {
        firstName: formData.basicDetails?.firstName || "",
        lastName: formData.basicDetails?.lastName || "",
        email: formData.basicDetails?.email || "",
        phoneNumber: formData.basicDetails?.phoneNumber || "",
        userType: effectiveUserType,
      },

      // Business Address
      businessAddress: {
        address: formData.businessAddress?.address || "",
        city: formData.businessAddress?.city || "",
        state: formData.businessAddress?.state || "",
        country: formData.businessAddress?.country || "",
        postalCode: formData.businessAddress?.pincode || "",
      },

      // PAN Card Details
      panCardDetails: {
        nameAsPerPan: formData.panCardDetails?.nameAsPerPan || "",
        fatherOrHusbandNameInPan:
          formData.panCardDetails?.fatherOrHusbandNameInPan || "",
        dateOfBirthInPan: formatDate(formData.panCardDetails?.dateOfBirthInPan),
        panNumber: formData.panCardDetails?.panNumber || "",
      },

      // Aadhaar Card Details
      aadhaarCardDetails: {
        aadharNumber: formData.aadhaarCardDetails?.aadhaarNumber || "",
        nameAsPerAadhaar: formData.aadhaarCardDetails?.nameAsPerAadhaar || "",
        fatherOrHusbandNameInAadhaar:
          formData.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar || "",
        dateOfBirthInAadhaar: formatDate(
          formData.aadhaarCardDetails?.dateOfBirthInAadhaar
        ),
        genderInAadhaar: formData.aadhaarCardDetails?.genderInAadhaar || "",
      },

      // Bank Details
      bankDetails: {
        accountNumber: formData.bankDetails?.accountNumber || "",
        accountHolderName: formData.bankDetails?.accountHolderName || "",
        bankName: formData.bankDetails?.bankName || "",
        ifscCode: formData.bankDetails?.ifscCode || "",
      },
    };

    // Add company-specific fields for company types
    const companyUserTypes = [2, 4, 7]; // TRANSPORT_COMPANY, CARRIER, SHIPPER_COMPANY
    if (companyUserTypes.includes(effectiveUserType)) {
      // Company Details
      structuredData.companyDetails = {
        companyName: formData.companyDetails?.companyName || "",
        brandName: formData.companyDetails?.brandName || "",
        companyLogo: formData.companyDetails?.companyLogo || "",
        companyContactEmail: formData.companyDetails?.companyContactEmail || "",
        companyContactPhone: formData.companyDetails?.companyContactPhone || "",
      };

      // GST Details
      structuredData.gstDetails = {
        gstNumber: formData.gstDetails?.gstNumber || "",
        legalName: formData.gstDetails?.legalName || "",
        tradeName: formData.gstDetails?.tradeName || "",
        placeOfBusiness: formData.gstDetails?.placeOfBusiness || "",
        registrationDate: formatDate(formData.gstDetails?.registrationDate),
        registrationNumber: formData.gstDetails?.registrationNumber || "",
      };

      // Add company-specific PAN fields
      structuredData.panCardDetails = {
        ...structuredData.panCardDetails,
        companyName: formData.panCardDetails?.companyName || "",
        dateOfIncorporation: formatDate(
          formData.panCardDetails?.dateOfIncorporation
        ),
        businessPanNumber: formData.panCardDetails?.businessPanNumber || "",
      };

      // CIN Number
      structuredData.cinNumber = {
        cin: formData.cinNumber?.cin || "",
      };
    }

    // Add industry details for Shipper Company
    if (effectiveUserType === 7) {
      // SHIPPER_COMPANY
      structuredData.industryDetails = {
        industryType: formData.industryDetails?.industryType || "",
        customIndustryType: formData.industryDetails?.customIndustryType || "",
        businessCategory: formData.industryDetails?.businessCategory || "",
        customBusinessCategory:
          formData.industryDetails?.customBusinessCategory || "",
      };
    }

    // Remove undefined fields
    const cleanedValues = JSON.parse(
      JSON.stringify(structuredData, (_, value) => {
        return value === undefined ? null : value;
      })
    );

    return cleanedValues;
  } catch (error) {
    console.error("Error formatting form data for API:", error);
    return formData || {};
  }
};
