import React from "react";
import { createPortal } from "react-dom";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import { FaTimes, FaArrowRight } from "react-icons/fa";
import { isProfileIncomplete } from "@utils/profileUtils";
import ROUTES from "@constants/routes";

const ModernActionPopup = ({ isOpen, onClose }) => {
  const { isKYCCompleted, isProfileCompleted } = useSelector((state) => state.user.profileStatus);
  const { profileData } = useSelector((state) => state.user);
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleAction = () => {
    onClose();
    // Check if profile is incomplete based on API status
    if (profileData && isProfileIncomplete(profileData)) {
      navigate(ROUTES.PROFILE_COMPLETION);
    } else if (!isProfileCompleted) {
      navigate(ROUTES.PROFILE_COMPLETION);
    } else if (!isKYCCompleted) {
      navigate(ROUTES.KYC_COMPLETION);
    }
  };

  const getContent = () => {
    // Check profile status from API first
    if (profileData && isProfileIncomplete(profileData)) {
      return {
        title: "COMPLETE PROFILE & KYC",
        subtitle: "Your profile setup is incomplete",
        description: "Complete your profile information and KYC verification to unlock all features and start using the platform.",
        buttonText: "Complete Profile"
      };
    } else if (!isProfileCompleted) {
      return {
        title: "COMPLETE PROFILE",
        subtitle: "Your profile setup is required",
        description: "Complete your profile information to unlock all features and start using the platform.",
        buttonText: "Complete Profile"
      };
    } else if (!isKYCCompleted) {
      return {
        title: "VERIFY IDENTITY",
        subtitle: "KYC verification is pending",
        description: "Complete your KYC verification to ensure secure transactions and full platform access.",
        buttonText: "Complete KYC"
      };
    }
    return null;
  };

  const content = getContent();
  if (!content) return null;

  return createPortal(
    <div className="modern-popup-overlay">
      <div className="modern-popup-container">
        {/* Close Button */}
        <button
          className="modern-popup-close"
          onClick={onClose}
          aria-label="Close"
        >
          <FaTimes size={20} />
        </button>

        {/* Content */}
        <div className="modern-popup-content">
          <div className="modern-popup-text">
            <h1 className="modern-popup-title">{content.title}</h1>
            <p className="modern-popup-subtitle">{content.subtitle}</p>
            <p className="modern-popup-description">{content.description}</p>

            <button
              className="modern-popup-button"
              onClick={handleAction}
            >
              {content.buttonText}
              <FaArrowRight size={14} className="ms-2" />
            </button>
          </div>
        </div>
      </div>
    </div>,
    document.body
  );
};

export default ModernActionPopup;
