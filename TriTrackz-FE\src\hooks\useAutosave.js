import { useCallback, useEffect, useRef, useState } from 'react';
import { useAutosaveComprehensiveProfile } from '@api/authHooks';
import { saveDraftToStorage, hasFormDataChanged } from '@utils/draftUtils';
import _ from 'lodash';

/**
 * Custom hook for autosave functionality with debouncing and profile status management
 * @param {Object} options - Configuration options
 * @param {string} options.userId - User ID
 * @param {Object} options.formData - Current form data
 * @param {number} options.currentStep - Current step index
 * @param {Array} options.completedSteps - Array of completed step indices
 * @param {number} options.debounceMs - Debounce delay in milliseconds (default: 60000 = 1 minute)
 * @param {boolean} options.enabled - Whether autosave is enabled (default: true)
 * @param {Object} options.profileData - Current profile data with status information
 * @param {boolean} options.isEditMode - Whether user is editing a completed profile
 * @param {Function} options.onSuccess - Success callback
 * @param {Function} options.onError - Error callback
 * @returns {Object} Autosave state and functions
 */
export const useAutosave = ({
  userId,
  formData,
  currentStep,
  completedSteps,
  debounceMs = 60000, // 1 minute default
  enabled = true,
  profileData = null,
  isEditMode = false,
  onSuccess,
  onError
}) => {
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSavedData, setLastSavedData] = useState(null);
  const [lastSaveTime, setLastSaveTime] = useState(null);
  const [saveError, setSaveError] = useState(null);

  const debounceTimeoutRef = useRef(null);
  const lastDataRef = useRef(null);
  const isMountedRef = useRef(true);

  // Determine if auto-save should be enabled based on profile status
  const shouldEnableAutosave = useCallback(() => {
    // If explicitly disabled, don't auto-save
    if (!enabled) return false;

    // If no profile data, allow auto-save (new form)
    if (!profileData) return true;

    // If profile status is 0 (incomplete), allow auto-save
    if (profileData.status === 0) return true;

    // If profile status is 1+ (completed) and user is in edit mode,
    // disable auto-save to prevent status from changing back to 0
    if (profileData.status >= 1) {

      return false;
    }

    // Default to allowing auto-save
    return true;
  }, [enabled, profileData]);

  // API mutation for autosave
  const { mutate: autosaveMutation } = useAutosaveComprehensiveProfile({
    onSuccess: (response) => {
      if (!isMountedRef.current) return;
      
      setIsAutoSaving(false);
      setLastSavedData(formData);
      setLastSaveTime(new Date());
      setSaveError(null);
      
      // Save to localStorage as backup
      saveDraftToStorage(userId, formData, currentStep, completedSteps);
      
      if (onSuccess) {
        onSuccess(response);
      }
    },
    onError: (error) => {
      if (!isMountedRef.current) return;
      
      setIsAutoSaving(false);
      setSaveError(error);
      
      // Still save to localStorage even if API fails
      saveDraftToStorage(userId, formData, currentStep, completedSteps);
      
      // Silent error handling - don't interrupt user
      
      if (onError) {
        onError(error);
      }
    }
  });

  // Format form data for API submission
  const formatFormDataForAPI = useCallback((data) => {
    try {
      // Format dates to ISO string
      const formatDate = (dateString) => {
        if (!dateString) return null;
        return new Date(dateString).toISOString();
      };

      const formattedData = {
        ...data,
        panCardDetails: {
          ...data.panCardDetails,
          dateOfBirthInPan: formatDate(data.panCardDetails?.dateOfBirthInPan),
          dateOfIncorporation: data.panCardDetails?.dateOfIncorporation
            ? formatDate(data.panCardDetails.dateOfIncorporation)
            : undefined
        },
        aadhaarCardDetails: {
          ...data.aadhaarCardDetails,
          dateOfBirthInAadhaar: formatDate(data.aadhaarCardDetails?.dateOfBirthInAadhaar)
        }
      };

      // Add GST registration date formatting if exists
      if (formattedData.gstDetails?.registrationDate) {
        formattedData.gstDetails.registrationDate = formatDate(formattedData.gstDetails.registrationDate);
      }

      // Remove undefined fields
      const cleanedData = JSON.parse(JSON.stringify(formattedData, (_, value) => {
        return value === undefined ? null : value;
      }));

      return cleanedData;
    } catch (error) {

      return data;
    }
  }, []);

  // Perform autosave
  const performAutosave = useCallback(() => {
    // Check if auto-save should be enabled based on profile status
    if (!shouldEnableAutosave() || !userId || !formData) {

      return;
    }

    // Check if data has actually changed
    if (lastDataRef.current && !hasFormDataChanged(formData, lastDataRef.current)) {
      return;
    }


    setIsAutoSaving(true);
    setSaveError(null);

    const formattedData = formatFormDataForAPI(formData);

    // For incomplete profiles (status 0), save as draft
    // For completed profiles, this code path shouldn't execute due to shouldEnableAutosave check
    autosaveMutation({
      userId,
      profileData: formattedData,
      isDraft: true, // This ensures status: 0 for incomplete profiles
      preserveStatus: null // Let the API use draft logic
    });

    lastDataRef.current = formData;
  }, [shouldEnableAutosave, userId, formData, formatFormDataForAPI, autosaveMutation, profileData?.status]);

  // Debounced autosave effect
  useEffect(() => {
    // Check if auto-save should be enabled
    if (!shouldEnableAutosave() || !userId || !formData) {
      return;
    }

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      performAutosave();
    }, debounceMs);

    // Cleanup function
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [formData, currentStep, completedSteps, shouldEnableAutosave, userId, debounceMs, performAutosave]);

  // Manual save function
  const saveNow = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    performAutosave();
  }, [performAutosave]);

  // Save on page unload - both localStorage and API
  const saveOnUnload = useCallback(() => {
    // Check if auto-save should be enabled based on profile status
    if (!shouldEnableAutosave() || !userId || !formData) {

      return;
    }

    try {
      // Save to localStorage immediately as backup
      saveDraftToStorage(userId, formData, currentStep, completedSteps);

      // Make synchronous API call for immediate save on unload
      const formattedData = formatFormDataForAPI(formData);

      // Determine status based on profile completion
      // - For incomplete profiles (status 0): save as draft (status: 0)
      // - For completed profiles (status >= 1): preserve existing status
      // Note: This function should only run for incomplete profiles due to shouldEnableAutosave check
      const status = profileData?.status >= 1 ? profileData.status : 0;

      const payload = {
        ...formattedData,
        status: status // Preserve existing status for completed profiles, use 0 for incomplete
      };



      // Use synchronous XMLHttpRequest for page unload (more reliable than sendBeacon for PUT)
      const xhr = new XMLHttpRequest();
      const apiUrl = `${import.meta.env.VITE_APP_BASE_URL}/api/ComprehensiveProfile/${userId}`;

      xhr.open('PUT', apiUrl, false); // false = synchronous
      xhr.setRequestHeader('Content-Type', 'application/json');

      // Add authorization header if token exists
      const token = localStorage.getItem('token');
      if (token) {
        xhr.setRequestHeader('Authorization', `Bearer ${token}`);
      }

      try {
        xhr.send(JSON.stringify(payload));
      } catch (apiError) {
        // Silent error handling
      }

    } catch (error) {
      // Silent error handling
    }
  }, [shouldEnableAutosave, userId, formData, currentStep, completedSteps, formatFormDataForAPI, profileData?.status]);

  // Cleanup on unmount
  useEffect(() => {
    isMountedRef.current = true;
    
    return () => {
      isMountedRef.current = false;
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Calculate time since last save
  const timeSinceLastSave = lastSaveTime 
    ? Math.floor((new Date() - lastSaveTime) / 1000)
    : null;

  return {
    isAutoSaving,
    lastSavedData,
    lastSaveTime,
    timeSinceLastSave,
    saveError,
    saveNow,
    saveOnUnload,
    hasUnsavedChanges: lastDataRef.current ? hasFormDataChanged(formData, lastDataRef.current) : true,
    isAutosaveEnabled: shouldEnableAutosave(),
    profileStatus: profileData?.status
  };
};
