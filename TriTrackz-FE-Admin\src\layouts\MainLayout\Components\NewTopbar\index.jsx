import React, {
  useState,
  useEffect,
  useRef,
  useMemo,
  useCallback,
  memo,
} from "react";
import {
  <PERSON>a<PERSON><PERSON>,
  <PERSON>a<PERSON>ell,
  <PERSON>a<PERSON><PERSON>,
  FaSignOutAlt,
  FaSun,
  FaMoon,
} from "react-icons/fa";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";

import { getDisplayName } from "@utils/profileUtils";
import { useBootstrapTheme } from "@contexts/BootstrapThemeContext";
import { Avatar } from "@components/Common";
import ROUTES from "@constants/routes";

const NewTopbar = memo(({ onToggleMobileSidebar, onLogout }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileMenu, setShowProfileMenu] = useState(false);

  const { user } = useSelector((state) => state.user);
  const navigate = useNavigate();
  const { toggleTheme, isDark } = useBootstrapTheme();

  // Memoize display name to prevent recalculation
  const displayName = useMemo(() => getDisplayName(user), [user]);
  const notificationRef = useRef(null);
  const profileRef = useRef(null);

  // Memoize event handlers to prevent unnecessary re-renders
  const toggleNotifications = useCallback(() => {
    console.log("Toggling notifications:", !showNotifications);
    setShowNotifications(!showNotifications);
    setShowProfileMenu(false);
  }, [showNotifications]);

  const toggleProfileMenu = useCallback(() => {
    console.log("Toggling profile menu:", !showProfileMenu);
    setShowProfileMenu(!showProfileMenu);
    setShowNotifications(false);
  }, [showProfileMenu]);

  const handleProfileSettingsClick = useCallback(() => {
    navigate(ROUTES.PROFILE_SETTINGS);
    setShowProfileMenu(false);
  }, [navigate]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        notificationRef.current &&
        !notificationRef.current.contains(event.target)
      ) {
        setShowNotifications(false);
      }
      if (profileRef.current && !profileRef.current.contains(event.target)) {
        setShowProfileMenu(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Memoized notification dropdown to prevent unnecessary re-renders
  const NotificationDropdown = useMemo(() => {
    if (!showNotifications) return null;

    return (
      <div className="dropdown-menu show position-absolute end-0 mt-2 shadow">
        <div className="dropdown-header">
          <h6>Notifications</h6>
        </div>
        <div className="dropdown-item-text text-center py-4">
          <p className="text-muted mb-0">No new notifications</p>
        </div>
      </div>
    );
  }, [showNotifications]);

  // Memoized profile dropdown to prevent unnecessary re-renders
  const ProfileDropdown = useMemo(() => {
    if (!showProfileMenu) return null;

    return (
      <div className="dropdown-menu show position-absolute end-0 mt-2 shadow">
        <div className="d-flex align-items-center p-3 border-bottom">
          <div className="position-relative d-inline-block">
            <Avatar
              name={displayName}
              size="large"
              className=""
              showBorder={false}
            />
            <span
              className="position-absolute bottom-0 end-0 bg-success border border-2 border-white rounded-circle"
              style={{ width: "12px", height: "12px" }}
            ></span>
          </div>
          <div className="ms-3">
            <h6 className="mb-1">{displayName}</h6>
            <p className="mb-0 small text-muted">Admin User</p>
          </div>
        </div>
        <div className="py-2">
          <button
            className="dropdown-item d-flex align-items-center"
            onClick={handleProfileSettingsClick}
          >
            <FaUser size={14} />
            <span className="ms-3">Profile Settings</span>
          </button>
          <button
            className="dropdown-item d-flex align-items-center"
            onClick={toggleTheme}
          >
            {isDark ? <FaSun size={14} /> : <FaMoon size={14} />}
            <span className="ms-3">{isDark ? "Light Mode" : "Dark Mode"}</span>
          </button>
          <button
            className="dropdown-item d-flex align-items-center text-danger"
            onClick={onLogout}
          >
            <FaSignOutAlt size={14} />
            <span className="ms-3">Logout</span>
          </button>
        </div>
      </div>
    );
  }, [
    showProfileMenu,
    displayName,
    handleProfileSettingsClick,
    onLogout,
    toggleTheme,
    isDark,
  ]);

  return (
    <div className="d-flex align-items-center justify-content-between p-3 position-sticky top-0 z-1">
      {/* Left Section - Menu Toggle */}
      <div className="d-flex align-items-center">
        <button
          className="btn btn-outline-light btn-sm d-flex align-items-center justify-content-center border-0"
          onClick={onToggleMobileSidebar}
          aria-label="Toggle menu"
        >
          <FaBars size={18} />
        </button>
      </div>

      {/* Right Section - Notifications & Profile */}
      <div className="d-flex align-items-center gap-3">
        {/* Notifications */}
        <div className="position-relative" ref={notificationRef}>
          <button
            className="btn btn-outline-light btn-sm d-flex align-items-center justify-content-center position-relative border-0"
            onClick={toggleNotifications}
            aria-label="Notifications"
          >
            <FaBell size={18} />
            <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
              3
            </span>
          </button>

          {NotificationDropdown}
        </div>

        {/* Profile Avatar with Online Indicator */}
        <div className="position-relative" ref={profileRef}>
          <button
            className="btn p-0 bg-transparent border-0 rounded-circle"
            onClick={toggleProfileMenu}
            aria-label="Profile menu"
          >
            <div className="position-relative d-inline-block">
              <Avatar
                name={displayName}
                size="small"
                className=""
                showBorder={false}
              />
              <span
                className="position-absolute bottom-0 end-0 bg-success border border-2 border-dark rounded-circle"
                style={{ width: "12px", height: "12px" }}
              ></span>
            </div>
          </button>

          {ProfileDropdown}
        </div>
      </div>
    </div>
  );
});

NewTopbar.displayName = "NewTopbar";

export default NewTopbar;
