import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { GENDER_OPTIONS } from '@constants/enum';
import { DOCUMENT_TYPES } from '@api/documentUploadHooks';
import { DATE_FIELD_TYPES } from '@utils/dateValidationUtils';
import SelectInput from '@components/Common/SelectInput';
import DateInput from '@components/Common/DateInput';
import DocumentUpload from './DocumentUpload';

const AadhaarCardDetailsForm = ({ formik, existingDocuments = [], onDocumentChange }) => {
  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Aadhaar Card Details</h3>
        <p className="clean-section-subtitle">Please provide your Aadhaar card information</p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.aadhaarNumber" className="clean-form-label">
              Aadhaar Number <span className="text-danger">•</span>
            </label>
            <Field
              type="text"
              name="aadhaarCardDetails.aadhaarNumber"
              className={`clean-form-control ${
                formik.touched.aadhaarCardDetails?.aadhaarNumber && formik.errors.aadhaarCardDetails?.aadhaarNumber
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter 12-digit Aadhaar number"
              maxLength="12"
            />
            <ErrorMessage
              name="aadhaarCardDetails.aadhaarNumber"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.nameAsPerAadhaar" className="clean-form-label">
              Name as per Aadhaar <span className="text-danger">•</span>
            </label>
            <Field
              type="text"
              name="aadhaarCardDetails.nameAsPerAadhaar"
              className={`clean-form-control ${
                formik.touched.aadhaarCardDetails?.nameAsPerAadhaar && formik.errors.aadhaarCardDetails?.nameAsPerAadhaar
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter name as per Aadhaar"
            />
            <ErrorMessage
              name="aadhaarCardDetails.nameAsPerAadhaar"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.fatherOrHusbandNameInAadhaar" className="clean-form-label">
              Father/Husband Name in Aadhaar <span className="text-danger">*</span>
            </label>
            <Field
              type="text"
              name="aadhaarCardDetails.fatherOrHusbandNameInAadhaar"
              className={`clean-form-control ${
                formik.touched.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar && formik.errors.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar
                  ? 'is-invalid'
                  : ''
              }`}
              placeholder="Enter father/husband name"
            />
            <ErrorMessage
              name="aadhaarCardDetails.fatherOrHusbandNameInAadhaar"
              component="div"
              className="clean-form-error"
            />
          </div>

          <div className="col-md-6 mb-3">
            <DateInput
              name="aadhaarCardDetails.dateOfBirthInAadhaar"
              label="Date of Birth in Aadhaar"
              required={true}
              fieldType={DATE_FIELD_TYPES.DATE_OF_BIRTH}
              formik={formik}
              helpText="Must be at least 18 years old"
            />
          </div>

          <div className="col-md-6 mb-3">
            <label htmlFor="aadhaarCardDetails.genderInAadhaar" className="clean-form-label">
              Gender in Aadhaar <span className="text-danger">*</span>
            </label>
            <SelectInput
              name="aadhaarCardDetails.genderInAadhaar"
              value={formik.values.aadhaarCardDetails?.genderInAadhaar || ''}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              options={GENDER_OPTIONS}
              placeholder="Select gender"
              isInvalid={formik.touched.aadhaarCardDetails?.genderInAadhaar && formik.errors.aadhaarCardDetails?.genderInAadhaar}
              isValid={formik.touched.aadhaarCardDetails?.genderInAadhaar && formik.values.aadhaarCardDetails?.genderInAadhaar && !formik.errors.aadhaarCardDetails?.genderInAadhaar}
              isClearable={false}
              isSearchable={false}
            />
            <ErrorMessage
              name="aadhaarCardDetails.genderInAadhaar"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Aadhaar Card Upload */}
          <DocumentUpload
            documentType={DOCUMENT_TYPES.AADHAR_CARD}
            title="Aadhaar Card"
            accept=".pdf,.jpg,.jpeg,.png"
            existingDocuments={existingDocuments}
            onDocumentChange={onDocumentChange}
          />
        </div>
      </div>
    </div>
  );
};

export default AadhaarCardDetailsForm;
