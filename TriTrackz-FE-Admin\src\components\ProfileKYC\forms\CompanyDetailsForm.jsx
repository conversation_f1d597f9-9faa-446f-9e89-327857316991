import React from "react";
import { Field, ErrorMessage } from "formik";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";

const CompanyDetailsForm = ({
  formik,
  existingDocuments,
  onDocumentChange,
}) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">Company Details</h3>
        <p className="text-muted mb-0">
          Please provide your company information
        </p>
      </div>
      <div className="row">
        <div className="col-md-6 mb-3">
          <label
            htmlFor="companyDetails.companyName"
            className="form-label fw-medium"
          >
            Company Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="companyDetails.companyName"
            className={`form-control ${
              formik.touched.companyDetails?.companyName &&
              formik.errors.companyDetails?.companyName
                ? "is-invalid"
                : formik.touched.companyDetails?.companyName &&
                  formik.values.companyDetails?.companyName
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter company name"
          />
          <ErrorMessage
            name="companyDetails.companyName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="companyDetails.brandName"
            className="form-label fw-medium"
          >
            Brand Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="companyDetails.brandName"
            className={`form-control ${
              formik.touched.companyDetails?.brandName &&
              formik.errors.companyDetails?.brandName
                ? "is-invalid"
                : formik.touched.companyDetails?.brandName &&
                  formik.values.companyDetails?.brandName
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter brand name"
          />
          <ErrorMessage
            name="companyDetails.brandName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="companyDetails.companyContactEmail"
            className="form-label fw-medium"
          >
            Company Contact Email <span className="text-danger">*</span>
          </label>
          <Field
            type="email"
            name="companyDetails.companyContactEmail"
            className={`form-control ${
              formik.touched.companyDetails?.companyContactEmail &&
              formik.errors.companyDetails?.companyContactEmail
                ? "is-invalid"
                : formik.touched.companyDetails?.companyContactEmail &&
                  formik.values.companyDetails?.companyContactEmail
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter company contact email"
          />
          <ErrorMessage
            name="companyDetails.companyContactEmail"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="companyDetails.companyContactPhone"
            className="form-label fw-medium"
          >
            Company Contact Phone <span className="text-danger">*</span>
          </label>
          <PhoneInput
            country="in"
            value={formik.values.companyDetails?.companyContactPhone || ""}
            onChange={(value) => {
              formik.setFieldValue(
                "companyDetails.companyContactPhone",
                value || ""
              );
            }}
            onBlur={() =>
              formik.setFieldTouched("companyDetails.companyContactPhone", true)
            }
            placeholder="Enter company contact phone"
            inputClass={`form-control ${
              formik.touched.companyDetails?.companyContactPhone &&
              formik.errors.companyDetails?.companyContactPhone
                ? "is-invalid"
                : formik.touched.companyDetails?.companyContactPhone &&
                  formik.values.companyDetails?.companyContactPhone &&
                  !formik.errors.companyDetails?.companyContactPhone
                ? "is-valid"
                : ""
            }`}
            containerClass="phone-input-container"
            buttonClass="phone-input-button"
          />
          <ErrorMessage
            name="companyDetails.companyContactPhone"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-12 mb-3">
          <label
            htmlFor="companyDetails.companyLogo"
            className="form-label fw-medium"
          >
            Company Logo URL
          </label>
          <Field
            type="url"
            name="companyDetails.companyLogo"
            className={`form-control ${
              formik.touched.companyDetails?.companyLogo &&
              formik.errors.companyDetails?.companyLogo
                ? "is-invalid"
                : formik.touched.companyDetails?.companyLogo &&
                  formik.values.companyDetails?.companyLogo
                ? "is-valid"
                : ""
            }`}
            placeholder="Enter company logo URL (optional)"
          />
          <ErrorMessage
            name="companyDetails.companyLogo"
            component="div"
            className="invalid-feedback"
          />
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsForm;
