import { useState, useEffect, useCallback, useRef } from 'react';
import { loadDraftFromStorage, removeDraftFromStorage, getDraftMetadata } from '@utils/draftUtils';

/**
 * Custom hook for loading and managing draft data
 * @param {string} userId - User ID
 * @param {Function} onDraftLoaded - Callback when draft is loaded
 * @param {Function} onDraftRestored - Callback when draft is restored to form
 * @returns {Object} Draft loading state and functions
 */
export const useDraftLoader = (userId, onDraftLoaded, onDraftRestored) => {
  const [draftData, setDraftData] = useState(null);
  const [isDraftLoading, setIsDraftLoading] = useState(false);
  const [draftError, setDraftError] = useState(null);
  const [showDraftPrompt, setShowDraftPrompt] = useState(false);
  const [draftMetadata, setDraftMetadata] = useState(null);

  // Store callbacks in refs to avoid dependency issues
  const onDraftLoadedRef = useRef(onDraftLoaded);
  const onDraftRestoredRef = useRef(onDraftRestored);

  // Update refs when callbacks change
  useEffect(() => {
    onDraftLoadedRef.current = onDraftLoaded;
    onDraftRestoredRef.current = onDraftRestored;
  });

  // Load draft data on mount - only depends on userId
  useEffect(() => {
    if (!userId) return;

    const loadDraft = async () => {
      setIsDraftLoading(true);
      setDraftError(null);

      try {
        // First check metadata
        const metadata = getDraftMetadata(userId);
        setDraftMetadata(metadata);

        // Load full draft data
        const draft = loadDraftFromStorage(userId);

        if (draft) {
          // Always load draft data for form initialization
          setDraftData(draft);

          // Check if we should show the restore prompt
          const currentSessionStart = sessionStorage.getItem('session-start-time');
          const draftTimestamp = new Date(draft.timestamp).getTime();
          const sessionStartTime = currentSessionStart ? parseInt(currentSessionStart) : Date.now();

          // Show draft prompt if:
          // 1. Draft exists
          // 2. Draft was created before current session started (different session)
          // 3. Draft is at least 2 minutes old (reduced from 5 minutes)
          const isDraftFromPreviousSession = draftTimestamp < sessionStartTime;
          const isDraftOldEnough = (Date.now() - draftTimestamp) > (2 * 60 * 1000); // 2 minutes

          if (isDraftFromPreviousSession && isDraftOldEnough) {
            setShowDraftPrompt(true);
          }

          // Always call onDraftLoaded for form initialization
          if (onDraftLoadedRef.current) {
            onDraftLoadedRef.current(draft);
          }
        }
      } catch (error) {

        setDraftError(error);
      } finally {
        setIsDraftLoading(false);
      }
    };

    // Set session start time if not already set
    if (!sessionStorage.getItem('session-start-time')) {
      sessionStorage.setItem('session-start-time', Date.now().toString());
    }

    loadDraft();
  }, [userId]); // Only userId dependency

  // Restore draft to form
  const restoreDraft = useCallback(() => {
    if (!draftData) return false;

    try {
      if (onDraftRestoredRef.current) {
        onDraftRestoredRef.current(draftData);
      }

      setShowDraftPrompt(false);
      return true;
    } catch (error) {
      console.error('Error restoring draft:', error);
      setDraftError(error);
      return false;
    }
  }, [draftData]); // Removed onDraftRestored dependency

  // Dismiss draft prompt
  const dismissDraft = useCallback(() => {
    setShowDraftPrompt(false);
    setDraftData(null);
  }, []);

  // Delete draft
  const deleteDraft = useCallback(() => {
    if (!userId) return false;

    try {
      const success = removeDraftFromStorage(userId);
      if (success) {
        setDraftData(null);
        setDraftMetadata(null);
        setShowDraftPrompt(false);
      }
      return success;
    } catch (error) {
      console.error('Error deleting draft:', error);
      setDraftError(error);
      return false;
    }
  }, [userId]);

  // Check if draft exists
  const hasDraft = Boolean(draftData);

  // Get draft age in minutes
  const getDraftAge = useCallback(() => {
    if (!draftData?.timestamp) return null;
    
    const now = new Date();
    const draftTime = new Date(draftData.timestamp);
    return Math.floor((now - draftTime) / (1000 * 60)); // Age in minutes
  }, [draftData]);

  // Format draft age for display
  const formatDraftAge = useCallback(() => {
    const ageInMinutes = getDraftAge();
    if (ageInMinutes === null) return '';

    if (ageInMinutes < 1) return 'just now';
    if (ageInMinutes < 60) return `${ageInMinutes} minute${ageInMinutes === 1 ? '' : 's'} ago`;
    
    const ageInHours = Math.floor(ageInMinutes / 60);
    if (ageInHours < 24) return `${ageInHours} hour${ageInHours === 1 ? '' : 's'} ago`;
    
    const ageInDays = Math.floor(ageInHours / 24);
    return `${ageInDays} day${ageInDays === 1 ? '' : 's'} ago`;
  }, [getDraftAge]);

  return {
    draftData,
    draftMetadata,
    isDraftLoading,
    draftError,
    showDraftPrompt,
    hasDraft,
    getDraftAge,
    formatDraftAge,
    restoreDraft,
    dismissDraft,
    deleteDraft,
    setShowDraftPrompt
  };
};
