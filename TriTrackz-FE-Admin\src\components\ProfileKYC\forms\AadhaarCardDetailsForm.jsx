import React from "react";
import { Field, ErrorMessage } from "formik";
import { GENDER_OPTIONS } from "@constants/enum";
import { DOCUMENT_TYPES } from "@api/documentUploadHooks";
import DocumentUpload from "./DocumentUpload";

const AadhaarCardDetailsForm = ({
  formik,
  existingDocuments = [],
  onDocumentChange,
  targetUserId = null,
}) => {
  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">Aadhaar Card Details</h3>
        <p className="text-muted mb-0">
          Please provide your Aadhaar card information
        </p>
      </div>
      <div className="row">
        <div className="col-md-6 mb-3">
          <label
            htmlFor="aadhaarCardDetails.aadhaarNumber"
            className="form-label fw-medium"
          >
            Aadhaar Number <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="aadhaarCardDetails.aadhaarNumber"
            className={`form-control ${
              formik.touched.aadhaarCardDetails?.aadhaarNumber &&
              formik.errors.aadhaarCardDetails?.aadhaarNumber
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter 12-digit Aadhaar number"
            maxLength="12"
          />
          <ErrorMessage
            name="aadhaarCardDetails.aadhaarNumber"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="aadhaarCardDetails.nameAsPerAadhaar"
            className="form-label fw-medium"
          >
            Name as per Aadhaar <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="aadhaarCardDetails.nameAsPerAadhaar"
            className={`form-control ${
              formik.touched.aadhaarCardDetails?.nameAsPerAadhaar &&
              formik.errors.aadhaarCardDetails?.nameAsPerAadhaar
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter name as per Aadhaar"
          />
          <ErrorMessage
            name="aadhaarCardDetails.nameAsPerAadhaar"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="aadhaarCardDetails.fatherOrHusbandNameInAadhaar"
            className="form-label fw-medium"
          >
            Father/Husband Name in Aadhaar{" "}
            <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="aadhaarCardDetails.fatherOrHusbandNameInAadhaar"
            className={`form-control ${
              formik.touched.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar &&
              formik.errors.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter father/husband name"
          />
          <ErrorMessage
            name="aadhaarCardDetails.fatherOrHusbandNameInAadhaar"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="aadhaarCardDetails.dateOfBirthInAadhaar"
            className="form-label fw-medium"
          >
            Date of Birth in Aadhaar <span className="text-danger">*</span>
          </label>
          <Field
            type="date"
            name="aadhaarCardDetails.dateOfBirthInAadhaar"
            className={`form-control ${
              formik.touched.aadhaarCardDetails?.dateOfBirthInAadhaar &&
              formik.errors.aadhaarCardDetails?.dateOfBirthInAadhaar
                ? "is-invalid"
                : ""
            }`}
          />
          <div className="form-text">Must be at least 18 years old</div>
          <ErrorMessage
            name="aadhaarCardDetails.dateOfBirthInAadhaar"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="aadhaarCardDetails.genderInAadhaar"
            className="form-label fw-medium"
          >
            Gender in Aadhaar <span className="text-danger">*</span>
          </label>
          <Field
            as="select"
            name="aadhaarCardDetails.genderInAadhaar"
            className={`form-select ${
              formik.touched.aadhaarCardDetails?.genderInAadhaar &&
              formik.errors.aadhaarCardDetails?.genderInAadhaar
                ? "is-invalid"
                : formik.touched.aadhaarCardDetails?.genderInAadhaar &&
                  formik.values.aadhaarCardDetails?.genderInAadhaar &&
                  !formik.errors.aadhaarCardDetails?.genderInAadhaar
                ? "is-valid"
                : ""
            }`}
          >
            <option value="">Select gender</option>
            {GENDER_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </Field>
          <ErrorMessage
            name="aadhaarCardDetails.genderInAadhaar"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-12 mb-3">
          {/* Aadhaar Card Upload */}
          <DocumentUpload
            documentType={DOCUMENT_TYPES.AADHAR_CARD}
            title="Aadhaar Card"
            accept=".pdf,.jpg,.jpeg,.png"
            existingDocuments={existingDocuments}
            onDocumentChange={onDocumentChange}
            targetUserId={targetUserId}
          />
        </div>
      </div>
    </div>
  );
};

export default AadhaarCardDetailsForm;
