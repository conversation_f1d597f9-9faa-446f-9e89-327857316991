/**
 * Date Validation Utilities
 * Provides comprehensive date validation functions for forms
 */

/**
 * Date field types with specific validation rules
 */
export const DATE_FIELD_TYPES = {
  DATE_OF_BIRTH: "dateOfBirth",
  DATE_OF_INCORPORATION: "dateOfIncorporation",
  REGISTRATION_DATE: "registrationDate",
  GST_REGISTRATION: "gstRegistration",
  FUTURE_DATE: "futureDate",
  PAST_DATE: "pastDate",
  ANY_DATE: "anyDate",
};

/**
 * Age validation constants
 */
export const AGE_LIMITS = {
  MIN_AGE: 18, // Minimum age for legal documents
  MAX_AGE: 120, // Maximum realistic age
  MIN_INCORPORATION_YEARS: 1950, // Minimum year for company incorporation
  MAX_FUTURE_YEARS: 10, // Maximum years in future for certain dates
};

/**
 * Calculate age from date of birth
 * @param {Date|string} dateOfBirth - Date of birth
 * @returns {number} Age in years
 */
export const calculateAge = (dateOfBirth) => {
  if (!dateOfBirth) return 0;

  const birthDate = new Date(dateOfBirth);
  const today = new Date();

  if (isNaN(birthDate.getTime())) return 0;

  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();

  if (
    monthDiff < 0 ||
    (monthDiff === 0 && today.getDate() < birthDate.getDate())
  ) {
    age--;
  }

  return age;
};

/**
 * Get minimum date based on field type
 * @param {string} fieldType - Type of date field
 * @returns {Date} Minimum allowed date
 */
export const getMinDate = (fieldType) => {
  const today = new Date();

  switch (fieldType) {
    case DATE_FIELD_TYPES.DATE_OF_BIRTH:
      // Minimum age of 18 years
      const minBirthDate = new Date();
      minBirthDate.setFullYear(today.getFullYear() - AGE_LIMITS.MAX_AGE);
      return minBirthDate;

    case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
    case DATE_FIELD_TYPES.REGISTRATION_DATE:
    case DATE_FIELD_TYPES.GST_REGISTRATION:
      // Minimum year for business registration
      return new Date(AGE_LIMITS.MIN_INCORPORATION_YEARS, 0, 1);

    case DATE_FIELD_TYPES.FUTURE_DATE:
      // Today is minimum for future dates
      return today;

    case DATE_FIELD_TYPES.PAST_DATE:
      // 100 years ago as reasonable minimum
      const minPastDate = new Date();
      minPastDate.setFullYear(today.getFullYear() - 100);
      return minPastDate;

    default:
      // Default to 100 years ago
      const defaultMinDate = new Date();
      defaultMinDate.setFullYear(today.getFullYear() - 100);
      return defaultMinDate;
  }
};

/**
 * Get maximum date based on field type
 * @param {string} fieldType - Type of date field
 * @returns {Date} Maximum allowed date
 */
export const getMaxDate = (fieldType) => {
  const today = new Date();

  switch (fieldType) {
    case DATE_FIELD_TYPES.DATE_OF_BIRTH:
      // Maximum age of 18 years (minimum legal age)
      const maxBirthDate = new Date();
      maxBirthDate.setFullYear(today.getFullYear() - AGE_LIMITS.MIN_AGE);
      return maxBirthDate;

    case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
    case DATE_FIELD_TYPES.REGISTRATION_DATE:
    case DATE_FIELD_TYPES.GST_REGISTRATION:
    case DATE_FIELD_TYPES.PAST_DATE:
      // Today is maximum for past dates
      return today;

    case DATE_FIELD_TYPES.FUTURE_DATE:
      // Maximum 10 years in future
      const maxFutureDate = new Date();
      maxFutureDate.setFullYear(
        today.getFullYear() + AGE_LIMITS.MAX_FUTURE_YEARS
      );
      return maxFutureDate;

    default:
      // Default to today
      return today;
  }
};

/**
 * Format date for HTML input (YYYY-MM-DD)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
export const formatDateForInput = (date) => {
  if (!date) return "";

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return "";

  const year = dateObj.getFullYear();
  const month = String(dateObj.getMonth() + 1).padStart(2, "0");
  const day = String(dateObj.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

/**
 * Validate date based on field type
 * @param {Date|string} date - Date to validate
 * @param {string} fieldType - Type of date field
 * @returns {object} Validation result with isValid and error message
 */
export const validateDate = (date, fieldType) => {
  if (!date) {
    return { isValid: false, error: "Date is required" };
  }

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return { isValid: false, error: "Enter a valid date" };
  }

  const minDate = getMinDate(fieldType);
  const maxDate = getMaxDate(fieldType);

  if (dateObj < minDate) {
    switch (fieldType) {
      case DATE_FIELD_TYPES.DATE_OF_BIRTH:
        return { isValid: false, error: "Enter a realistic date of birth" };
      case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
        return {
          isValid: false,
          error: `Date cannot be before ${AGE_LIMITS.MIN_INCORPORATION_YEARS}`,
        };
      case DATE_FIELD_TYPES.REGISTRATION_DATE:
      case DATE_FIELD_TYPES.GST_REGISTRATION:
        return {
          isValid: false,
          error: `Registration date cannot be before ${AGE_LIMITS.MIN_INCORPORATION_YEARS}`,
        };
      default:
        return { isValid: false, error: "Date is too far in the past" };
    }
  }

  if (dateObj > maxDate) {
    switch (fieldType) {
      case DATE_FIELD_TYPES.DATE_OF_BIRTH:
        const age = calculateAge(dateObj);
        return {
          isValid: false,
          error: `Age must be at least ${AGE_LIMITS.MIN_AGE} years`,
        };
      case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
      case DATE_FIELD_TYPES.REGISTRATION_DATE:
      case DATE_FIELD_TYPES.GST_REGISTRATION:
      case DATE_FIELD_TYPES.PAST_DATE:
        return { isValid: false, error: "Date cannot be in the future" };
      case DATE_FIELD_TYPES.FUTURE_DATE:
        return {
          isValid: false,
          error: `Date cannot be more than ${AGE_LIMITS.MAX_FUTURE_YEARS} years in the future`,
        };
      default:
        return { isValid: false, error: "Date cannot be in the future" };
    }
  }

  return { isValid: true, error: null };
};

/**
 * Get date validation attributes for HTML input
 * @param {string} fieldType - Type of date field
 * @returns {object} Object with min and max attributes
 */
export const getDateInputAttributes = (fieldType) => {
  const minDate = getMinDate(fieldType);
  const maxDate = getMaxDate(fieldType);

  return {
    min: formatDateForInput(minDate),
    max: formatDateForInput(maxDate),
  };
};

/**
 * Check if a person is of legal age (18+)
 * @param {Date|string} dateOfBirth - Date of birth
 * @returns {boolean} True if person is 18 or older
 */
export const isLegalAge = (dateOfBirth) => {
  const age = calculateAge(dateOfBirth);
  return age >= AGE_LIMITS.MIN_AGE;
};

/**
 * Get age validation message
 * @param {Date|string} dateOfBirth - Date of birth
 * @returns {string|null} Error message or null if valid
 */
export const getAgeValidationMessage = (dateOfBirth) => {
  if (!dateOfBirth) return "Date of birth is required";

  const age = calculateAge(dateOfBirth);
  if (age < AGE_LIMITS.MIN_AGE) {
    return `You must be at least ${AGE_LIMITS.MIN_AGE} years old`;
  }

  if (age > AGE_LIMITS.MAX_AGE) {
    return "Please enter a realistic date of birth";
  }

  return null;
};

/**
 * Validate age based on date of birth
 * @param {Date|string} dateOfBirth - Date of birth
 * @returns {object} Validation result with isValid and error message
 */
export const validateAge = (dateOfBirth) => {
  if (!dateOfBirth) {
    return { isValid: false, error: "Date of birth is required" };
  }

  const age = calculateAge(dateOfBirth);

  if (age < AGE_LIMITS.MIN_AGE) {
    return {
      isValid: false,
      error: `You must be at least ${AGE_LIMITS.MIN_AGE} years old`,
    };
  }

  if (age > AGE_LIMITS.MAX_AGE) {
    return {
      isValid: false,
      error: `Age cannot exceed ${AGE_LIMITS.MAX_AGE} years`,
    };
  }

  return { isValid: true, error: null };
};
