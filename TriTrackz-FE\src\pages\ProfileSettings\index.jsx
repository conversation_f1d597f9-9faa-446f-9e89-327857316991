import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useGetProfile } from "@api/profileHooks";
import {
  useDocumentDownload,
  getRequiredDocumentsByUserType,
} from "@api/documentUploadHooks";
import { USER_TYPE_LABELS, USER_TYPES } from "@constants/enum";
import {
  FaUser,
  FaBuilding,
  FaMapMarkerAlt,
  FaSpinner,
  FaExclamationTriangle,
  FaChartLine,
  FaIdCard,
  FaIndustry,
  FaEye,
  FaEyeSlash,
  FaArrowLeft,
  FaFileAlt,
  FaDownload,
  FaCheckCircle,
  FaFilePdf,
  FaFileImage,
} from "react-icons/fa";

// Import the ProfileKYCForm for editing
import { ProfileKYCForm } from "@components/ProfileKYC";
import { useSidebarActions } from "@hooks/useSidebarActions";

const ProfileSettings = () => {
  const { user } = useSelector((state) => state.user);
  const [isEditing, setIsEditing] = useState(false);
  const { openChangePassword, openChangePhone } = useSidebarActions();
  const [showNumbers, setShowNumbers] = useState({
    pan: false,
    aadhaar: false,
    account: false,
  });
  const [downloadLoading, setDownloadLoading] = useState({});

  // Fetch profile data using ComprehensiveProfile endpoint
  const {
    data: profileResponse,
    isLoading,
    error,
    refetch,
  } = useGetProfile(user?.userId || user?.id);

  const profileData = profileResponse?.data;
  const userType = profileData?.userType || user?.userType;

  // Get documents from profile data
  const userDocuments = profileData?.documents || [];

  // Document download hook
  const documentDownloadMutation = useDocumentDownload();

  // Toggle function for show/hide numbers
  const toggleNumberVisibility = (type) => {
    setShowNumbers((prev) => ({
      ...prev,
      [type]: !prev[type],
    }));
  };

  // Get file icon and color based on file extension
  const getFileIconAndColor = (fileName) => {
    if (!fileName) return { icon: FaFileAlt, color: "#6c757d" };

    const extension = fileName.toLowerCase().split(".").pop();

    switch (extension) {
      case "pdf":
        return { icon: FaFilePdf, color: "#dc3545" }; // Red for PDF
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
      case "bmp":
      case "webp":
        return { icon: FaFileImage, color: "#28a745" }; // Green for images
      case "doc":
      case "docx":
        return { icon: FaFileAlt, color: "#0d6efd" }; // Blue for Word docs
      case "xls":
      case "xlsx":
        return { icon: FaFileAlt, color: "#198754" }; // Green for Excel
      case "txt":
        return { icon: FaFileAlt, color: "#6f42c1" }; // Purple for text
      default:
        return { icon: FaFileAlt, color: "#6c757d" }; // Gray for unknown
    }
  };

  // Handle document download
  const handleDocumentDownload = async (documentFile) => {
    const documentKey = `${documentFile.documentType}-${documentFile.fileName}`;
    setDownloadLoading((prev) => ({ ...prev, [documentKey]: true }));

    try {
      const docTypeMap = {
        GstCertificate: 0,
        PanCard: 2,
        AadharCard: 4,
        TradeLicense: 1,
        BusinessLicense: 3,
        DrivingLicense: 5,
        RegulatoryDoc: 12,
      };

      const documentTypeId = docTypeMap[documentFile.documentType];

      if (documentTypeId === undefined) {
        throw new Error(`Unknown document type: ${documentFile.documentType}`);
      }

      const result = await documentDownloadMutation.mutateAsync({
        documentId: documentFile.documentId,
        documentType: documentTypeId,
      });

      const fileName =
        documentFile.fileName?.replace(/[^a-zA-Z0-9.\-_]/g, "_") ||
        "downloaded-file";

      // Create download link using global document object
      const url = window.URL.createObjectURL(result.blob);
      const link = globalThis.document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      globalThis.document.body.appendChild(link);
      link.click();
      globalThis.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      alert(`Failed to download document: ${error.message}`);
    } finally {
      setDownloadLoading((prev) => ({ ...prev, [documentKey]: false }));
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <div className="container-fluid py-4">
        <div className="row">
          <div className="col-12 text-center">
            <FaSpinner className="fa-spin me-2" size={20} />
            <span>Loading profile data...</span>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container-fluid py-4">
        <div className="row">
          <div className="col-12">
            <div className="alert alert-danger d-flex align-items-center">
              <FaExclamationTriangle className="me-2" size={20} />
              <div>
                <strong>Error loading profile data:</strong>
                <br />
                {error?.response?.data?.message ||
                  error?.message ||
                  "Failed to load profile data"}
                <br />
                <button
                  className="btn btn-sm btn-outline-danger mt-2"
                  onClick={() => refetch()}
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show message if no profile data
  if (!profileData) {
    return (
      <div className="container-fluid py-4">
        <div className="row">
          <div className="col-12">
            <div className="alert alert-warning d-flex align-items-center">
              <FaExclamationTriangle className="me-2" size={20} />
              <div>
                <strong>No profile data found</strong>
                <br />
                Please complete your profile registration first.
                <br />
                <button
                  className="btn btn-sm btn-outline-warning mt-2"
                  onClick={() => refetch()}
                >
                  Refresh
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Helper functions to determine what to show based on user type
  const isCompanyType = () => {
    return [
      USER_TYPES.TRANSPORT_COMPANY,
      USER_TYPES.CARRIER,
      USER_TYPES.SHIPPER_COMPANY,
    ].includes(userType);
  };

  const isShipperCompany = () => {
    return userType === USER_TYPES.SHIPPER_COMPANY;
  };

  if (isLoading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: "400px" }}
      >
        <div className="text-center">
          <FaSpinner className="fa-spin text-primary mb-3" size={32} />
          <p className="text-muted">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: "400px" }}
      >
        <div className="text-center">
          <FaExclamationTriangle className="text-warning mb-3" size={32} />
          <p className="text-muted">Failed to load profile data</p>
          <button className="btn btn-primary btn-sm" onClick={() => refetch()}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Handle edit mode toggle
  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
  };

  const handleSaveSuccess = () => {
    setIsEditing(false);
    refetch(); // Refresh the profile data
  };

  // If in edit mode, check status before showing form
  if (isEditing) {
    // Status 3 (Approved) - Don't allow editing
    if (profileData?.status === 3) {
      return (
        <div className="container-fluid py-4">
          <div className="row mb-4">
            <div className="col-12">
              <div className="d-flex align-items-center mb-3">
                <button
                  className="btn btn-link p-0 me-3 text-decoration-none"
                  onClick={handleCancelEdit}
                  style={{ color: "inherit" }}
                >
                  <FaArrowLeft size={16} />
                </button>
                <div>
                  <h4 className="mb-1">Profile Status</h4>
                  <p className="text-muted mb-0">
                    Your KYC status information
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="row justify-content-center">
            <div className="col-lg-8 col-12">
              <div className="alert alert-success d-flex align-items-center p-4 shadow-sm">
                <FaCheckCircle className="me-3" size={24} />
                <div>
                  <h5 className="mb-2">KYC Completed Successfully!</h5>
                  <p className="mb-2">
                    Your profile has been verified and approved by our admin team.
                  </p>
                  <p className="mb-0 text-muted">
                    <strong>Note:</strong> To make changes to your approved profile, please contact our admin team for assistance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      );
    }

    // Status 4 (Rejected) - Show rejection reason and allow re-submission
    if (profileData?.status === 4) {
      return (
        <div className="container-fluid py-4">
          <div className="row mb-4">
            <div className="col-12">
              <div className="d-flex align-items-center mb-3">
                <button
                  className="btn btn-link p-0 me-3 text-decoration-none"
                  onClick={handleCancelEdit}
                  style={{ color: "inherit" }}
                >
                  <FaArrowLeft size={16} />
                </button>
                <div>
                  <h4 className="mb-1">KYC Rejected - Resubmit Required</h4>
                  <p className="text-muted mb-0">
                    Please update your information and resubmit
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="row mb-4 justify-content-center">
            <div className="col-lg-8 col-12">
              <div className="alert alert-danger d-flex align-items-start p-4 shadow-sm">
                <FaExclamationTriangle className="me-3 mt-1" size={20} />
                <div>
                  <h6 className="mb-2">KYC Application Rejected</h6>
                  <p className="mb-2">
                    Your KYC application has been rejected by our admin team. Please review the feedback below and update your information accordingly.
                  </p>
                  {profileData?.rejectionReason && (
                    <div className="mt-3 p-3 bg-light rounded">
                      <strong>Rejection Reason:</strong>
                      <p className="mb-0 mt-1">{profileData.rejectionReason}</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="row">
            <div className="col-12">
              <ProfileKYCForm
                initialData={profileData}
                isEditMode={true}
                onSuccess={handleSaveSuccess}
                onCancel={handleCancelEdit}
                hideHeader={true}
              />
            </div>
          </div>
        </div>
      );
    }

    // Status 0, 1, 2 - Allow normal editing
    return (
      <div className="container-fluid py-4">
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex align-items-center mb-3">
              <button
                className="btn btn-link p-0 me-3 text-decoration-none"
                onClick={handleCancelEdit}
                style={{ color: "inherit" }}
              >
                <FaArrowLeft size={16} />
              </button>
              <div>
                <h4 className="mb-1">Edit Profile</h4>
                <p className="text-muted mb-0">
                  Update your account information
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="row">
          <div className="col-12">
            <ProfileKYCForm
              initialData={profileData}
              isEditMode={true}
              onSuccess={handleSaveSuccess}
              onCancel={handleCancelEdit}
              hideHeader={true}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid py-3 px-4 compact-profile-settings-full">
      {/* Compact Header */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h5 className="mb-1 fw-semibold">Profile Settings</h5>
              <p className="text-muted mb-0 small">
                Manage your account information and preferences
              </p>
            </div>
            <div className="d-flex gap-2">
              <button
                className="btn btn-primary btn-sm"
                onClick={openChangePassword}
                disabled={isLoading}
              >
                Change Password
              </button>
              <button
                className="btn btn-primary btn-sm"
                onClick={openChangePhone}
                disabled={isLoading}
              >
                Change Phone Number
              </button>
              <button
                className="btn btn-primary btn-sm"
                onClick={handleEditClick}
                disabled={isLoading}
              >
                Edit Profile
              </button>
            </div>
          </div>
        </div>
      </div>



      {/* Compact Profile Header Card */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card border-0 shadow-sm compact-profile-card">
            <div className="card-body p-3">
              <div className="row align-items-center">
                <div className="col-auto">
                  <div
                    className="profile-avatar-compact bg-primary text-white rounded-circle d-flex align-items-center justify-content-center"
                    style={{ width: "60px", height: "60px", fontSize: "24px" }}
                  >
                    {profileData?.firstName?.charAt(0) || "U"}
                  </div>
                </div>
                <div className="col">
                  <h6 className="mb-1 fw-semibold">
                    {profileData?.displayName ||
                      `${profileData?.firstName} ${profileData?.lastName}`}
                  </h6>
                  <p className="text-muted mb-1 small">
                    {USER_TYPE_LABELS[profileData?.userType] || "User"}
                  </p>
                  <div className="d-flex flex-wrap gap-2">
                    <div className="d-flex align-items-center">
                      <span className="text-muted me-1 small">Status:</span>
                      <span
                        className={`badge badge-sm ${
                          profileData?.isActive ? "bg-success" : "bg-secondary"
                        }`}
                      >
                        {profileData?.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Basic Details and Business Address Side by Side */}
      <div className="row mb-3">
        {/* Basic Details Card - Left Side */}
        <div className="col-lg-6 col-md-12">
          <div className="card border-0 shadow-sm compact-details-card h-100">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <FaUser className="text-primary me-2" size={16} />
                <h6 className="mb-0 fw-semibold">Basic Details</h6>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">First Name</label>
                    <div className="compact-field-value">
                      <span>{profileData?.firstName || "N/A"}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Last Name</label>
                    <div className="compact-field-value">
                      <span>{profileData?.lastName || "N/A"}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Display Name</label>
                    <div className="compact-field-value">
                      <span>{profileData?.displayName || "N/A"}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">User Type</label>
                    <div className="compact-field-value">
                      <span>{USER_TYPE_LABELS[userType] || "N/A"}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Email</label>
                    <div className="compact-field-value">
                      <span>{profileData?.email || "N/A"}</span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Phone Number</label>
                    <div className="compact-field-value">
                      <span>{profileData?.phoneNumber || "N/A"}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Business Address Card - Right Side */}
        <div className="col-lg-6 col-md-12">
          <div className="card border-0 shadow-sm compact-details-card h-100">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <FaMapMarkerAlt className="text-primary me-2" size={16} />
                <h6 className="mb-0 fw-semibold">Business Address</h6>
              </div>
            </div>
            <div className="card-body p-4 d-flex align-items-center justify-content-center">
              <div className="address-format-container">
                <div className="formatted-address">
                  <div className="address-line">
                    {profileData?.address || "Street Address Not Available"}
                  </div>
                  <div className="address-line">
                    {[
                      profileData?.city,
                      profileData?.state,
                      profileData?.postalCode,
                    ]
                      .filter(Boolean)
                      .join(", ") || "City, State, Postal Code Not Available"}
                  </div>
                  <div className="address-line">
                    {profileData?.country || "Country Not Available"}
                  </div>
                  {profileData?.region && (
                    <div className="address-line region-line">
                      Region: {profileData.region}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Company Details Card - 2x6 Layout */}
      {isCompanyType() && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card border-0 shadow-sm compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <FaBuilding className="text-primary me-2" size={16} />
                  <h6 className="mb-0 fw-semibold">Company Details</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Company Name
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.companyName || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">Brand Name</label>
                      <div className="compact-field-value">
                        <span>{profileData?.brandName || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">Legal Name</label>
                      <div className="compact-field-value">
                        <span>{profileData?.legalName || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">Trade Name</label>
                      <div className="compact-field-value">
                        <span>{profileData?.tradeName || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Company Email
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.companyContactEmail || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Company Phone
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.companyContactPhone || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Place of Business
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.placeOfBusiness || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  {profileData?.companyLogo && (
                    <div className="col-lg-2 col-md-4 col-sm-6">
                      <div className="compact-field-group">
                        <label className="compact-field-label">
                          Company Logo
                        </label>
                        <div className="compact-field-value">
                          <img
                            src={profileData.companyLogo}
                            alt="Company Logo"
                            style={{
                              width: "40px",
                              height: "40px",
                              objectFit: "contain",
                            }}
                            className="me-2 rounded"
                          />
                          <small className="text-muted">Logo uploaded</small>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full Width GST Details Card - 2x6 Layout */}
      {isCompanyType() && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card border-0 shadow-sm compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <FaBuilding className="text-primary me-2" size={16} />
                  <h6 className="mb-0 fw-semibold">GST Details</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">GST Number</label>
                      <div className="compact-field-value">
                        <span>{profileData?.gstNumber || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">GST Name</label>
                      <div className="compact-field-value">
                        <span>{profileData?.gstName || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        GST Registration Number
                      </label>
                      <div className="compact-field-value">
                        <span>
                          {profileData?.gstRegistrationNumber || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        GST Registration Date
                      </label>
                      <div className="compact-field-value">
                        <span>
                          {formatDate(profileData?.gstRegistrationDate) ||
                            "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full Width CIN Number Card - 2x6 Layout */}
      {isCompanyType() && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card border-0 shadow-sm compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <FaIdCard className="text-primary me-2" size={16} />
                  <h6 className="mb-0 fw-semibold">CIN Number</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">CIN Number</label>
                      <div className="compact-field-value">
                        <span>{profileData?.cinNumber || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Date of Incorporation
                      </label>
                      <div className="compact-field-value">
                        <span>
                          {formatDate(profileData?.dateOfIncorporation) ||
                            "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Physical Card Style Layout - PAN, Aadhaar, Bank Cards */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card border-0 shadow-sm compact-details-card">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <FaIdCard className="text-primary me-2" size={16} />
                <h6 className="mb-0 fw-semibold">Identity & Financial Cards</h6>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-3">
                {/* PAN Card */}
                <div className="col-lg-4 col-md-6 col-sm-12">
                  <div className="compact-physical-card pan-card">
                    <div className="compact-card-header">
                      <span className="card-title">PAN CARD</span>
                    </div>
                    <div className="compact-card-content">
                      <div className="row g-2">
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Name</span>
                            <span className="field-value">
                              {profileData?.nameAsPerPan || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Father's Name</span>
                            <span className="field-value">
                              {profileData?.fatherOrHusbandNameInPan || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Date of Birth</span>
                            <span className="field-value">
                              {formatDate(profileData?.dateOfBirthInPan) ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-12">
                          <div className="compact-card-field pan-number-field">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="field-label">PAN Number</span>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-primary p-1"
                                onClick={() => toggleNumberVisibility("pan")}
                                style={{ fontSize: "0.7rem", lineHeight: 1 }}
                              >
                                {showNumbers.pan ? (
                                  <FaEyeSlash size={10} />
                                ) : (
                                  <FaEye size={10} />
                                )}
                              </button>
                            </div>
                            <span className="field-value pan-number">
                              {showNumbers.pan
                                ? profileData?.panNumber || "**********"
                                : "••••••••••"}
                            </span>
                          </div>
                        </div>
                        {isCompanyType() && profileData?.businessPanNumber && (
                          <>
                            <div className="col-6">
                              <div className="compact-card-field">
                                <span className="field-label">
                                  Business PAN
                                </span>
                                <span className="field-value pan-number">
                                  {profileData?.businessPanNumber}
                                </span>
                              </div>
                            </div>
                            <div className="col-6"></div>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Aadhaar Card */}
                <div className="col-lg-4 col-md-6 col-sm-12">
                  <div className="compact-physical-card aadhaar-card">
                    <div className="compact-card-header">
                      <span className="card-title">AADHAAR CARD</span>
                    </div>
                    <div className="compact-card-content">
                      <div className="row g-2">
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Name</span>
                            <span className="field-value">
                              {profileData?.nameAsPerAadhaar || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Father's Name</span>
                            <span className="field-value">
                              {profileData?.fatherOrHusbandNameInAadhaar ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Date of Birth</span>
                            <span className="field-value">
                              {formatDate(profileData?.dateOfBirthInAadhaar) ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Gender</span>
                            <span className="field-value">
                              {profileData?.genderInAadhaar || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-12">
                          <div className="compact-card-field aadhaar-number-field">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="field-label">
                                Aadhaar Number
                              </span>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-warning p-1"
                                onClick={() =>
                                  toggleNumberVisibility("aadhaar")
                                }
                                style={{ fontSize: "0.7rem", lineHeight: 1 }}
                              >
                                {showNumbers.aadhaar ? (
                                  <FaEyeSlash size={10} />
                                ) : (
                                  <FaEye size={10} />
                                )}
                              </button>
                            </div>
                            <span className="field-value aadhaar-number">
                              {showNumbers.aadhaar
                                ? profileData?.aadharNumber ||
                                  profileData?.aadhaarNumber ||
                                  profileData?.adhaarNumber ||
                                  "**********12"
                                : "••••-••••-••••"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Bank Card */}
                <div className="col-lg-4 col-md-6 col-sm-12">
                  <div className="compact-physical-card bank-card">
                    <div className="compact-card-header">
                      <span className="card-title">BANK DETAILS</span>
                    </div>
                    <div className="compact-card-content">
                      <div className="row g-2">
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Account Holder</span>
                            <span className="field-value">
                              {profileData?.accountHolderName || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">Bank Name</span>
                            <span className="field-value">
                              {profileData?.bankName || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="compact-card-field">
                            <span className="field-label">IFSC Code</span>
                            <span className="field-value">
                              {profileData?.ifscCode || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-12">
                          <div className="compact-card-field account-number-field">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="field-label">
                                Account Number
                              </span>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-success p-1"
                                onClick={() =>
                                  toggleNumberVisibility("account")
                                }
                                style={{ fontSize: "0.7rem", lineHeight: 1 }}
                              >
                                {showNumbers.account ? (
                                  <FaEyeSlash size={10} />
                                ) : (
                                  <FaEye size={10} />
                                )}
                              </button>
                            </div>
                            <span className="field-value account-number">
                              {showNumbers.account
                                ? profileData?.accountNumber || "**********"
                                : "••••••••••"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Industry Details Card - 2x6 Layout */}
      {isShipperCompany() && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card border-0 shadow-sm compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <FaIndustry className="text-primary me-2" size={16} />
                  <h6 className="mb-0 fw-semibold">Industry Details</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Industry Type
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.industryType || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Business Category
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.businessCategory || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Documents Section */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card border-0 shadow-sm compact-details-card">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <FaFileAlt className="text-primary me-2" size={16} />
                <h6 className="mb-0 fw-semibold">Documents</h6>
                {userType !== 3 && ( // Don't show badge for Broker (userType 3)
                  <span className="badge bg-info text-dark ms-auto">
                    {USER_TYPE_LABELS[userType] || "User"} Documents
                  </span>
                )}
              </div>
            </div>
            <div className="card-body p-4">
              {isLoading ? (
                <div className="text-center py-4">
                  <FaSpinner className="fa-spin text-primary mb-2" size={24} />
                  <p className="text-muted mb-0">Loading documents...</p>
                </div>
              ) : error ? (
                <div className="text-center py-4">
                  <FaExclamationTriangle
                    className="text-warning mb-2"
                    size={24}
                  />
                  <p className="text-muted mb-0">Failed to load documents</p>
                </div>
              ) : (
                <div className="documents-list">
                  {userDocuments.length > 0 ? (
                    userDocuments.map((uploadedDoc) => {
                      // Get the document name from the required documents list
                      const requiredDocInfo = getRequiredDocumentsByUserType(
                        userType
                      ).find(
                        (reqDoc) => reqDoc.key === uploadedDoc.documentType
                      );

                      // If document type is not in required list, still show it with generic name
                      const documentName =
                        requiredDocInfo?.name ||
                        uploadedDoc.documentType
                          .replace(/([A-Z])/g, " $1")
                          .trim();

                      return (
                        <div
                          key={uploadedDoc.documentId}
                          className="document-row"
                        >
                          {/* Document Icon */}
                          <div className="document-icon">
                            {(() => {
                              const { icon: FileIcon, color } =
                                getFileIconAndColor(uploadedDoc.fileName);
                              return (
                                <FileIcon
                                  size={20}
                                  style={{ color }}
                                  title={uploadedDoc.fileName}
                                />
                              );
                            })()}
                          </div>

                          {/* Document Name */}
                          <div className="document-name">
                            <h6>{documentName}</h6>
                          </div>

                          {/* Document Status */}
                          <div className="document-status">
                            <span className="status-badge uploaded">
                              <FaCheckCircle className="me-1" size={10} />
                              Uploaded Successfully
                            </span>
                          </div>

                          {/* Document Actions */}
                          <div className="document-actions">
                            <button
                              type="button"
                              className="btn"
                              onClick={() =>
                                handleDocumentDownload(uploadedDoc)
                              }
                              disabled={
                                downloadLoading[
                                  `${uploadedDoc.documentType}-${uploadedDoc.fileName}`
                                ]
                              }
                            >
                              {downloadLoading[
                                `${uploadedDoc.documentType}-${uploadedDoc.fileName}`
                              ] ? (
                                <>
                                  <FaSpinner
                                    className="fa-spin me-1"
                                    size={10}
                                  />
                                  Downloading...
                                </>
                              ) : (
                                <>
                                  <FaDownload className="me-1" size={10} />
                                  Download
                                </>
                              )}
                            </button>
                          </div>
                        </div>
                      );
                    })
                  ) : (
                    <div className="text-center py-4">
                      <FaFileAlt className="text-muted mb-2" size={24} />
                      <p className="text-muted mb-0">
                        No documents uploaded yet
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Account Information Card - 2x6 Layout */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card border-0 shadow-sm compact-details-card">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <FaChartLine className="text-primary me-2" size={16} />
                <h6 className="mb-0 fw-semibold">Account Information</h6>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-3">
                <div className="col-lg-3 col-md-6 col-sm-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">
                      Account Created
                    </label>
                    <div className="compact-field-value">
                      <span>{formatDate(profileData?.createdAt)}</span>
                    </div>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6 col-sm-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Last Updated</label>
                    <div className="compact-field-value">
                      <span>{formatDate(profileData?.updatedAt)}</span>
                    </div>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6 col-sm-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Last Login</label>
                    <div className="compact-field-value">
                      <span>{formatDate(profileData?.lastLoginAt)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSettings;
