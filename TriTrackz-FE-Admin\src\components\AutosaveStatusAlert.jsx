import React from 'react';
import { Alert } from 'reactstrap';
import { FaCheckCircle, FaExclamationTriangle, FaSpinner } from 'react-icons/fa';

const AutosaveStatusAlert = ({ 
  isAutoSaving, 
  lastSaveTime, 
  saveError, 
  className = '' 
}) => {
  // Don't show anything if no save activity
  if (!isAutoSaving && !lastSaveTime && !saveError) {
    return null;
  }

  // Show saving status
  if (isAutoSaving) {
    return (
      <Alert color="info" className={`d-flex align-items-center ${className}`}>
        <FaSpinner className="fa-spin me-2" />
        <span>Saving draft...</span>
      </Alert>
    );
  }

  // Show error status
  if (saveError) {
    return (
      <Alert color="warning" className={`d-flex align-items-center ${className}`}>
        <FaExclamationTriangle className="me-2" />
        <span>Auto-save failed. Your changes are saved locally.</span>
      </Alert>
    );
  }

  // Show success status
  if (lastSaveTime) {
    const timeAgo = getTimeAgo(lastSaveTime);
    return (
      <Alert color="success" className={`d-flex align-items-center ${className}`}>
        <FaCheckCircle className="me-2" />
        <span>Draft saved {timeAgo}</span>
      </Alert>
    );
  }

  return null;
};

// Helper function to format time ago
const getTimeAgo = (timestamp) => {
  const now = new Date();
  const saveTime = new Date(timestamp);
  const diffInSeconds = Math.floor((now - saveTime) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
};

export default AutosaveStatusAlert;
