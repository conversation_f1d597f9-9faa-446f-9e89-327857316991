{"name": "react-pro-sidebar", "version": "1.1.0", "description": "high level and customizable side navigation", "main": "dist/index.js", "module": "dist/index.es.js", "files": ["dist"], "repository": "https://github.com/azouaoui-med/react-pro-sidebar.git", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "private": false, "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "yarn clean && rollup -c", "start": "rollup -c -w", "storybook": "start-storybook -p 9001", "build:storybook": "build-storybook", "test": "jest", "test:ci": "yarn test --coverage --watchAll=false --runInBand --forceExit", "test:watch": "jest --watch", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "yarn lint --fix", "format": "prettier --write .", "prepare": "husky install", "gh-pages": "yarn build:storybook && gh-pages -d storybook-static"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/preset-env": "^7.16.4", "@babel/preset-react": "^7.16.0", "@rollup/plugin-commonjs": "^21.1.0", "@rollup/plugin-node-resolve": "^13.3.0", "@storybook/addon-essentials": "^6.4.7", "@storybook/react": "^6.4.7", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.0.3", "@types/react": "^18.0.25", "@types/react-dom": "^18.0.9", "@typescript-eslint/eslint-plugin": "^5.5.0", "@typescript-eslint/parser": "^5.5.0", "babel-jest": "^27.4.2", "eslint": "^8.4.0", "eslint-config-airbnb": "^19.0.2", "eslint-config-airbnb-typescript": "^16.1.0", "eslint-config-prettier": "^8.3.0", "eslint-import-resolver-typescript": "^2.5.0", "eslint-plugin-import": "^2.25.3", "eslint-plugin-jest-dom": "^3.9.2", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-react": "^7.27.1", "eslint-plugin-react-hooks": "^4.3.0", "eslint-plugin-testing-library": "^5.0.1", "gh-pages": "^4.0.0", "husky": "^7.0.0", "jest": "^27.4.3", "lint-staged": ">=10", "prettier": "^2.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^3.0.2", "rollup": "^2.79.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-typescript2": "^0.31.2", "typescript": "^4.5.2"}, "keywords": ["react-component", "react-sidebar", "layout", "sidebar", "menu", "submenu", "component", "collapsed", "rtl"], "dependencies": {"@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@popperjs/core": "^2.11.6", "classnames": "^2.3.2"}}