import { authAPI } from "./axiosInstance";

/**
 * Authentication Service
 * Handles mobile registration, OTP verification, and basic user management
 */

// Mobile Registration Flow
export const authService = {
  /**
   * Send OTP to mobile number
   * @param {string} phoneNumber - Mobile number with country code
   * @param {string} userType - Selected user type (vendor, customer, driver, etc.)
   * @returns {Promise} API response with OTP request ID and user info
   */
  sendOTP: async (phoneNumber, userType = null) => {
    try {
      const response = await authAPI.post("/auth/send-otp", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""), // Remove spaces
        userType,
      });
      return {
        success: true,
        data: response.data,
        message: "O<PERSON> sent successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to send OTP",
        error: error.response?.data,
      };
    }
  },

  /**
   * Verify OTP
   * @param {string} phoneNumber - Mobile number
   * @param {string} otp - OTP code
   * @param {string} requestId - OTP request ID
   * @param {string} userType - Selected user type
   * @returns {Promise} API response with verification status
   */
  verifyOTP: async (phoneNumber, otp, requestId, userType = null) => {
    try {
      const response = await authAPI.post("/auth/verify-otp", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""),
        otp,
        requestId,
        userType,
      });
      return {
        success: true,
        data: response.data,
        message: "OTP verified successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Invalid OTP",
        error: error.response?.data,
      };
    }
  },

  /**
   * Complete basic information registration
   * @param {Object} basicInfo - User basic information
   * @returns {Promise} API response with user data and token
   */
  completeBasicInfo: async (basicInfo) => {
    try {
      const response = await authAPI.post("/auth/complete-basic-info", {
        firstName: basicInfo.firstName,
        lastName: basicInfo.lastName,
        email: basicInfo.email,
        phoneNumber: basicInfo.phoneNumber,
      });
      return {
        success: true,
        data: response.data,
        message: "Registration completed successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Registration failed",
        error: error.response?.data,
      };
    }
  },

  /**
   * Check phone number registration status and existing user types
   * @param {string} phoneNumber - Mobile number
   * @param {string} requestedUserType - Requested user type
   * @returns {Promise} API response with registration status and user types
   */
  checkPhoneRegistration: async (phoneNumber, requestedUserType) => {
    try {
      const response = await authAPI.post("/auth/check-phone-registration", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""),
        requestedUserType,
      });
      return {
        success: true,
        data: response.data,
        // data structure: { isRegistered, existingUserTypes, canRegisterNewType, conflictInfo }
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to check phone registration",
        error: error.response?.data,
      };
    }
  },

  /**
   * Resend OTP
   * @param {string} phoneNumber - Mobile number
   * @returns {Promise} API response with new OTP request ID
   */
  resendOTP: async (phoneNumber) => {
    try {
      const response = await authAPI.post("/auth/resend-otp", {
        phoneNumber: phoneNumber.replace(/\s+/g, ""),
      });
      return {
        success: true,
        data: response.data,
        message: "OTP resent successfully",
      };
    } catch (error) {
      return {
        success: false,
        message: error.response?.data?.message || "Failed to resend OTP",
        error: error.response?.data,
      };
    }
  },
};






