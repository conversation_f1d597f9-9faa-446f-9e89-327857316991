import toast from 'react-hot-toast';

/**
 * Toast utility functions using react-hot-toast
 * Provides a consistent interface for showing toast notifications
 */

export const showToast = {
  /**
   * Show success toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  success: (message, options = {}) => {
    return toast.success(message, {
      duration: 4000,
      position: 'top-center',
      style: {
        background: '#10b981',
        color: 'white',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '16px',
      },
      ...options
    });
  },

  /**
   * Show error toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  error: (message, options = {}) => {
    return toast.error(message, {
      duration: 5000,
      position: 'top-center',
      style: {
        background: '#dc3545',
        color: 'white',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '16px',
      },
      ...options
    });
  },

  /**
   * Show warning toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  warning: (message, options = {}) => {
    return toast(message, {
      duration: 4000,
      position: 'top-center',
      icon: '⚠️',
      style: {
        background: '#f59e0b',
        color: 'white',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '16px',
      },
      ...options
    });
  },

  /**
   * Show info toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  info: (message, options = {}) => {
    return toast(message, {
      duration: 4000,
      position: 'top-center',
      icon: 'ℹ️',
      style: {
        background: '#3b82f6',
        color: 'white',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '16px',
      },
      ...options
    });
  },

  /**
   * Show loading toast
   * @param {string} message - The message to display
   * @param {object} options - Additional options for the toast
   */
  loading: (message, options = {}) => {
    return toast.loading(message, {
      position: 'top-center',
      style: {
        background: '#6b7280',
        color: 'white',
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '16px',
      },
      ...options
    });
  },

  /**
   * Dismiss a specific toast
   * @param {string} toastId - The ID of the toast to dismiss
   */
  dismiss: (toastId) => {
    return toast.dismiss(toastId);
  },

  /**
   * Dismiss all toasts
   */
  dismissAll: () => {
    return toast.dismiss();
  },

  /**
   * Show a promise toast that updates based on promise state
   * @param {Promise} promise - The promise to track
   * @param {object} messages - Object with loading, success, and error messages
   * @param {object} options - Additional options for the toast
   */
  promise: (promise, messages, options = {}) => {
    return toast.promise(promise, {
      loading: messages.loading || 'Loading...',
      success: messages.success || 'Success!',
      error: messages.error || 'Something went wrong!',
    }, {
      position: 'top-center',
      style: {
        borderRadius: '8px',
        fontSize: '14px',
        fontWeight: '500',
        padding: '16px',
      },
      success: {
        style: {
          background: '#10b981',
          color: 'white',
        },
      },
      error: {
        style: {
          background: '#dc3545',
          color: 'white',
        },
      },
      loading: {
        style: {
          background: '#6b7280',
          color: 'white',
        },
      },
      ...options
    });
  }
};

// Export individual functions for convenience
export const { success, error, warning, info, loading, dismiss, dismissAll, promise } = showToast;

// Default export
export default showToast;
