import { USER_TYPES, USER_TYPE_LABELS } from '@constants/enum';

/**
 * Get user type from localStorage or Redux store
 * @param {Object} user - User object from Redux store
 * @returns {number|null} - User type ID or null if not found
 */
export const getUserType = (user) => {
  if (user?.userType) {
    return user.userType;
  }
  
  // Fallback to localStorage if user object doesn't have userType
  try {
    const storedUser = localStorage.getItem('persist:tritrackz-admin-user');
    if (storedUser) {
      const parsedData = JSON.parse(storedUser);
      const userData = JSON.parse(parsedData.user || '{}');
      return userData.user?.userType || null;
    }
  } catch (error) {
    // Silent error handling for localStorage access
  }

  return null;
};

/**
 * Get user type label
 * @param {number} userType - User type ID
 * @returns {string} - User type label
 */
export const getUserTypeLabel = (userType) => {
  // Handle legacy User Type 6 (should be cleaned up in database)
  if (userType === 6) {
    return 'Shipper Company';
  }
  return USER_TYPE_LABELS[userType] || 'Unknown';
};

/**
 * Check if user type requires company details
 * @param {number} userType - User type ID
 * @returns {boolean} - True if company details are required
 */
export const requiresCompanyDetails = (userType) => {
  // Include legacy User Type 6 (should be cleaned up in database)
  return [USER_TYPES.TRANSPORT_COMPANY, USER_TYPES.CARRIER, USER_TYPES.SHIPPER_COMPANY, 6].includes(userType);
};

/**
 * Check if user type requires industry details
 * @param {number} userType - User type ID
 * @returns {boolean} - True if industry details are required
 */
export const requiresIndustryDetails = (userType) => {
  // Include legacy User Type 6 (should be cleaned up in database)
  return userType === USER_TYPES.SHIPPER_COMPANY || userType === 6;
};

/**
 * Get required form sections for a user type
 * @param {number} userType - User type ID
 * @returns {Array} - Array of required form section names
 */
export const getRequiredFormSections = (userType) => {
  const baseSections = ['basicDetails', 'businessAddress', 'panCardDetails', 'aadhaarCardDetails', 'bankDetails'];
  
  if (requiresCompanyDetails(userType)) {
    baseSections.push('companyDetails', 'gstDetails', 'cinNumber');
  }
  
  if (requiresIndustryDetails(userType)) {
    baseSections.push('industryDetails');
  }
  
  return baseSections;
};

/**
 * Validate user type
 * @param {number} userType - User type ID
 * @returns {boolean} - True if valid user type
 */
export const isValidUserType = (userType) => {
  // Include legacy User Type 6 (should be cleaned up in database)
  return Object.values(USER_TYPES).includes(userType) || userType === 6;
};
