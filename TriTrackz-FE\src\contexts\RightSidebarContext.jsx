import React, { createContext, useContext, useState } from 'react';

const RightSidebarContext = createContext();

export const useRightSidebar = () => {
  const context = useContext(RightSidebarContext);
  if (!context) {
    throw new Error('useRightSidebar must be used within a RightSidebarProvider');
  }
  return context;
};

export const RightSidebarProvider = ({ children }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [ContentComponent, setContentComponent] = useState(null);
  const [title, setTitle] = useState('');
  const [icon, setIcon] = useState(null);
  const [width, setWidth] = useState('500px');

  const openSidebar = ({ ContentComponent, title, icon, width = '500px' }) => {
    setContentComponent(() => ContentComponent);
    setTitle(title);
    setIcon(icon);
    setWidth(width);
    setIsOpen(true);
  };

  const closeSidebar = () => {
    setIsOpen(false);
    // Clear content after simple animation (0.3s)
    setTimeout(() => {
      setContentComponent(null);
      setTitle('');
      setIcon(null);
      setWidth('500px');
    }, 300);
  };

  const value = {
    isOpen,
    ContentComponent,
    title,
    icon,
    width,
    openSidebar,
    closeSidebar
  };

  return (
    <RightSidebarContext.Provider value={value}>
      {children}
    </RightSidebarContext.Provider>
  );
};

export default RightSidebarContext;
