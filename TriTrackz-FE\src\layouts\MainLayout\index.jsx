import React, { useState, useEffect } from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { resetUser, setProfileData } from "@store/userSlice";
import { useTheme } from "@contexts/ThemeContext";
import { useGetProfile } from "@api/profileHooks";
import { shouldShowKYCPopup } from "@utils/profileUtils";
import { triggerAutosaveBeforeLogout } from "@utils/autosaveManager";
import { isKYCFormActive, clearKYCFormSession } from "@utils/kycSessionUtils";
import toast from "react-hot-toast";
import { FaPlus, FaBoxOpen, FaClipboardList, FaTruck, FaUser, FaDollarSign, FaChartBar } from "react-icons/fa";

// Import new components
import NewSidebar from "./Components/NewSidebar/index.jsx";
import NewTopbar from "./Components/NewTopbar/index.jsx";
import ModernActionPopup from "@components/ModernActionPopup";
import RightSidebar from "@components/RightSidebar";
import { RightSidebarProvider } from "@contexts/RightSidebarContext";

import ROUTES from "@constants/routes";


const MainLayout = () => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileSidebarOpen, setMobileSidebarOpen] = useState(false);
  const [showKYCPopup, setShowKYCPopup] = useState(false);
  const [isUserInKYCForm, setIsUserInKYCForm] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const { resetToDefaultTheme } = useTheme();

  // Get user data from Redux
  const { user, token } = useSelector((state) => state.user);
  const { profileData } = useSelector((state) => state.user);

  // Fetch profile data using the hook
  const {
    data: profileResponse,
    isLoading: profileLoading,
    error: profileError,
    isSuccess: profileSuccess
  } = useGetProfile(user?.id, {
    enabled: !!user?.id && !!token, // Only fetch if user ID and token exist
    onSuccess: (response) => {
      if (response?.data) {
        dispatch(setProfileData(response.data));
      }
    },
    onError: (error) => {
      toast.error('Failed to load profile data');
    }
  });

  // Effect to handle profile data updates
  useEffect(() => {
    if (profileSuccess && profileResponse?.data) {
      dispatch(setProfileData(profileResponse.data));
    }
  }, [profileSuccess, profileResponse, dispatch]);

  // Track when user is in KYC form based on current route and active session
  useEffect(() => {
    const checkKYCFormStatus = () => {
      const kycFormRoutes = ['/profile-completion', '/kyc-completion'];
      const isInKYCForm = kycFormRoutes.some(route => location.pathname.includes(route));

      // Check if user has an active KYC form session using utility function
      const hasActiveKYCSession = isKYCFormActive();

      setIsUserInKYCForm(isInKYCForm || hasActiveKYCSession);
    };

    // Initial check
    checkKYCFormStatus();

    // Set up periodic check to ensure we catch session changes
    const checkInterval = setInterval(checkKYCFormStatus, 5000); // Check every 5 seconds

    return () => clearInterval(checkInterval);
  }, [location.pathname]);

  // Effect to show KYC popup when profile status is incomplete
  useEffect(() => {
    if (profileData && shouldShowKYCPopup(profileData) && !isUserInKYCForm) {
      // Show popup after a short delay to ensure UI is ready
      const timer = setTimeout(() => {
        setShowKYCPopup(true);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [profileData, isUserInKYCForm]);

  // Handle profile loading error
  useEffect(() => {
    if (profileError) {
      // Don't show error toast for 401/403 as these are handled by axios interceptors
      if (profileError?.response?.status !== 401 && profileError?.response?.status !== 403) {
        toast.error('Failed to load profile data. Some features may not work correctly.');
      }
    }
  }, [profileError]);

  // Function to get page configuration based on current route
  const getPageConfig = () => {
    const path = location.pathname;

    const pageConfigs = {
      '/': {
        title: 'Dashboard',
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
        addButtonIcon: <FaPlus size={12} />
      },
      '/dashboard': {
        title: 'Dashboard',
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false,
        addButtonIcon: <FaPlus size={12} />
      },
      '/shipments': {
        title: 'Shipments Management',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: false,
        addButtonText: 'Add Shipment',
        addButtonIcon: <FaBoxOpen size={12} />
      },
      '/orders': {
        title: 'Orders',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: 'New Order',
        addButtonIcon: <FaClipboardList size={12} />
      },
      '/fleet': {
        title: 'Fleet Management',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: false,
        addButtonText: 'Add Vehicle',
        addButtonIcon: <FaTruck size={12} />
      },
      '/drivers': {
        title: 'Drivers',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: 'Add Driver',
        addButtonIcon: <FaUser size={12} />
      },
      '/inventory': {
        title: 'Inventory',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: 'Add Item',
        addButtonIcon: <FaBoxOpen size={12} />
      },
      '/analytics': {
        title: 'Report & Analytics',
        showSearch: false,
        showAddButton: false,
        showFilters: true,
        showExport: true
      },
      '/profile/settings': {
        title: 'Profile Settings',
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false
      },
      '/billing': {
        title: 'Billing & Payments',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: 'New Invoice',
        addButtonIcon: <FaDollarSign size={12} />
      },
      '/help': {
        title: 'Help',
        showSearch: true,
        showAddButton: false,
        showFilters: false,
        showExport: false
      },
      '/settings': {
        title: 'Settings',
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false
      },
      '/rate-calculator': {
        title: 'Rate Calculator',
        showSearch: false,
        showAddButton: false,
        showFilters: false,
        showExport: false
      },
      '/reports': {
        title: 'Reports',
        showSearch: true,
        showAddButton: true,
        showFilters: true,
        showExport: true,
        addButtonText: 'Generate Report',
        addButtonIcon: <FaChartBar size={12} />
      }
    };

    return pageConfigs[path] || pageConfigs['/'];
  };

  const handleLogout = async () => {
    try {
      // Trigger autosave before logout
      await triggerAutosaveBeforeLogout();
    } catch (error) {

    }

    dispatch(resetUser());
    resetToDefaultTheme(); // Reset theme to dark on logout
    navigate(ROUTES.LOGIN);
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const toggleMobileSidebar = () => {
    setMobileSidebarOpen(!mobileSidebarOpen);
  };

  const handleSearch = (searchTerm) => {
    // Implement search functionality here
  };

  const handleAddClick = () => {
    // Implement add functionality based on current page
  };

  const handleFilterClick = () => {
    // Implement filter functionality
  };

  const handleExportClick = () => {
    // Implement export functionality
  };

  return (
    <RightSidebarProvider>
      <div className="d-flex vh-100 w-100 position-relative new-main-layout">
        {/* New Sidebar */}
        <NewSidebar
          collapsed={sidebarCollapsed}
          mobileOpen={mobileSidebarOpen}
          onMobileClose={() => setMobileSidebarOpen(false)}
        />

        {/* Main Content Area */}
        <div className="flex-fill d-flex flex-column position-relative vh-100 new-main-content">
          {/* New Top Bar */}
          <NewTopbar
            {...getPageConfig()}
            onToggleSidebar={toggleSidebar}
            onToggleMobileSidebar={toggleMobileSidebar}
            onSearch={handleSearch}
            onAddClick={handleAddClick}
            onFilterClick={handleFilterClick}
            onExportClick={handleExportClick}
            onLogout={handleLogout}
          />

          {/* Page Content with Container */}
          <main className="flex-fill overflow-auto new-page-content">
            <div className="h-100 overflow-auto content-container">
              {/* Show loading indicator if profile is being fetched for the first time */}
              {profileLoading && !profileData && (
                <div className="d-flex justify-content-center align-items-center h-100">
                  <div className="text-center">
                    <div className="spinner-border text-primary" role="status">
                      <span className="visually-hidden">Loading profile...</span>
                    </div>
                    <p className="mt-2 text-muted">Loading profile data...</p>
                  </div>
                </div>
              )}
              <Outlet />
            </div>
          </main>
        </div>

        {/* Right Sidebar */}
        <RightSidebar />

        {/* KYC Popup */}
        <ModernActionPopup
          isOpen={showKYCPopup}
          onClose={() => {
            setShowKYCPopup(false);
            // Clear the KYC form session when popup is closed
            clearKYCFormSession();
          }}
        />
      </div>
    </RightSidebarProvider>
  );
};

export default MainLayout;
