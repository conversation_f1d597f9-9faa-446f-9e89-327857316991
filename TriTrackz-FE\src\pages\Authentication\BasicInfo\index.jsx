import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useNavigate, useLocation } from "react-router-dom";
import {
  setUser,
  setBasicInfoCompleted,
  setProfileData,
  setHasExistingCredentials
} from "@store/userSlice";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import { useFormik } from "formik";
import * as Yup from "yup";
import { FaCheck, FaEye, FaEyeSlash } from "react-icons/fa";
import { useMobileVerify } from "@api/authHooks";


const BasicInfo = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  // State for password visibility
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const { phoneNumber, isPhoneVerified, selectedUserType, hasExistingCredentials } = useSelector((state) => state.user.authData);

  // Get prefilled data from navigation state
  const prefillData = location.state?.prefillData;
  const userData = location.state?.userData;

  // Determine if password fields should be disabled
  // Priority: 1. From navigation state, 2. From Redux store, 3. Default to false
  // If no navigation data is provided, default to false (enable password fields)
  const shouldDisablePasswordFields =
    (prefillData || userData) ?
      (prefillData?.hasExistingCredentials ??
       userData?.hasExistingCredentials ??
       hasExistingCredentials ??
       false) :
      false;

  // Update Redux store on component mount
  useEffect(() => {
    // Update Redux store with the determined value
    if (prefillData?.hasExistingCredentials !== undefined || userData?.hasExistingCredentials !== undefined) {
      dispatch(setHasExistingCredentials(shouldDisablePasswordFields));
    }

    // If no navigation data is provided, ensure password fields are enabled
    if (!prefillData && !userData && hasExistingCredentials) {
      dispatch(setHasExistingCredentials(false));
    }
  }, [prefillData, userData, hasExistingCredentials, shouldDisablePasswordFields, dispatch]);



  // Password strength helper
  const getPasswordStrength = (password) => {
    if (!password) return { score: 0, label: "", color: "" };

    let score = 0;
    const checks = {
      length: password.length >= 8,
      lowercase: /[a-z]/.test(password),
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
      special: /[@$!%*?&]/.test(password)
    };

    score = Object.values(checks).filter(Boolean).length;

    if (score <= 2) return { score, label: "Weak", color: "var(--danger-500)" };
    if (score <= 3) return { score, label: "Fair", color: "var(--warning-500)" };
    if (score <= 4) return { score, label: "Good", color: "var(--info-500)" };
    return { score, label: "Strong", color: "var(--success-500)" };
  };

  // Mobile verification mutation
  const mobileVerifyMutation = useMobileVerify({
    onSuccess: async (response) => {
      if (response.success) {
        const userInfo = {
          id: prefillData?.userId || userData?.userId,
          firstName: response.firstName,
          lastName: response.lastName,
          email: response.email,
          phoneNumber: response.phoneNumber,
          userType: prefillData?.userType || userData?.userType || selectedUserType
        };

        dispatch(setUser(userInfo));
        dispatch(setBasicInfoCompleted(true));

        // Fetch updated profile data from API after successful completion
        try {
          const profileResponse = await fetch(`${import.meta.env.VITE_APP_BASE_URL}/api/ComprehensiveProfile/${userInfo.id}`, {
            headers: {
              'Authorization': `Bearer ${response.token || localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          });

          if (profileResponse.ok) {
            const profileData = await profileResponse.json();
            console.log('Fetched profile data:', profileData);

            // Set the actual profile data from API
            if (profileData?.data) {
              dispatch(setProfileData(profileData.data));
            }
          } else {
            console.warn('Failed to fetch profile data, using fallback');
            // Fallback: Set profile data with expected structure
            const fallbackProfileData = {
              ...userInfo,
              status: 1,
              isComplete: true,
              kycStatus: 'Completed'
            };
            dispatch(setProfileData(fallbackProfileData));
          }
        } catch (error) {
          console.error('Error fetching profile data:', error);
          // Fallback: Set profile data with expected structure
          const fallbackProfileData = {
            ...userInfo,
            status: 1,
            isComplete: true,
            kycStatus: 'Completed'
          };
          dispatch(setProfileData(fallbackProfileData));
        }

        toast.success("Profile completed successfully!");
        navigate(ROUTES.DASHBOARD);
      } else {
        toast.error(response.message || "Failed to complete profile");
      }
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || "Failed to complete profile");
    }
  });

  // Redirect if phone not verified (unless we have prefilled data)
  useEffect(() => {
    if (!prefillData && !userData && (!phoneNumber || !isPhoneVerified)) {
      toast.error("Please verify your mobile number first");
      navigate(ROUTES.LOGIN);
    }
  }, [phoneNumber, isPhoneVerified, navigate, prefillData, userData]);

  // Dynamic validation schema based on password field state
  const getValidationSchema = () => {
    const baseSchema = {
      firstName: Yup.string()
        .min(2, 'First Name must be between 2 and 50 characters')
        .max(50, 'First Name must be between 2 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/, 'Enter valid First Name')
        .required('First name is required'),
      lastName: Yup.string()
        .min(1, 'Last Name must be between 1 and 50 characters')
        .max(50, 'Last Name must be between 1 and 50 characters')
        .matches(/^[a-zA-Z\s]+$/, 'Enter valid Last Name'),
      email: Yup.string()
        .trim()
        .matches(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, 'Enter valid Email Address'),
      phoneNumber: Yup.string()
        .required('Mobile number is required')
        .test('phone-format', 'Enter valid Mobile Number (10 digits starting with 6-9)', function(value) {
          if (!value) return false;

          // Remove any non-digit characters
          const cleaned = value.replace(/\D/g, '');

          // Check if it's 10 digits (without country code) or 12 digits (with 91 country code)
          if (cleaned.length === 10) {
            return /^[6-9]\d{9}$/.test(cleaned);
          } else if (cleaned.length === 12) {
            return /^91[6-9]\d{9}$/.test(cleaned);
          }

          return false;
        }),
    };

    // Only add password validation if password fields are enabled
    if (!shouldDisablePasswordFields) {
      baseSchema.password = Yup.string()
        .required('Password is required')
        .min(8, 'Password must be between 8 to 15 characters')
        .max(15, 'Password must be between 8 to 15 characters')
        .matches(/[A-Z]/, 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character')
        .matches(/[a-z]/, 'Password must contain at least one uppercase letter, one lowercase letter, one number and one special character')
        .matches(/\d/, 'Password must contain at least one uppercase letter, one lowercase letter, and one special character')
        .matches(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/, 'Password must contain at least one uppercase letter, one lowercase letter, and one special character');

      baseSchema.confirmPassword = Yup.string()
        .required('Confirm Password is required')
        .oneOf([Yup.ref('password')], 'Passwords do not match');
    }

    return Yup.object(baseSchema);
  };

 

  const formik = useFormik({
    initialValues: {
      firstName: prefillData?.firstName || "",
      lastName: prefillData?.lastName || "",
      email: prefillData?.email || "",
      phoneNumber: prefillData?.phoneNumber || userData?.phoneNumber || phoneNumber || "",
      password: "",
      confirmPassword: "",
    },
    validationSchema: getValidationSchema(),
    onSubmit: (values) => {
      const payload = {
        userId: prefillData?.userId || userData?.userId || null,
        userType: prefillData?.userType || userData?.userType || selectedUserType,
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        email: values.email.trim().toLowerCase(),
        phoneNumber: prefillData?.phoneNumber || userData?.phoneNumber || phoneNumber,
      };

      // Only include password fields if they're enabled
      if (!shouldDisablePasswordFields) {
        payload.password = values.password; // Plain text password
        payload.confirmPassword = values.confirmPassword; // Plain text confirm password
      }

      mobileVerifyMutation.mutate(payload);
    },
  });

  // Format phone number for display
  const formatPhoneNumber = (phone) => {
    if (!phone) return "";
    const cleaned = phone.replace(/\D/g, "");
    if (cleaned.length >= 10) {
      const countryCode = cleaned.slice(0, -10);
      const number = cleaned.slice(-10);
      return `+${countryCode} ${number.slice(0, 3)}-${number.slice(3, 6)}-${number.slice(6)}`;
    }
    return phone;
  };

  return (
    <div className="auth-form">
      <div className="auth-welcome-text mb-4">
        <h2 className="fw-bold" style={{ color: "var(--text-primary)" }}>Basic info</h2>
        <p style={{ color: "var(--text-secondary)" }}>Just a few more details to get you started with TriTrackz</p>
      </div>



      <form onSubmit={formik.handleSubmit}>
        {/* Name Fields */}
        <div className="row mb-4">
          <div className="col-md-6">
            <label htmlFor="firstName" className="form-label fw-semibold mb-3" style={{ color: "var(--text-primary)" }}>
              First Name
            </label>
            <input
              type="text"
              className={`form-control profile-input ${
                formik.touched.firstName && formik.errors.firstName ? "is-invalid" : ""
              }`}
              id="firstName"
              name="firstName"
              value={formik.values.firstName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter your first name"
              autoComplete="given-name"
            />
            {formik.touched.firstName && formik.errors.firstName && (
              <div className="profile-error-message mt-2">
                <small style={{ color: "var(--danger-500)" }}>{formik.errors.firstName}</small>
              </div>
            )}
          </div>
          <div className="col-md-6">
            <label htmlFor="lastName" className="form-label fw-semibold mb-3" style={{ color: "var(--text-primary)" }}>
              Last Name <span style={{ color: "var(--text-muted)", fontWeight: "normal" }}>(Optional)</span>
            </label>
            <input
              type="text"
              className={`form-control profile-input ${
                formik.touched.lastName && formik.errors.lastName ? "is-invalid" : ""
              }`}
              id="lastName"
              name="lastName"
              value={formik.values.lastName}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter your last name"
              autoComplete="family-name"
            />
            {formik.touched.lastName && formik.errors.lastName && (
              <div className="profile-error-message mt-2">
                <small style={{ color: "var(--danger-500)" }}>{formik.errors.lastName}</small>
              </div>
            )}
          </div>
        </div>

        {/* Email Field */}
        <div className="mb-4">
          <label htmlFor="email" className="form-label fw-semibold mb-3" style={{ color: "var(--text-primary)" }}>
            Email Address <span style={{ color: "var(--text-muted)", fontWeight: "normal" }}>(Optional)</span>
          </label>
          <input
            type="email"
            className={`form-control profile-input ${
              formik.touched.email && formik.errors.email ? "is-invalid" : ""
            }`}
            id="email"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            placeholder="Enter your email address"
            autoComplete="email"
          />
          {formik.touched.email && formik.errors.email && (
            <div className="profile-error-message mt-2">
              <small style={{ color: "var(--danger-500)" }}>{formik.errors.email}</small>
            </div>
          )}
        </div>

        {/* Password Fields - Only show for new users */}
        {!shouldDisablePasswordFields && (
          <div className="row mb-4">
            <div className="col-md-6">
              <label htmlFor="password" className="form-label fw-semibold mb-3" style={{ color: "var(--text-primary)" }}>
                Password
              </label>
              <div className="position-relative">
                <input
                  type={showPassword ? "text" : "password"}
                  className={`form-control profile-input ${
                    formik.touched.password && formik.errors.password ? "is-invalid" : ""
                  }`}
                  id="password"
                  name="password"
                  value={formik.values.password}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Create a password"
                  autoComplete="new-password"
                  style={{ paddingRight: "45px" }}
                />
                <button
                  type="button"
                  className="btn position-absolute"
                  style={{
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    border: "none",
                    background: "transparent",
                    color: "#6c757d",
                    padding: "0.25rem",
                    width: "32px",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: "4px",
                    transition: "all 0.2s ease",
                    cursor: "pointer"
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = "#495057";
                    e.target.style.backgroundColor = "rgba(108, 117, 125, 0.1)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = "#6c757d";
                    e.target.style.backgroundColor = "transparent";
                  }}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
                </button>
              </div>

              {/* Password Strength Indicator */}
              {formik.values.password && (
                <div className="mt-2">
                  <div className="d-flex justify-content-between align-items-center mb-1">
                    <small style={{ color: "var(--text-secondary)" }}>Password strength:</small>
                    <small style={{ color: getPasswordStrength(formik.values.password).color, fontWeight: "500" }}>
                      {getPasswordStrength(formik.values.password).label}
                    </small>
                  </div>
                  <div className="progress" style={{ height: "4px", backgroundColor: "var(--border-secondary)" }}>
                    <div
                      className="progress-bar"
                      style={{
                        width: `${(getPasswordStrength(formik.values.password).score / 5) * 100}%`,
                        backgroundColor: getPasswordStrength(formik.values.password).color,
                        transition: "all 0.3s ease"
                      }}
                    />
                  </div>
                </div>
              )}

              {formik.touched.password && formik.errors.password && (
                <div className="profile-error-message mt-2">
                  <small style={{ color: "var(--danger-500)" }}>{formik.errors.password}</small>
                </div>
              )}
            </div>
            <div className="col-md-6">
              <label htmlFor="confirmPassword" className="form-label fw-semibold mb-3" style={{ color: "var(--text-primary)" }}>
                Confirm Password
              </label>
              <div className="position-relative">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  className={`form-control profile-input ${
                    formik.touched.confirmPassword && formik.errors.confirmPassword ? "is-invalid" : ""
                  }`}
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formik.values.confirmPassword}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Confirm your password"
                  autoComplete="new-password"
                  style={{ paddingRight: "45px" }}
                />
                <button
                  type="button"
                  className="btn position-absolute"
                  style={{
                    right: "10px",
                    top: "50%",
                    transform: "translateY(-50%)",
                    border: "none",
                    background: "transparent",
                    color: "#6c757d",
                    padding: "0.25rem",
                    width: "32px",
                    height: "32px",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: "4px",
                    transition: "all 0.2s ease",
                    cursor: "pointer"
                  }}
                  onMouseEnter={(e) => {
                    e.target.style.color = "#495057";
                    e.target.style.backgroundColor = "rgba(108, 117, 125, 0.1)";
                  }}
                  onMouseLeave={(e) => {
                    e.target.style.color = "#6c757d";
                    e.target.style.backgroundColor = "transparent";
                  }}
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
                </button>
              </div>
              {formik.touched.confirmPassword && formik.errors.confirmPassword && (
                <div className="profile-error-message mt-2">
                  <small style={{ color: "var(--danger-500)" }}>{formik.errors.confirmPassword}</small>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Show message for existing users */}
        {shouldDisablePasswordFields && (
          <div className="mb-4">
            <div className="alert alert-info d-flex align-items-center" style={{
              backgroundColor: "var(--info-50)",
              borderColor: "var(--info-200)",
              color: "var(--info-700)"
            }}>
              <FaCheck className="me-2" size={16} />
              <span>Using existing credentials - no password required for additional user type registration.</span>
            </div>
          </div>
        )}

        {/* Phone Number (Read-only) */}
        <div className="mb-4">
          <label htmlFor="phoneNumber" className="form-label fw-semibold mb-3" style={{ color: "var(--text-primary)" }}>
            Mobile Number
            <span className="ms-2 badge bg-success-subtle text-success border-0 px-2 py-1" style={{ fontSize: "0.75rem" }}>
              <FaCheck className="me-1" size={10} />
              Verified
            </span>
          </label>
          <input
            type="text"
            className="form-control profile-input profile-input-verified"
            id="phoneNumber"
            name="phoneNumber"
            value={formatPhoneNumber(phoneNumber)}
            readOnly
            disabled
          />
        </div>

        {/* Terms and Privacy Notice */}
        <div className="mb-4 text-center">
          <small style={{ color: "var(--text-secondary)", lineHeight: "1.5" }}>
            By creating an account, you agree to our{" "}
            <a
              href="/terms"
              className="text-decoration-none fw-medium"
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: "var(--primary-500)" }}
            >
              Terms of Service
            </a>{" "}
            and{" "}
            <a
              href="/privacy"
              className="text-decoration-none fw-medium"
              target="_blank"
              rel="noopener noreferrer"
              style={{ color: "var(--primary-500)" }}
            >
              Privacy Policy
            </a>
            .
          </small>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="btn btn-primary w-100 py-3 mb-3 fw-semibold profile-submit-btn"
          disabled={mobileVerifyMutation.isPending || !formik.isValid}
          style={{
            borderRadius: "12px",
            fontSize: "1.1rem",
            background: "var(--triadic-red-50)",
            border: "none",
            color: "var(--triadic-red-900)",
            boxShadow: "0 4px 15px rgba(229, 191, 193, 0.3)"
          }}
        >
          {mobileVerifyMutation.isPending ? (
            <>
              <span
                className="spinner-border spinner-border-sm me-2"
                role="status"
                aria-hidden="true"
              ></span>
              Creating Account...
            </>
          ) : (
            "Complete Registration"
          )}
        </button>    
      </form>
    </div>
  );
};

export default BasicInfo;
