import React from 'react';
import { useSelector } from 'react-redux';
import { FaInfoCircle, FaClock, FaExclamationTriangle, FaCheckCircle } from 'react-icons/fa';

const KYCStatusAlert = ({ className = '', style = {} }) => {
  const { profileData } = useSelector((state) => state.user);

  // Don't show if no profile data or status is 0 (incomplete)
  if (!profileData || profileData.status === 0) {
    return null;
  }

  const getStatusConfig = () => {
    switch (profileData.status) {
      case 1:
        return {
          icon: <FaClock className="me-2" />,
          message: "KYC completed and pending admin approval, you'll be notified once verified.",
          alertClass: "alert-warning",
          bgColor: "#fff3cd",
          borderColor: "#ffeaa7",
          textColor: "#856404"
        };
      case 2:
        return {
          icon: <FaInfoCircle className="me-2" />,
          message: "Your profile is under review by our team",
          alertClass: "alert-info",
          bgColor: "#d1ecf1",
          borderColor: "#74b9ff",
          textColor: "#0c5460"
        };
      case 3:
        return {
          icon: <FaCheckCircle className="me-2" />,
          message: "KYC approved successfully! Your profile is verified and active.",
          alertClass: "alert-success",
          bgColor: "#d4edda",
          borderColor: "#c3e6cb",
          textColor: "#155724"
        };
      case 4:
        return {
          icon: <FaExclamationTriangle className="me-2" />,
          message: "KYC was rejected by the admin. Please update the required details and resubmit.",
          alertClass: "alert-danger",
          bgColor: "#f8d7da",
          borderColor: "#f5c6cb",
          textColor: "#721c24"
        };
      default:
        return null;
    }
  };

  const statusConfig = getStatusConfig();
  if (!statusConfig) return null;

  return (
    <div 
      className={`alert ${statusConfig.alertClass} ${className}`}
      style={{
        backgroundColor: statusConfig.bgColor,
        borderColor: statusConfig.borderColor,
        color: statusConfig.textColor,
        border: '1px solid',
        borderRadius: '8px',
        margin: '0 0 20px 0',
        padding: '12px 16px',
        display: 'flex',
        alignItems: 'center',
        fontSize: '14px',
        fontWeight: '500',
        ...style
      }}
    >
      {statusConfig.icon}
      <span>{statusConfig.message}</span>
    </div>
  );
};

export default KYCStatusAlert;
