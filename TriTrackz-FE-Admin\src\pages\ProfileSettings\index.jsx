import React, { useMemo, useCallback, memo } from "react";
import { useSelector } from "react-redux";

import { useSidebarActions } from "@hooks/useSidebarActions";
import { Avatar } from "@components/Common";
import {
  <PERSON>a<PERSON>ser,
  FaEnvelope,
  FaPhone,
  FaLock,
  FaSpinner,
  FaExclamationTriangle,
} from "react-icons/fa";

// Memoized sub-components to prevent unnecessary re-renders
const LoadingState = memo(() => (
  <div className="d-flex justify-content-center align-items-center min-vh-50">
    <div className="text-center">
      <FaSpinner className="fa-spin text-primary mb-3" size={32} />
      <p className="text-muted">Loading profile...</p>
    </div>
  </div>
));

LoadingState.displayName = "LoadingState";

const ErrorState = memo(({ onRetry }) => (
  <div className="d-flex justify-content-center align-items-center min-vh-50">
    <div className="text-center">
      <FaExclamationTriangle className="text-warning mb-3" size={32} />
      <p className="text-muted">Failed to load profile data</p>
      <button className="btn btn-primary btn-sm" onClick={onRetry}>
        Try Again
      </button>
    </div>
  </div>
));

ErrorState.displayName = "ErrorState";

// Memoized ProfileHeader component
const ProfileHeader = memo(({ onChangePassword }) => (
  <div className="row mb-3">
    <div className="col-12">
      <div className="d-flex justify-content-between align-items-center">
        <div>
          <h5 className="mb-1 fw-semibold">Profile Settings</h5>
          <p className="text-muted mb-0 small">
            Manage your account information and preferences
          </p>
        </div>
        <button className="btn btn-primary btn-sm" onClick={onChangePassword}>
          <FaLock size={14} className="me-2" />
          Change Password
        </button>
      </div>
    </div>
  </div>
));

ProfileHeader.displayName = "ProfileHeader";

// Memoized ProfileCard component
const ProfileCard = memo(({ displayName, userTypeLabel }) => (
  <div className="row mb-3">
    <div className="col-12">
      <div className="card shadow-sm">
        <div className="card-body p-3">
          <div className="row align-items-center">
            <div className="col-auto">
              <Avatar name={displayName} size="large" showBorder={true} />
            </div>
            <div className="col">
              <h5 className="mb-1">{displayName}</h5>
              <p className="text-muted mb-0">{userTypeLabel}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
));

ProfileCard.displayName = "ProfileCard";

// Memoized BasicDetailsCard component using ViewUser compact style
const BasicDetailsCard = memo(({ profileData }) => {
  const firstName =
    profileData?.basicDetails?.firstName || profileData?.firstName;
  const lastName = profileData?.basicDetails?.lastName || profileData?.lastName;
  const email = profileData?.basicDetails?.email || profileData?.email;
  const phone = profileData?.mobileNumber;

  return (
    <div className="row mb-3">
      <div className="col-12">
        <div className="card shadow-sm">
          <div className="card-header bg-transparent border-bottom py-3">
            <div className="d-flex align-items-center">
              <FaUser className="text-primary me-2" size={16} />
              <h6 className="mb-0 fw-semibold">Administrator Details</h6>
            </div>
          </div>
          <div className="card-body p-4">
            <div className="row g-3">
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="mb-3">
                  <label className="form-label text-muted small fw-medium">
                    First Name
                  </label>
                  <div className="d-flex align-items-center">
                    <FaUser className="text-muted me-2" size={14} />
                    <span className="fw-medium">{firstName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="mb-3">
                  <label className="form-label text-muted small fw-medium">
                    Last Name
                  </label>
                  <div className="d-flex align-items-center">
                    <FaUser className="text-muted me-2" size={14} />
                    <span className="fw-medium">{lastName || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="mb-3">
                  <label className="form-label text-muted small fw-medium">
                    Email
                  </label>
                  <div className="d-flex align-items-center">
                    <FaEnvelope className="text-muted me-2" size={14} />
                    <span className="fw-medium">{email || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="mb-3">
                  <label className="form-label text-muted small fw-medium">
                    Phone Number
                  </label>
                  <div className="d-flex align-items-center">
                    <FaPhone className="text-muted me-2" size={14} />
                    <span className="fw-medium">{phone || "N/A"}</span>
                  </div>
                </div>
              </div>
              <div className="col-lg-3 col-md-6 col-sm-6">
                <div className="mb-3">
                  <label className="form-label text-muted small fw-medium">
                    Role
                  </label>
                  <div>
                    <span className="fw-medium">Administrator</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

BasicDetailsCard.displayName = "BasicDetailsCard";

const ProfileSettings = () => {
  const { user } = useSelector((state) => state.user);
  const { openChangePassword } = useSidebarActions();

  // For admin portal, use user data directly
  const profileData = user;
  const isLoading = false;
  const error = null;

  // Memoize display name to prevent recalculation on every render
  const displayName = useMemo(() => {
    return (
      profileData?.displayName ||
      `${profileData?.firstName} ${profileData?.lastName}`
    );
  }, [profileData?.displayName, profileData?.firstName, profileData?.lastName]);

  // Memoize user type label to prevent recalculation
  const userTypeLabel = useMemo(() => {
    return "Administrator";
  }, []);

  // Memoize retry handler
  const handleRetry = useCallback(() => {
    // Add retry logic here if needed
    console.log("Retry clicked");
  }, []);

  if (isLoading) {
    return <LoadingState />;
  }

  if (error) {
    return <ErrorState onRetry={handleRetry} />;
  }

  return (
    <div className="container-fluid py-3 px-4">
      {/* Header */}
      <ProfileHeader onChangePassword={openChangePassword} />
      {/* Profile Header Card */}
      <ProfileCard displayName={displayName} userTypeLabel={userTypeLabel} />
      {/* Administrator Details Card */}
      <BasicDetailsCard profileData={profileData} />
    </div>
  );
};

export default ProfileSettings;
