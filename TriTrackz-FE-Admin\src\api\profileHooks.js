import { useQuery, useMutation } from '@tanstack/react-query';
import { API_ENDPOINTS } from '@constants/apiNamevariables';
import { baseAPI } from './axiosInstance';

export const useGetProfile = (userId, options = {}) =>
  useQuery({
    queryKey: ['profile', userId],
    queryFn: () => baseAPI.get(`${API_ENDPOINTS.GET_PROFILE}/${userId}`),
    enabled: !!userId, 
    staleTime: 5 * 60 * 1000, 
    cacheTime: 10 * 60 * 1000, 
    ...options,
  });

// Update user profile data
export const useUpdateProfile = (options) =>
  useMutation({
    mutationFn: ({ userId, profileData }) =>
      baseAPI.put(`${API_ENDPOINTS.COMPREHENSIVE_PROFILE}/${userId}`, profileData),
    ...options,
  });

// Autosave comprehensive profile
export const useAutosaveComprehensiveProfile = (options) =>
  useMutation({
    mutationFn: ({ userId, profileData, isDraft = true, preserveStatus = null }) => {
      // Determine the status based on parameters
      let status;
      if (preserveStatus !== null) {
        status = preserveStatus;
      } else if (isDraft) {
        status = 0; // Draft status
      } else {
        status = profileData.status || 1; // Use existing status or default to 1
      }

      const payload = {
        ...profileData,
        status: status
      };

      return baseAPI.put(`${API_ENDPOINTS.COMPREHENSIVE_PROFILE}/${userId}`, payload);
    },
    ...options,
  });
