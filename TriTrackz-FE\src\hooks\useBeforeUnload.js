import { useEffect, useRef } from 'react';

/**
 * Custom hook for handling beforeunload events
 * @param {Function} onBeforeUnload - Callback function to execute before unload
 * @param {boolean} enabled - Whether the hook is enabled (default: true)
 * @param {boolean} showWarning - Whether to show browser warning dialog (default: false)
 * @param {string} warningMessage - Custom warning message for the dialog
 */
export const useBeforeUnload = (
  onBeforeUnload,
  enabled = true,
  showWarning = false,
  warningMessage = 'You have unsaved changes. Are you sure you want to leave?'
) => {
  const callbackRef = useRef(onBeforeUnload);
  
  // Update callback ref when it changes
  useEffect(() => {
    callbackRef.current = onBeforeUnload;
  }, [onBeforeUnload]);

  useEffect(() => {
    if (!enabled) {
      return;
    }

    const handleBeforeUnload = (event) => {
      // Execute the callback function
      if (callbackRef.current) {
        try {
          callbackRef.current();
        } catch (error) {

        }
      }

      // Show warning dialog if enabled
      if (showWarning) {
        event.preventDefault();
        event.returnValue = warningMessage;
        return warningMessage;
      }
    };

    const handleVisibilityChange = () => {
      // Handle page visibility change (e.g., tab switching, minimizing)
      if (document.visibilityState === 'hidden' && callbackRef.current) {
        try {
          // Add small delay to ensure it's not just a quick tab switch
          setTimeout(() => {
            if (document.visibilityState === 'hidden') {
              callbackRef.current();
            }
          }, 100);
        } catch (error) {
          console.error('Error in visibility change callback:', error);
        }
      }
    };

    const handlePageHide = () => {
      // Handle page hide event (more reliable than beforeunload in some cases)
      if (callbackRef.current) {
        try {
          callbackRef.current();
        } catch (error) {
          console.error('Error in pagehide callback:', error);
        }
      }
    };

    // Add event listeners
    window.addEventListener('beforeunload', handleBeforeUnload);
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('pagehide', handlePageHide);

    // Cleanup function
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('pagehide', handlePageHide);
    };
  }, [enabled, showWarning, warningMessage]);
};

/**
 * Hook specifically for autosave on page unload
 * @param {Function} saveFunction - Function to call for saving data
 * @param {boolean} hasUnsavedChanges - Whether there are unsaved changes
 * @param {boolean} enabled - Whether the hook is enabled
 */
export const useAutosaveOnUnload = (saveFunction, hasUnsavedChanges = false, enabled = true) => {
  useBeforeUnload(
    saveFunction,
    enabled && hasUnsavedChanges,
    false // Don't show warning for autosave
  );
};
