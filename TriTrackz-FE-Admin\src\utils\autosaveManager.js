/**
 * Autosave manager utility for handling form data persistence
 */
import { saveDraftToStorage } from "./draftUtils";

class AutosaveManager {
  constructor() {
    this.isEnabled = true;
    this.saveQueue = new Map();
    this.isProcessing = false;
    this.registeredCallbacks = new Map(); // Store registered save callbacks
  }

  /**
   * Enable or disable autosave
   * @param {boolean} enabled
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
  }

  /**
   * Register a save callback for a specific key
   * @param {string} key - Unique identifier for the callback
   * @param {Function} callback - Function to call for saving
   */
  register(key, callback) {
    if (!key || typeof callback !== "function") {
      console.warn(
        "Invalid key or callback provided to autosaveManager.register"
      );
      return;
    }

    this.registeredCallbacks.set(key, callback);
    console.log(`Registered autosave callback for key: ${key}`);
  }

  /**
   * Unregister a save callback
   * @param {string} key - Unique identifier for the callback to remove
   */
  unregister(key) {
    if (!key) {
      console.warn("Invalid key provided to autosaveManager.unregister");
      return;
    }

    const removed = this.registeredCallbacks.delete(key);
    if (removed) {
      console.log(`Unregistered autosave callback for key: ${key}`);
    }
  }

  /**
   * Add a save operation to the queue
   * @param {string} userId
   * @param {Object} formData
   * @param {number} currentStep
   * @param {Array} completedSteps
   */
  queueSave(userId, formData, currentStep, completedSteps) {
    if (!this.isEnabled || !userId) return;

    this.saveQueue.set(userId, {
      formData,
      currentStep,
      completedSteps,
      timestamp: Date.now(),
    });

    this.processSaveQueue();
  }

  /**
   * Process the save queue
   */
  async processSaveQueue() {
    if (this.isProcessing || this.saveQueue.size === 0) return;

    this.isProcessing = true;

    try {
      for (const [userId, saveData] of this.saveQueue.entries()) {
        await this.performSave(userId, saveData);
        this.saveQueue.delete(userId);
      }
    } catch (error) {
      console.error("Error processing save queue:", error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Perform the actual save operation
   * @param {string} userId
   * @param {Object} saveData
   */
  async performSave(userId, saveData) {
    try {
      const success = saveDraftToStorage(
        userId,
        saveData.formData,
        saveData.currentStep,
        saveData.completedSteps
      );

      if (success) {
        console.log(`Autosave completed for user ${userId}`);
      } else {
        console.warn(`Autosave failed for user ${userId}`);
      }

      return success;
    } catch (error) {
      console.error(`Autosave error for user ${userId}:`, error);
      return false;
    }
  }

  /**
   * Force save all queued items immediately
   */
  async forceSaveAll() {
    // Call all registered callbacks first
    for (const [key, callback] of this.registeredCallbacks.entries()) {
      try {
        await callback();
        console.log(`Called registered callback for key: ${key}`);
      } catch (error) {
        console.error(
          `Error calling registered callback for key ${key}:`,
          error
        );
      }
    }

    // Then process the save queue
    if (this.saveQueue.size === 0) return;

    const promises = Array.from(this.saveQueue.entries()).map(
      ([userId, saveData]) => this.performSave(userId, saveData)
    );

    try {
      await Promise.all(promises);
      this.saveQueue.clear();
    } catch (error) {
      console.error("Error in force save all:", error);
    }
  }

  /**
   * Clear the save queue
   */
  clearQueue() {
    this.saveQueue.clear();
  }

  /**
   * Get queue status
   */
  getQueueStatus() {
    return {
      queueSize: this.saveQueue.size,
      isProcessing: this.isProcessing,
      isEnabled: this.isEnabled,
      registeredCallbacks: this.registeredCallbacks.size,
    };
  }

  /**
   * Clear all registered callbacks
   */
  clearCallbacks() {
    this.registeredCallbacks.clear();
    console.log("Cleared all registered callbacks");
  }
}

// Create singleton instance
const autosaveManager = new AutosaveManager();

// Set up beforeunload handler to save any pending changes
if (typeof window !== "undefined") {
  window.addEventListener("beforeunload", () => {
    autosaveManager.forceSaveAll();
  });
}

export default autosaveManager;
