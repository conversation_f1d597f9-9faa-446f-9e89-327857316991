import { useState, useEffect, useCallback, useRef } from 'react';
import { saveDraftToStorage, hasFormDataChanged } from '@utils/draftUtils';
import { useAutosaveComprehensiveProfile } from '@api/profileHooks';
import { formatFormDataForAPI } from '@utils/profileUtils';

/**
 * Custom hook for auto-saving form data
 * @param {Object} options - Configuration options
 * @param {string} options.userId - User ID
 * @param {Object} options.formData - Current form data
 * @param {number} options.currentStep - Current step index
 * @param {Array} options.completedSteps - Array of completed step indices
 * @param {number} options.debounceMs - Debounce delay in milliseconds
 * @param {boolean} options.enabled - Whether autosave is enabled
 * @param {Object} options.profileData - Existing profile data
 * @param {boolean} options.isEditMode - Whether in edit mode
 * @param {Function} options.onSuccess - Success callback
 * @param {Function} options.onError - Error callback
 * @returns {Object} Autosave state and functions
 */
export const useAutosave = ({
  userId,
  formData,
  currentStep,
  completedSteps,
  debounceMs = 60000, // 1 minute default
  enabled = true,
  profileData = null,
  isEditMode = false,
  onSuccess,
  onError
}) => {
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSavedData, setLastSavedData] = useState(null);
  const [lastSaveTime, setLastSaveTime] = useState(null);
  const [saveError, setSaveError] = useState(null);

  const debounceTimeoutRef = useRef(null);
  const lastDataRef = useRef(null);
  const isMountedRef = useRef(true);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, []);

  // Check if autosave should be enabled
  const shouldEnableAutosave = useCallback(() => {
    if (!enabled || !userId || !formData) {
      return false;
    }

    // Don't autosave for completed profiles in edit mode
    if (isEditMode && profileData?.status >= 1) {
      return false;
    }

    // Check if data has actually changed
    if (lastDataRef.current && !hasFormDataChanged(lastDataRef.current, formData)) {
      return false;
    }

    return true;
  }, [enabled, userId, formData, isEditMode, profileData?.status]);

  // API mutation for autosave
  const { mutate: autosaveMutation } = useAutosaveComprehensiveProfile({
    onSuccess: (response) => {
      if (!isMountedRef.current) return;
      
      setIsAutoSaving(false);
      setLastSavedData(formData);
      setLastSaveTime(new Date());
      setSaveError(null);
      
      // Save to localStorage as backup
      saveDraftToStorage(userId, formData, currentStep, completedSteps);
      
      if (onSuccess) {
        onSuccess(response);
      }
    },
    onError: (error) => {
      if (!isMountedRef.current) return;
      
      setIsAutoSaving(false);
      setSaveError(error);
      
      // Still save to localStorage even if API fails
      saveDraftToStorage(userId, formData, currentStep, completedSteps);
      
      // Silent error handling - don't interrupt user
      console.error('Autosave failed:', error);
      
      if (onError) {
        onError(error);
      }
    }
  });

  // Perform autosave
  const performAutosave = useCallback(() => {
    if (!shouldEnableAutosave()) {
      return;
    }

    // Don't save if already saving
    if (isAutoSaving) {
      return;
    }

    console.log('Performing auto-save for profile (status:', profileData?.status || 'new', ')');
    setIsAutoSaving(true);
    setSaveError(null);

    const formattedData = formatFormDataForAPI(formData);

    // For incomplete profiles (status 0), save as draft
    // For completed profiles, this code path shouldn't execute due to shouldEnableAutosave check
    autosaveMutation({
      userId,
      profileData: formattedData,
      isDraft: true, // This ensures status: 0 for incomplete profiles
      preserveStatus: null // Let the API use draft logic
    });

    lastDataRef.current = formData;
  }, [shouldEnableAutosave, userId, formData, formatFormDataForAPI, autosaveMutation, profileData?.status]);

  // Debounced autosave effect
  useEffect(() => {
    // Check if auto-save should be enabled
    if (!shouldEnableAutosave() || !userId || !formData) {
      return;
    }

    // Clear existing timeout
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    // Set new timeout
    debounceTimeoutRef.current = setTimeout(() => {
      performAutosave();
    }, debounceMs);

    // Cleanup function
    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [formData, currentStep, completedSteps, shouldEnableAutosave, userId, debounceMs, performAutosave]);

  // Manual save function
  const saveNow = useCallback(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }
    performAutosave();
  }, [performAutosave]);

  // Save on page unload
  const saveOnUnload = useCallback(() => {
    if (!shouldEnableAutosave()) {
      return;
    }

    try {
      // Save to localStorage immediately as backup
      saveDraftToStorage(userId, formData, currentStep, completedSteps);

      // Make synchronous API call for immediate save on unload
      const formattedData = formatFormDataForAPI(formData);

      // Determine status based on profile completion
      // - For incomplete profiles (status 0): save as draft (status: 0)
      // - For completed profiles (status >= 1): preserve existing status
      // Note: This function should only run for incomplete profiles due to shouldEnableAutosave check
      const status = profileData?.status >= 1 ? profileData.status : 0;

      const payload = {
        ...formattedData,
        status: status // Preserve existing status for completed profiles, use 0 for incomplete
      };

      // Use sendBeacon for reliable unload saving
      const apiUrl = `${import.meta.env.VITE_APP_BASE_URL}/api${API_ENDPOINTS.COMPREHENSIVE_PROFILE}/${userId}`;
      const token = localStorage.getItem('token');
      
      if (navigator.sendBeacon && token) {
        const headers = {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        };
        
        const blob = new Blob([JSON.stringify(payload)], { type: 'application/json' });
        navigator.sendBeacon(apiUrl, blob);
      }
    } catch (error) {
      console.error('Error saving on unload:', error);
    }
  }, [shouldEnableAutosave, userId, formData, currentStep, completedSteps, formatFormDataForAPI, profileData?.status]);

  // Set up beforeunload listener
  useEffect(() => {
    const handleBeforeUnload = () => {
      saveOnUnload();
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [saveOnUnload]);

  return {
    isAutoSaving,
    lastSavedData,
    lastSaveTime,
    saveError,
    saveNow,
    saveOnUnload
  };
};
