import ROUTES from "@constants/routes";
import AuthLayout from "@layouts/AuthLayout";
import MainLayout from "@layouts/MainLayout";
import UnifiedAuth from "@pages/Authentication/UnifiedAuth";
import PasswordLogin from "@pages/Authentication/PasswordLogin";
import BasicInfo from "@pages/Authentication/BasicInfo";
import Dashboard from "@pages/Dashboard";
import Shipments from "@pages/Shipments";
import Reports from "@pages/Reports";
import LandingPage from "@pages/LandingPage";
import ProfileCompletion from "@pages/ProfileCompletion";
import KYCCompletion from "@pages/KYCCompletion";
import ProfileSettings from "@pages/ProfileSettings";


import PrivateRoute from "./PrivateRoute";
import ProfileKYCGuard from "@components/ProfileKYCGuard";
import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Landing Page Route */}
        <Route path={ROUTES.LANDING} element={<LandingPage />} />



        {/* Unified Authentication Routes */}
        <Route element={<AuthLayout />}>
          <Route path={ROUTES.LOGIN} element={<UnifiedAuth />} />
          <Route path={ROUTES.PASSWORD_LOGIN} element={<PasswordLogin />} />
          <Route path={ROUTES.REGISTER} element={<UnifiedAuth />} />
          <Route path={ROUTES.MOBILE_REGISTER} element={<UnifiedAuth />} />
          <Route path={ROUTES.BASIC_INFO} element={<BasicInfo />} />
        </Route>

        {/* Protected Main Application Routes */}
        <Route element={<PrivateRoute />}>
          <Route element={<MainLayout />}>
            {/* Dashboard - Always accessible after login */}
            <Route path={ROUTES.DASHBOARD} element={<Dashboard />} />

            {/* Profile/KYC Completion Routes - Now inside MainLayout */}
            <Route path={ROUTES.PROFILE_COMPLETION} element={<ProfileCompletion />} />
            <Route path={ROUTES.KYC_COMPLETION} element={<KYCCompletion />} />

            {/* Profile Settings - Always accessible after login */}
            <Route path={ROUTES.PROFILE_SETTINGS} element={<ProfileSettings />} />

            {/* Other routes - Protected by ProfileKYCGuard */}
            <Route element={<ProfileKYCGuard />}>
              <Route path="/shipments" element={<Shipments />} />
              <Route path="/reports" element={<Reports />} />
            </Route>
          </Route>
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
