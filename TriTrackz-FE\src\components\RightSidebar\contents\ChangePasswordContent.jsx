import React, { useState } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSelector } from 'react-redux';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { useChangePassword } from '@api/authHooks';
import { useRightSidebar } from '@contexts/RightSidebarContext';
import toast from 'react-hot-toast';

const ChangePasswordContent = () => {
  const { user, profileData } = useSelector((state) => state.user);
  const { closeSidebar } = useRightSidebar();
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false
  });

  // Change password mutation
  const changePasswordMutation = useChangePassword({
    onSuccess: () => {
      toast.success('Password changed successfully!');
      formik.resetForm();
      closeSidebar();
    },
    onError: (error) => {
      const errorMessage = error?.response?.data?.message || 'Failed to change password';
      toast.error(errorMessage);
    }
  });

  // Validation schema
  const validationSchema = Yup.object({
    oldPassword: Yup.string()
      .required('Current password is required'),
    newPassword: Yup.string()
      .required('New password is required')
      .min(8, 'Password must be at least 8 characters')
      .max(15, 'Password must be at most 15 characters')
      .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
      .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
      .matches(/\d/, 'Password must contain at least one number')
      .matches(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/, 'Password must contain at least one special character'),
    confirmPassword: Yup.string()
      .required('Confirm password is required')
      .oneOf([Yup.ref('newPassword')], 'Passwords do not match')
  });

  // Formik setup
  const formik = useFormik({
    initialValues: {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    },
    validationSchema,
    onSubmit: (values) => {
      const payload = {
        mobileNumber: profileData?.mobileNumber || user?.phoneNumber,
        email: profileData?.email || user?.email || '',
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
        userType: profileData?.userType || user?.userType || 0,
        isAdmin: false
      };

      changePasswordMutation.mutate(payload);
    }
  });

  const togglePasswordVisibility = (field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <>
      <form onSubmit={formik.handleSubmit} className="sidebar-form p-3">
        {/* Current Password */}
        <div className="form-group mb-3">
          <label htmlFor="oldPassword" className="form-label fw-semibold small">
            Current Password
          </label>
          <div className="position-relative">
            <input
              type={showPasswords.old ? "text" : "password"}
              className={`form-control ${formik.touched.oldPassword && formik.errors.oldPassword ? 'is-invalid' : ''}`}
              id="oldPassword"
              name="oldPassword"
              value={formik.values.oldPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter current password"
              style={{ paddingRight: '45px' }}
            />
            <button
              type="button"
              className="btn position-absolute password-toggle-btn"
              style={{
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                border: 'none',
                background: 'none',
                color: 'var(--text-secondary)',
                padding: '4px',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px',
                transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'var(--surface-tertiary)';
                e.target.style.color = 'var(--text-primary)';
                e.target.style.transform = 'translateY(-50%) scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'none';
                e.target.style.color = 'var(--text-secondary)';
                e.target.style.transform = 'translateY(-50%) scale(1)';
              }}
              onClick={() => togglePasswordVisibility('old')}
            >
              {showPasswords.old ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
            </button>
          </div>
          {formik.touched.oldPassword && formik.errors.oldPassword && (
            <div className="invalid-feedback d-block">{formik.errors.oldPassword}</div>
          )}
        </div>

        {/* New Password */}
        <div className="form-group mb-3">
          <label htmlFor="newPassword" className="form-label fw-semibold small">
            New Password
          </label>
          <div className="position-relative">
            <input
              type={showPasswords.new ? "text" : "password"}
              className={`form-control ${formik.touched.newPassword && formik.errors.newPassword ? 'is-invalid' : ''}`}
              id="newPassword"
              name="newPassword"
              value={formik.values.newPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter new password"
              style={{ paddingRight: '45px' }}
            />
            <button
              type="button"
              className="btn position-absolute password-toggle-btn"
              style={{
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                border: 'none',
                background: 'none',
                color: 'var(--text-secondary)',
                padding: '4px',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px',
                transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'var(--surface-tertiary)';
                e.target.style.color = 'var(--text-primary)';
                e.target.style.transform = 'translateY(-50%) scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'none';
                e.target.style.color = 'var(--text-secondary)';
                e.target.style.transform = 'translateY(-50%) scale(1)';
              }}
              onClick={() => togglePasswordVisibility('new')}
            >
              {showPasswords.new ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
            </button>
          </div>
          {formik.touched.newPassword && formik.errors.newPassword && (
            <div className="invalid-feedback d-block">{formik.errors.newPassword}</div>
          )}
        </div>

        {/* Confirm Password */}
        <div className="form-group mb-3">
          <label htmlFor="confirmPassword" className="form-label fw-semibold small">
            Confirm New Password
          </label>
          <div className="position-relative">
            <input
              type={showPasswords.confirm ? "text" : "password"}
              className={`form-control ${formik.touched.confirmPassword && formik.errors.confirmPassword ? 'is-invalid' : ''}`}
              id="confirmPassword"
              name="confirmPassword"
              value={formik.values.confirmPassword}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Confirm new password"
              style={{ paddingRight: '45px' }}
            />
            <button
              type="button"
              className="btn position-absolute password-toggle-btn"
              style={{
                right: '10px',
                top: '50%',
                transform: 'translateY(-50%)',
                border: 'none',
                background: 'none',
                color: 'var(--text-secondary)',
                padding: '4px',
                width: '24px',
                height: '24px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '4px',
                transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
                cursor: 'pointer'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = 'var(--surface-tertiary)';
                e.target.style.color = 'var(--text-primary)';
                e.target.style.transform = 'translateY(-50%) scale(1.1)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'none';
                e.target.style.color = 'var(--text-secondary)';
                e.target.style.transform = 'translateY(-50%) scale(1)';
              }}
              onClick={() => togglePasswordVisibility('confirm')}
            >
              {showPasswords.confirm ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
            </button>
          </div>
          {formik.touched.confirmPassword && formik.errors.confirmPassword && (
            <div className="invalid-feedback d-block">{formik.errors.confirmPassword}</div>
          )}
        </div>

        {/* Password Requirements */}
        <div className="sidebar-section p-3 border-bottom">
          <div className="section-title small fw-bold text-uppercase text-secondary mb-3" style={{ letterSpacing: '0.5px' }}>
            Password Requirements
          </div>
          <ul className="small text-muted ps-3 mb-0" style={{ fontSize: '0.75rem' }}>
            <li>At least 8 characters long</li>
            <li>Contains uppercase and lowercase letters</li>
            <li>Contains at least one number</li>
            <li>Contains at least one special character</li>
          </ul>
        </div>
      </form>

      {/* Footer */}
      <div className="sidebar-footer p-3 border-top flex-shrink-0">
        <div className="d-flex gap-2 w-100">
          <button
            type="button"
            className="btn btn-outline-secondary flex-fill"
            onClick={closeSidebar}
            disabled={changePasswordMutation.isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`btn btn-primary flex-fill ${changePasswordMutation.isLoading ? 'loading' : ''}`}
            onClick={formik.handleSubmit}
            disabled={changePasswordMutation.isLoading || !formik.isValid}
          >
            {changePasswordMutation.isLoading ? 'Changing...' : 'Change Password'}
          </button>
        </div>
      </div>
    </>
  );
};

export default ChangePasswordContent;
