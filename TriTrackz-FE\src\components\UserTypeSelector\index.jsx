import React from "react";
import { FaTruck, FaStore, FaUser, FaShippingFast, FaUserTie } from "react-icons/fa";
import { TbTruckDelivery,TbUserHexagon, TbBuildings, TbUsersGroup, TbUser, TbSteeringWheel } from "react-icons/tb";

const UserTypeSelector = ({ selectedUserType, onUserTypeSelect, onNavigateNext, disabled = false }) => {
  const userTypes = [
    {
      id: 2,
      label: "Transport Company",
      description: "I operate a transportation business",
      icon: TbBuildings  ,
      color: "",
    },
    {
      id: 3,
      label: "Broker",
      description: "I connect shippers with carriers",
      icon: TbUserHexagon  ,
      color: "",
    },
    {
      id: 4,
      label: "Carrier",
      description: "I transport goods and cargo",
      icon:  TbTruckDelivery,
      color: "",
    },
  
    {
      id: 7,
      label: "Shipper Company",
      description: "I ship goods as a business",
      icon: TbUsersGroup ,
      color: "",
    },
    {
      id: 8,
      label: "Shipper Individual",
      description: "I ship goods as an individual",
      icon: TbUser,
      color: "",
    },
  ];

  return (
    <div className="user-type-selector">
      <div className="mb-4">
        <p className="small mb-3" style={{ color: "var(--text-secondary)" }}>
          Choose the option that best describes your role in the logistics process
        </p>
      </div>

      <div className="row g-3">
        {userTypes.map((userType) => {
          const IconComponent = userType.icon;
          const isSelected = selectedUserType === userType.id;
          
          return (
            <div key={userType.id} className="col-md-6">
              <div
                className={`card h-100 cursor-pointer user-type-card ${disabled ? "opacity-50" : ""}`}
                style={{
                  cursor: disabled ? "not-allowed" : "pointer",
                  transition: "all 0.2s ease",
                  borderColor: isSelected ? `var(--${userType.color})` : "var(--border-secondary)",
                  borderWidth: isSelected ? "2px" : "1px",
                  backgroundColor: "transparent",
                }}
                onClick={() => {
                  if (!disabled) {
                    onUserTypeSelect(userType.id);
                    if (onNavigateNext) {
                      onNavigateNext();
                    }
                  }
                }}
              >
                <div className="card-body p-3 text-center">
                  <div className="mb-2">
                    <div
                      className="rounded-circle d-inline-flex align-items-center justify-content-center border"
                      style={{
                        width: "50px",
                        height: "50px",
                        borderColor: isSelected ? `var(--${userType.color})` : "var(--border-secondary)",
                        borderWidth: isSelected ? "2px" : "1px",
                        backgroundColor: "transparent",
                      }}
                    >
                      <IconComponent
                        size={20}
                        className={`text-${userType.color}`}
                        style={{ color: `var(--${userType.color})` }}
                      />
                    </div>
                  </div>
                  <h6 className="mb-1 fw-semibold" style={{
                    color: isSelected ? `var(--${userType.color})` : "var(--text-primary)"
                  }}>
                    {userType.label}
                  </h6>
                  <small style={{ color: "var(--text-secondary)" }}>
                    {userType.description}
                  </small>
                  {isSelected && (
                    <div className="mt-2">
                      <span
                        className="badge border"
                        style={{
                          color: `var(--${userType.color})`,
                          borderColor: `var(--${userType.color})`,
                          backgroundColor: "transparent",
                        }}
                      >
                        Selected
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </div>


    </div>
  );
};

export default UserTypeSelector;
