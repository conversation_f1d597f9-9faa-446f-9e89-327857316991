import React from 'react';
import { useRightSidebar } from '@contexts/RightSidebarContext';
import ChangePasswordContent from '@components/RightSidebar/contents/ChangePasswordContent';
import ChangePhoneContent from '@components/RightSidebar/contents/ChangePhoneContent';

export const useSidebarActions = () => {
  const { openSidebar } = useRightSidebar();

  const openChangePassword = () => {
    openSidebar({
      title: 'Change Password',
      icon: null,
      ContentComponent: ChangePasswordContent,
      width: '520px'
    });
  };

  const openChangePhone = () => {
    openSidebar({
      title: 'Change Phone Number',
      icon: null,
      ContentComponent: ChangePhoneContent,
      width: '520px'
    });
  };

  return {
    openChangePassword,
    openChangePhone
  };
};
