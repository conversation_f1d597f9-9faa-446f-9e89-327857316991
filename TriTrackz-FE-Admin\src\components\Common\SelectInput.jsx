import React from 'react';
import Select from 'react-select';

const SelectInput = ({
  name,
  value,
  onChange,
  onBlur,
  options = [],
  placeholder = "Select an option",
  className = "",
  isInvalid = false,
  isValid = false,
  disabled = false,
  isMulti = false,
  isClearable = true,
  isSearchable = true,
  menuPlacement = "auto"
}) => {
  // Convert value to react-select format
  const getSelectValue = () => {
    if (!value) return null;
    
    if (isMulti) {
      return Array.isArray(value) 
        ? value.map(val => options.find(opt => opt.value === val)).filter(Boolean)
        : [];
    }
    
    return options.find(opt => opt.value === value) || null;
  };

  // Handle change event
  const handleChange = (selectedOption) => {
    let newValue;
    
    if (isMulti) {
      newValue = selectedOption ? selectedOption.map(opt => opt.value) : [];
    } else {
      newValue = selectedOption ? selectedOption.value : '';
    }

    // Create synthetic event for Formik compatibility
    const syntheticEvent = {
      target: {
        name: name,
        value: newValue
      }
    };
    
    onChange(syntheticEvent);
  };

  // Handle blur event
  const handleBlur = () => {
    if (onBlur) {
      const syntheticEvent = {
        target: {
          name: name,
          value: value
        }
      };
      onBlur(syntheticEvent);
    }
  };

  // Custom styles for react-select to match clean-form design
  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      border: `1px solid ${isInvalid ? '#dc3545' : isValid ? '#28a745' : 'var(--border-light)'}`,
      borderRadius: '6px',
      padding: '0.375rem 0.5rem',
      minHeight: '48px',
      fontSize: '1rem',
      backgroundColor: 'transparent',
      boxShadow: state.isFocused 
        ? isInvalid 
          ? '0 0 0 0.2rem rgba(220, 53, 69, 0.15)'
          : isValid
          ? '0 0 0 0.2rem rgba(40, 167, 69, 0.15)'
          : '0 0 0 0.2rem rgba(var(--primary-rgb), 0.1)'
        : 'none',
      '&:hover': {
        borderColor: isInvalid ? '#dc3545' : isValid ? '#28a745' : 'var(--primary)'
      },
      cursor: disabled ? 'not-allowed' : 'pointer'
    }),
    valueContainer: (provided) => ({
      ...provided,
      padding: '0 0.5rem',
      fontSize: '1rem'
    }),
    input: (provided) => ({
      ...provided,
      margin: '0',
      padding: '0',
      color: 'var(--text-primary)'
    }),
    placeholder: (provided) => ({
      ...provided,
      color: 'var(--text-secondary)',
      fontSize: '1rem'
    }),
    singleValue: (provided) => ({
      ...provided,
      color: 'var(--text-primary)',
      fontSize: '1rem'
    }),
    multiValue: (provided) => ({
      ...provided,
      backgroundColor: 'var(--primary)',
      borderRadius: '4px'
    }),
    multiValueLabel: (provided) => ({
      ...provided,
      color: 'white',
      fontSize: '0.875rem'
    }),
    multiValueRemove: (provided) => ({
      ...provided,
      color: 'white',
      '&:hover': {
        backgroundColor: 'rgba(255, 255, 255, 0.2)',
        color: 'white'
      }
    }),
    menu: (provided) => ({
      ...provided,
      backgroundColor: 'var(--bg-primary)',
      border: '1px solid var(--border-light)',
      borderRadius: '6px',
      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      zIndex: 9999
    }),
    menuList: (provided) => ({
      ...provided,
      padding: '0.5rem 0'
    }),
    option: (provided, state) => ({
      ...provided,
      backgroundColor: state.isSelected 
        ? 'var(--primary)' 
        : state.isFocused 
        ? 'rgba(var(--primary-rgb), 0.1)' 
        : 'transparent',
      color: state.isSelected ? 'white' : 'var(--text-primary)',
      padding: '0.75rem 1rem',
      fontSize: '1rem',
      cursor: 'pointer',
      '&:hover': {
        backgroundColor: state.isSelected ? 'var(--primary)' : 'rgba(var(--primary-rgb), 0.1)'
      }
    }),
    indicatorSeparator: (provided) => ({
      ...provided,
      backgroundColor: 'var(--border-light)'
    }),
    dropdownIndicator: (provided, state) => ({
      ...provided,
      color: state.isFocused ? 'var(--primary)' : 'var(--text-secondary)',
      '&:hover': {
        color: 'var(--primary)'
      }
    }),
    clearIndicator: (provided) => ({
      ...provided,
      color: 'var(--text-secondary)',
      '&:hover': {
        color: '#dc3545'
      }
    })
  };

  return (
    <div className={`select-input-wrapper ${className}`}>
      <Select
        name={name}
        value={getSelectValue()}
        onChange={handleChange}
        onBlur={handleBlur}
        options={options}
        placeholder={placeholder}
        isDisabled={disabled}
        isMulti={isMulti}
        isClearable={isClearable}
        isSearchable={isSearchable}
        menuPlacement={menuPlacement}
        styles={customStyles}
        className="react-select-container"
        classNamePrefix="react-select"
        theme={(theme) => ({
          ...theme,
          colors: {
            ...theme.colors,
            primary: 'var(--primary)',
            primary75: 'rgba(var(--primary-rgb), 0.75)',
            primary50: 'rgba(var(--primary-rgb), 0.5)',
            primary25: 'rgba(var(--primary-rgb), 0.25)',
          }
        })}
      />
    </div>
  );
};

export default SelectInput;
