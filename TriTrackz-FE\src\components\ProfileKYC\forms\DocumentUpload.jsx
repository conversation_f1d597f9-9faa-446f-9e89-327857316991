import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import {
  useDocumentUpload,
  useDocumentDownload,
  useDocumentDelete,
  validateFile,
  getUserIdFromStorage,
  DOCUMENT_TYPES,
} from "@api/documentUploadHooks";
import { showToast } from "@utils/toastUtils";
import { useSelector } from "react-redux";
import {
  FaCheckCircle,
  FaDownload,
  FaTrash,
  FaFileAlt,
  FaTimes,
  FaExclamationCircle,
  FaExclamationTriangle,
} from "react-icons/fa";

const DocumentUpload = ({
  documentType,
  title,
  accept = ".pdf,.jpg,.jpeg,.png",
  existingDocuments = [],
  onDocumentChange,
  optional = false,
}) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [uploadStatus, setUploadStatus] = useState(null); // 'success', 'error', null
  const [apiMessage, setApiMessage] = useState("");
  const [existingDocument, setExistingDocument] = useState(null);
  const [downloadLoading, setDownloadLoading] = useState({});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const fileInputRef = useRef(null);

  // Get user from Redux store
  const { user } = useSelector((state) => state.user);

  // Document download and delete hooks
  const documentDownloadMutation = useDocumentDownload();
  const documentDeleteMutation = useDocumentDelete({
    onSuccess: (response) => {
      setExistingDocument(null);
      setApiMessage(response?.message || "Document deleted successfully");
      setUploadStatus("success");

      // Close the delete confirmation popup
      setShowDeleteConfirm(false);
      setDocumentToDelete(null);

      // Show success message from API response in center
      showToast.success(response?.message || "Document deleted successfully");

      // Notify parent component about document change
      if (onDocumentChange) {
        onDocumentChange();
      }
    },
    onError: (error) => {
      setApiMessage(error.message || "Failed to delete document");
      setUploadStatus("error");

      // Close the delete confirmation popup
      setShowDeleteConfirm(false);
      setDocumentToDelete(null);

      // Show error message in center
      showToast.error(error.message || "Failed to delete document");
    },
  });

  // Find existing document for this document type
  useEffect(() => {
    const docTypeMap = {
      [DOCUMENT_TYPES.GST_CERTIFICATE]: "GstCertificate",
      [DOCUMENT_TYPES.PAN_CARD]: "PanCard",
      [DOCUMENT_TYPES.AADHAR_CARD]: "AadharCard",
      [DOCUMENT_TYPES.TRADE_LICENSE]: "TradeLicense",
      [DOCUMENT_TYPES.BUSINESS_LICENSE]: "BusinessLicense",
      [DOCUMENT_TYPES.REGULATORY_DOC]: "RegulatoryDoc",
    };

    const targetDocType = docTypeMap[documentType];

    const existingDoc = existingDocuments.find((doc) => {
      return doc.documentType === targetDocType;
    });

    if (existingDoc) {
      setExistingDocument(existingDoc);
      setUploadStatus("success");
      setApiMessage("Document already uploaded");
    } else {
      setExistingDocument(null);
      setUploadStatus(null);
      setApiMessage("");
    }
  }, [existingDocuments, documentType, title]);

  // Try to get user ID from different possible locations
  const getUserId = () => {
    // Try different possible user ID locations from Redux
    const possibleIds = [
      user?.id,
      user?.userId,
      user?.user?.id,
      user?.user?.userId,
      user?.data?.id,
      user?.data?.userId,
    ];

    let validId = possibleIds.find(
      (id) => id && id !== null && id !== undefined
    );

    // If no ID found in Redux, try localStorage as fallback
    if (!validId) {
      validId = getUserIdFromStorage();
    }

    return validId;
  };

  // Document upload mutation
  const documentUploadMutation = useDocumentUpload({
    onSuccess: (response) => {
      const isSuccess = response?.success === true;

      if (isSuccess) {
        setSelectedFile(null);
        setUploadStatus("success");
        setApiMessage(response?.message || "Document uploaded successfully");

        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }

        showToast.success(`${title} uploaded successfully!`);

        if (onDocumentChange) {
          setTimeout(() => {
            onDocumentChange();
          }, 500);
        }
      } else {
        setUploadStatus("error");
        setApiMessage(response?.message || `Failed to upload ${title}`);
      }
    },

    onError: (error) => {
      let errorMessage = `Failed to upload ${title}`;

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const { errors } = error.response.data;
        if (Array.isArray(errors) && errors.length > 0) {
          errorMessage = errors[0];
        } else if (typeof errors === "object") {
          errorMessage = Object.values(errors)[0];
        }
      } else if (error.message) {
        errorMessage = error.message;
      }

      setUploadStatus("error");
      setApiMessage(errorMessage);
    },
  });

  // Handle file selection and auto-upload
  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Reset previous status
    setUploadStatus(null);
    setApiMessage("");

    // Validate file
    const validation = validateFile(file, 5); // 5MB limit
    if (!validation.isValid) {
      setUploadStatus("error");
      setApiMessage(validation.errors.join(", "));
      return;
    }

    setSelectedFile(file);

    // Auto-upload the file after validation
    const userId = getUserId();
    if (!userId) {
      showToast.error("User ID not found. Please login again.");

      return;
    }

    // Upload document automatically
    documentUploadMutation.mutate({
      file: file,
      documentType: documentType,
      userId: userId,
    });
  };

  // Handle file upload
  const handleUpload = () => {
    if (!selectedFile) {
      showToast.error("Please select a file first");
      return;
    }

    const userId = getUserId();
    if (!userId) {
      showToast.error("User ID not found. Please login again.");

      return;
    }

    // Upload document
    documentUploadMutation.mutate({
      file: selectedFile,
      documentType: documentType,
      userId: userId,
    });
  };

  // Handle file removal
  const handleRemove = () => {
    setSelectedFile(null);
    setUploadStatus(null);
    setApiMessage("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle document download
  const handleDownload = async (documentFile) => {
    const userId = getUserId();
    if (!userId) {
      showToast.error("Unable to download document. User ID not found.");
      return;
    }

    const documentKey = `${documentFile.documentType}-${documentFile.fileName}`;
    setDownloadLoading((prev) => ({ ...prev, [documentKey]: true }));

    try {
      showToast.loading("Preparing download...", {
        id: `download-${documentKey}`,
      });

      const docTypeMap = {
        GstCertificate: 0,
        PanCard: 2,
        AadharCard: 4,
        TradeLicense: 1,
        BusinessLicense: 3,
        RegulatoryDoc: 12,
      };

      const result = await documentDownloadMutation.mutateAsync({
        documentId: documentFile.documentId,
        documentType: docTypeMap[documentFile.documentType],
      });

      const fileName =
        documentFile.fileName?.replace(/[^a-zA-Z0-9.\-_]/g, "_") ||
        "downloaded-file";

      // Create download link using global document object
      const url = window.URL.createObjectURL(result.blob);
      const link = globalThis.document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      globalThis.document.body.appendChild(link);
      link.click();
      globalThis.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showToast.success(`Document "${fileName}" downloaded successfully!`, {
        id: `download-${documentKey}`,
      });
    } catch (error) {
      const errorMessage =
        error?.response?.data?.message ||
        error?.message ||
        "Failed to download document. Please try again.";
      showToast.error(errorMessage, {
        id: `download-${documentKey}`,
      });
    } finally {
      setDownloadLoading((prev) => ({ ...prev, [documentKey]: false }));
    }
  };

  // Handle document delete
  const handleDelete = (document) => {
    // Prevent multiple popups from opening
    if (showDeleteConfirm || documentDeleteMutation.isPending) {
      return;
    }
    setDocumentToDelete(document);
    setShowDeleteConfirm(true);
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Confirmation popup component using ModernActionPopup design
  const DeleteConfirmationPopup = () => {
    if (!showDeleteConfirm || !documentToDelete) return null;

    const handleCancel = () => {
      setShowDeleteConfirm(false);
      setDocumentToDelete(null);
    };

    const handleConfirmDelete = async () => {
      // Prevent multiple API calls and close popup immediately
      if (documentDeleteMutation.isPending || !showDeleteConfirm) {
        return;
      }

      // Close popup immediately to prevent multiple clicks
      setShowDeleteConfirm(false);

      try {
        await documentDeleteMutation.mutateAsync({
          documentId: documentToDelete.documentId,
        });
        // Success and error handling is done in the mutation callbacks
      } catch (error) {
        // Error handling is done in the mutation callbacks
      }
    };

    return createPortal(
      <div className="modern-popup-overlay">
        <div className="modern-popup-container">
          {/* Close Button */}
          <button
            className="modern-popup-close"
            onClick={handleCancel}
            aria-label="Close"
            disabled={documentDeleteMutation.isPending}
          >
            <FaTimes size={20} />
          </button>

          {/* Content */}
          <div className="modern-popup-content">
            <div className="modern-popup-text">
              <h1 className="modern-popup-title">DELETE DOCUMENT</h1>
              <p className="modern-popup-subtitle">
                Are you sure you want to delete this document?
              </p>

              {/* Document Preview */}
              <div
                className="d-flex align-items-start p-3 mb-4"
                style={{
                  background: "var(--bg-secondary)",
                  borderRadius: "12px",
                  border: "2px dashed var(--border-secondary)",
                  minHeight: "80px",
                }}
              >
                <FaFileAlt
                  className="me-3 text-primary flex-shrink-0 mt-1"
                  size={24}
                />
                <div className="flex-grow-1 text-start" style={{ minWidth: 0 }}>
                  <div
                    className="fw-medium text-primary mb-1"
                    style={{
                      wordBreak: "break-word",
                      lineHeight: "1.4",
                      fontSize: "14px",
                    }}
                  >
                    {documentToDelete.fileName}
                  </div>
                  <small className="text-muted">
                    This action cannot be undone
                  </small>
                </div>
              </div>

              <p className="modern-popup-description">
                Once deleted, you'll need to upload a new document to complete
                your profile verification.
              </p>

              {/* Action Buttons */}
              <div className="d-flex gap-3 justify-content-center">
                <button
                  className="btn btn-outline-secondary px-4"
                  onClick={handleCancel}
                  disabled={documentDeleteMutation.isPending}
                  style={{ borderRadius: "12px" }}
                >
                  Cancel
                </button>
                <button
                  className="modern-popup-button btn-danger"
                  onClick={handleConfirmDelete}
                  disabled={documentDeleteMutation.isPending}
                >
                  {documentDeleteMutation.isPending ? (
                    <>
                      <span
                        className="spinner-border spinner-border-sm me-2"
                        role="status"
                        aria-hidden="true"
                      ></span>
                      Deleting...
                    </>
                  ) : (
                    <>
                      <FaTrash className="me-2" />
                      Delete Document
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>,
      document.body
    );
  };

  return (
    <>
      <DeleteConfirmationPopup />
      <div className="col-md-6 mb-3">
        <label className="clean-form-label">
          {title}{" "}
          {optional ? (
            <span className="text-muted">(Optional)</span>
          ) : (
            <span className="text-danger">*</span>
          )}
        </label>

        {/* Existing Document Display */}
        {existingDocument && (
          <div className="clean-document-card">
            <div className="document-info">
              <div className="document-details">
                <div className="document-name">{existingDocument.fileName}</div>
                <div className="document-status">
                  <FaCheckCircle />
                  <span>Uploaded successfully</span>
                </div>
              </div>
            </div>

            <div className="document-actions">
              <button
                type="button"
                className="btn-action btn-download"
                onClick={() => handleDownload(existingDocument)}
                disabled={
                  downloadLoading[
                    `${existingDocument.documentType}-${existingDocument.fileName}`
                  ] || documentDownloadMutation.isPending
                }
                title="Download document"
              >
                {downloadLoading[
                  `${existingDocument.documentType}-${existingDocument.fileName}`
                ] || documentDownloadMutation.isPending ? (
                  <span className="upload-spinner"></span>
                ) : (
                  <FaDownload />
                )}
              </button>
              <button
                type="button"
                className="btn-action btn-delete"
                onClick={() => handleDelete(existingDocument)}
                disabled={documentDeleteMutation.isPending || showDeleteConfirm}
                title="Delete and upload new"
              >
                {documentDeleteMutation.isPending ? (
                  <span className="upload-spinner"></span>
                ) : (
                  <FaTrash />
                )}
              </button>
            </div>
          </div>
        )}

        {/* File Input - Hide when document exists */}
        {!existingDocument && (
          <div className="mb-2">
            <input
              ref={fileInputRef}
              type="file"
              accept={accept}
              onChange={handleFileSelect}
              className="clean-form-control"
              disabled={documentUploadMutation.isPending}
            />
          </div>
        )}

        {/* Selected File Info */}
        {selectedFile && (
          <div className="file-upload-card mb-3">
            <div className="file-info-section">
              <div className="file-icon-wrapper">
                <FaFileAlt />
              </div>
              <div className="file-details">
                <div className="file-name">{selectedFile.name}</div>
                <div className="file-size">
                  {formatFileSize(selectedFile.size)}
                </div>
              </div>
            </div>

            <div className="file-actions">
              {/* Status Icon */}
              {uploadStatus === "success" && (
                <div className="status-icon success">
                  <FaCheckCircle />
                </div>
              )}
              {uploadStatus === "error" && (
                <div className="status-icon error">
                  <FaTimes />
                </div>
              )}

              {documentUploadMutation.isPending && (
                <div className="uploading-indicator">
                  <span className="upload-spinner"></span>
                  Uploading...
                </div>
              )}

              {!documentUploadMutation.isPending &&
                uploadStatus !== "success" && (
                  <button
                    type="button"
                    className="btn-remove"
                    onClick={handleRemove}
                    title="Remove file"
                  >
                    <FaTimes />
                  </button>
                )}
            </div>
          </div>
        )}

        {/* API Message Display - Only show error messages, success is shown in document card */}
        {apiMessage && uploadStatus === "error" && (
          <div className="api-message mb-2 error">
            <FaExclamationCircle className="me-2" />
            <span>{apiMessage}</span>
          </div>
        )}

        {/* Help Text - Only show when no document exists */}
        {!existingDocument && (
          <small className="text-muted">
            Supported formats: PDF, JPG, JPEG, PNG (Max 5MB)
          </small>
        )}
      </div>
    </>
  );
};

export default DocumentUpload;
