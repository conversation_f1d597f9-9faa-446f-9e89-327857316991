import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useDocumentUpload, useDocumentDownload, useDocumentDelete, validateFile, getUserIdFromStorage } from '@api/documentUploadHooks';
import toast from 'react-hot-toast';
import { useSelector } from 'react-redux';
import {
  FaCheckCircle,
  FaDownload,
  FaTrash,
  FaFileAlt,
  FaTimes,
  FaPlus,
  FaMinus,
  FaExclamationCircle,
  FaExclamationTriangle
} from 'react-icons/fa';

const MultipleDocumentUploadLikeAadhaar = ({ 
  documentType, 
  title, 
  accept = ".pdf,.jpg,.jpeg,.png", 
  existingDocuments = [], 
  onDocumentChange,
  maxDocuments = 5 
}) => {
  const [documentEntries, setDocumentEntries] = useState([{ 
    id: Date.now(), 
    selectedFile: null, 
    uploadStatus: null, 
    apiMessage: '', 
    existingDocument: null 
  }]);
  const [downloadLoading, setDownloadLoading] = useState({});
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [documentToDelete, setDocumentToDelete] = useState(null);
  const fileInputRefs = useRef({});

  // Get user from Redux store
  const { user } = useSelector((state) => state.user);

  // Document upload mutation
  const documentUploadMutation = useDocumentUpload({
    onSuccess: (response, variables) => {
      const entryId = variables.entryId;
      
      setDocumentEntries(prev => prev.map(entry => 
        entry.id === entryId 
          ? {
              ...entry,
              uploadStatus: 'success',
              apiMessage: response?.data?.message || response?.message || 'Document uploaded successfully',
              existingDocument: {
                fileName: variables.file.name,
                documentType: variables.documentType,
                uploadDate: new Date().toISOString()
              },
              selectedFile: null
            }
          : entry
      ));

      // Notify parent component about document change
      if (onDocumentChange) {
        onDocumentChange();
      }
    },
    onError: (error, variables) => {

      const entryId = variables.entryId;
      
      setDocumentEntries(prev => prev.map(entry => 
        entry.id === entryId 
          ? {
              ...entry,
              uploadStatus: 'error',
              apiMessage: error?.response?.data?.message || error.message || 'Failed to upload document'
            }
          : entry
      ));
    }
  });

  // Document download and delete hooks
  const documentDownloadMutation = useDocumentDownload();
  const documentDeleteMutation = useDocumentDelete({
    onSuccess: (response, variables) => {
      const entryId = variables.entryId;
      
      setDocumentEntries(prev => prev.map(entry => 
        entry.id === entryId 
          ? {
              ...entry,
              existingDocument: null,
              uploadStatus: null,
              apiMessage: response?.data?.message || response?.message || 'Document deleted successfully'
            }
          : entry
      ));

      // Notify parent component about document change
      if (onDocumentChange) {
        onDocumentChange();
      }
    },
    onError: (error, variables) => {

      const entryId = variables.entryId;
      
      setDocumentEntries(prev => prev.map(entry => 
        entry.id === entryId 
          ? {
              ...entry,
              uploadStatus: 'error',
              apiMessage: error?.response?.data?.message || error.message || 'Failed to delete document'
            }
          : entry
      ));
    }
  });

  // Get user ID from multiple sources
  const getUserId = () => {
    return getUserIdFromStorage() || user?.id || user?.userId;
  };

  // Initialize existing documents
  useEffect(() => {
    if (existingDocuments && existingDocuments.length > 0) {
      const relevantDocs = existingDocuments.filter(doc => doc.documentType === documentType);
      if (relevantDocs.length > 0) {
        setDocumentEntries(relevantDocs.map((doc, index) => ({
          id: Date.now() + index,
          selectedFile: null,
          uploadStatus: 'success',
          apiMessage: 'Document uploaded successfully',
          existingDocument: doc
        })));
      }
    }
  }, [existingDocuments, documentType]);

  // Handle file selection and auto-upload
  const handleFileSelect = (event, entryId) => {
    const file = event.target.files[0];
    if (!file) return;

    // Reset previous status for this entry
    setDocumentEntries(prev => prev.map(entry => 
      entry.id === entryId 
        ? { ...entry, uploadStatus: null, apiMessage: '', selectedFile: file }
        : entry
    ));

    // Validate file
    const validation = validateFile(file, 5); // 5MB limit
    if (!validation.isValid) {
      setDocumentEntries(prev => prev.map(entry => 
        entry.id === entryId 
          ? { ...entry, uploadStatus: 'error', apiMessage: validation.errors.join(', ') }
          : entry
      ));
      return;
    }

    // Auto-upload the file after validation
    const userId = getUserId();
    if (!userId) {
      toast.error('User ID not found. Please login again.');
      return;
    }

    // Upload document automatically
    documentUploadMutation.mutate({
      file: file,
      documentType: documentType,
      userId: userId,
      entryId: entryId
    });
  };

  // Add new document entry
  const addDocumentEntry = () => {
    if (documentEntries.length < maxDocuments) {
      setDocumentEntries(prev => [...prev, { 
        id: Date.now(), 
        selectedFile: null, 
        uploadStatus: null, 
        apiMessage: '', 
        existingDocument: null 
      }]);
    }
  };

  // Remove document entry
  const removeDocumentEntry = (entryId) => {
    if (documentEntries.length > 1) {
      setDocumentEntries(prev => prev.filter(entry => entry.id !== entryId));
    }
  };

  // Handle file removal
  const handleRemove = (entryId) => {
    setDocumentEntries(prev => prev.map(entry => 
      entry.id === entryId 
        ? { ...entry, selectedFile: null, uploadStatus: null, apiMessage: '' }
        : entry
    ));
    if (fileInputRefs.current[entryId]) {
      fileInputRefs.current[entryId].value = '';
    }
  };

  // Handle document download
  const handleDownload = async (documentFile, entryId) => {
    const userId = getUserId();
    if (!userId) {
      toast.error("Unable to download document. User ID not found.");
      return;
    }

    const documentKey = `${entryId}-${documentFile.fileName}`;
    setDownloadLoading((prev) => ({ ...prev, [documentKey]: true }));

    try {
      // Map document types to their enum values
      const docTypeMap = {
        GstCertificate: 0,
        PanCard: 2,
        AadharCard: 4,
        TradeLicense: 1,
        BusinessLicense: 3,
        RegulatoryDoc: 12,
      };

      const result = await documentDownloadMutation.mutateAsync({
        documentId: documentFile.documentId,
        documentType: docTypeMap[documentFile.documentType]
      });

      const fileName = documentFile.fileName?.replace(/[^a-zA-Z0-9.\-_]/g, "_") || "downloaded-file";

      // Create download link using global document object
      const url = window.URL.createObjectURL(result.blob);
      const link = globalThis.document.createElement("a");
      link.href = url;
      link.setAttribute("download", fileName);
      globalThis.document.body.appendChild(link);
      link.click();
      globalThis.document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success(`Document "${fileName}" downloaded successfully!`);
    } catch (error) {
      toast.error('Failed to download document');
    } finally {
      setDownloadLoading((prev) => ({ ...prev, [documentKey]: false }));
    }
  };

  // Handle document delete
  const handleDelete = (document, entryId) => {
    setDocumentToDelete({ document, entryId });
    setShowDeleteConfirm(true);
  };

  // Confirm delete
  const confirmDelete = async () => {
    if (!documentToDelete) return;

    const { document, entryId } = documentToDelete;
    const userId = getUserId();
    
    if (!userId) {
      toast.error("Unable to delete document. User ID not found.");
      return;
    }

    try {
      await documentDeleteMutation.mutateAsync({
        userId: userId,
        fileName: document.fileName,
        documentType: document.documentType,
        entryId: entryId
      });
    } catch {
      // Silent error handling
    } finally {
      setShowDeleteConfirm(false);
      setDocumentToDelete(null);
    }
  };

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Delete confirmation popup
  const DeleteConfirmationPopup = () => {
    if (!showDeleteConfirm) return null;

    return createPortal(
      <div className="modal-overlay" style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 9999
      }}>
        <div className="confirmation-popup" style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          padding: '24px',
          maxWidth: '400px',
          width: '90%',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)'
        }}>
          <div className="popup-header" style={{ textAlign: 'center', marginBottom: '20px' }}>
            <div style={{
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              backgroundColor: '#fee2e2',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 16px'
            }}>
              <FaExclamationTriangle style={{ color: '#dc2626', fontSize: '24px' }} />
            </div>
            <h3 style={{ margin: 0, color: '#1f2937', fontSize: '18px', fontWeight: '600' }}>
              Delete Document
            </h3>
          </div>
          
          <p style={{ 
            textAlign: 'center', 
            color: '#6b7280', 
            margin: '0 0 24px',
            lineHeight: '1.5'
          }}>
            Are you sure you want to delete this document? This action cannot be undone.
          </p>
          
          <div style={{ display: 'flex', gap: '12px', justifyContent: 'center' }}>
            <button
              onClick={() => {
                setShowDeleteConfirm(false);
                setDocumentToDelete(null);
              }}
              style={{
                padding: '10px 20px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                backgroundColor: 'white',
                color: '#374151',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '500'
              }}
            >
              Cancel
            </button>
            <button
              onClick={confirmDelete}
              disabled={documentDeleteMutation.isPending}
              style={{
                padding: '10px 20px',
                border: 'none',
                borderRadius: '8px',
                backgroundColor: '#dc2626',
                color: 'white',
                cursor: documentDeleteMutation.isPending ? 'not-allowed' : 'pointer',
                fontSize: '14px',
                fontWeight: '500',
                opacity: documentDeleteMutation.isPending ? 0.7 : 1
              }}
            >
              {documentDeleteMutation.isPending ? 'Deleting...' : 'Delete'}
            </button>
          </div>
        </div>
      </div>,
      document.body
    );
  };

  return (
    <>
      <DeleteConfirmationPopup />
      {documentEntries.map((entry, index) => (
        <div key={entry.id} className="col-md-6 mb-3">
          <div className="d-flex justify-content-between align-items-center mb-2">
            <label className="clean-form-label">
              {title} {index > 0 && `${index + 1}`} <span className="text-muted">(Optional)</span>
            </label>
            <div className="d-flex gap-1">
              {documentEntries.length < maxDocuments && (
                <button
                  type="button"
                  className="btn btn-outline-success btn-sm"
                  onClick={addDocumentEntry}
                  style={{ 
                    padding: '4px 8px',
                    fontSize: '11px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}
                  title="Add Document"
                >
                  <FaPlus size={10} />
                </button>
              )}
              {documentEntries.length > 1 && (
                <button
                  type="button"
                  className="btn btn-outline-danger btn-sm"
                  onClick={() => removeDocumentEntry(entry.id)}
                  style={{ 
                    padding: '4px 8px',
                    fontSize: '11px',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '4px'
                  }}
                  title="Remove Document"
                >
                  <FaMinus size={10} />
                </button>
              )}
            </div>
          </div>

          {/* Existing Document Display - Same as Aadhaar */}
          {entry.existingDocument && (
            <div className="clean-document-card">
              <div className="document-info">
                <div className="document-details">
                  <div className="document-name">{entry.existingDocument.fileName}</div>
                  <div className="document-status">
                    <FaCheckCircle />
                    <span>Uploaded successfully</span>
                  </div>
                </div>
              </div>

              <div className="document-actions">
                <button
                  type="button"
                  className="btn-action btn-download"
                  onClick={() => handleDownload(entry.existingDocument, entry.id)}
                  disabled={downloadLoading[`${entry.id}-${entry.existingDocument.fileName}`]}
                  title="Download document"
                >
                  {downloadLoading[`${entry.id}-${entry.existingDocument.fileName}`] ? (
                    <span className="upload-spinner"></span>
                  ) : (
                    <FaDownload />
                  )}
                </button>
                <button
                  type="button"
                  className="btn-action btn-delete"
                  onClick={() => handleDelete(entry.existingDocument, entry.id)}
                  disabled={documentDeleteMutation.isPending}
                  title="Delete and upload new"
                >
                  {documentDeleteMutation.isPending ? (
                    <span className="upload-spinner"></span>
                  ) : (
                    <FaTrash />
                  )}
                </button>
              </div>
            </div>
          )}

          {/* File Input - Same as Aadhaar */}
          {!entry.existingDocument && (
            <div className="mb-2">
              <input
                ref={el => fileInputRefs.current[entry.id] = el}
                type="file"
                accept={accept}
                onChange={(e) => handleFileSelect(e, entry.id)}
                className="clean-form-control"
                disabled={documentUploadMutation.isPending}
              />
            </div>
          )}

          {/* Selected File Display - Same as Aadhaar */}
          {entry.selectedFile && !entry.existingDocument && (
            <div className="file-upload-card">
              <div className="file-info-section">
                <div className="file-icon-wrapper">
                  <FaFileAlt />
                </div>
                <div className="file-details">
                  <div className="file-name">{entry.selectedFile.name}</div>
                  <div className="file-size">{formatFileSize(entry.selectedFile.size)}</div>
                </div>
              </div>

              <div className="file-actions">
                {/* Status Icon */}
                {entry.uploadStatus === 'success' && (
                  <div className="status-icon success">
                    <FaCheckCircle />
                  </div>
                )}
                {entry.uploadStatus === 'error' && (
                  <div className="status-icon error">
                    <FaTimes />
                  </div>
                )}

                {documentUploadMutation.isPending && (
                  <div className="uploading-indicator">
                    <span className="upload-spinner"></span>
                    Uploading...
                  </div>
                )}

                {!documentUploadMutation.isPending && entry.uploadStatus !== 'success' && (
                  <button
                    type="button"
                    className="btn-remove"
                    onClick={() => handleRemove(entry.id)}
                    title="Remove file"
                  >
                    <FaTimes />
                  </button>
                )}
              </div>
            </div>
          )}

          {/* API Response Message - Only show error messages, success is shown in document card */}
          {entry.apiMessage && entry.uploadStatus === 'error' && (
            <div className="api-message error">
              <FaExclamationCircle className="message-icon" />
              <span>{entry.apiMessage}</span>
            </div>
          )}
        </div>
      ))}
    </>
  );
};

export default MultipleDocumentUploadLikeAadhaar;
