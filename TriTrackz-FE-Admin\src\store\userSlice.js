import { createSlice } from "@reduxjs/toolkit";

const initialState = {
  user: null,
  token: null,
  isLoading: false,

  // Authentication flow state
  authData: {
    phoneNumber: null,
    isPhoneVerified: false,
    otpRequestId: null,
    selectedUserType: null,
    existingUserTypes: [], // Array of existing user types for this phone number
    isNewRegistration: false,
    basicInfoCompleted: false,
    hasExistingCredentials: false, // Whether user has existing password credentials
  },

  // User type conflict resolution
  userTypeConflict: {
    hasConflict: false,
    existingUserType: null,
    requestedUserType: null,
    allowParallelRegistration: false,
  },

  // Profile and KYC status
  profileStatus: {
    isProfileCompleted: false,
    isKYCCompleted: false,
    profileCompletionPercentage: 0,
  },

  // Comprehensive profile data
  profileData: null,
};

const userSlice = createSlice({
  name: "user",
  initialState,
  reducers: {
    setUser: (state, action) => {
      state.user = action.payload;
    },
    setToken: (state, action) => {
      state.token = action.payload;
    },
    resetUser: (state) => {
      state.user = null;
      state.token = null;
      state.isLoading = false;
      state.authData = initialState.authData;
      state.userTypeConflict = initialState.userTypeConflict;
      state.profileStatus = initialState.profileStatus;
      state.profileData = null;
    },
    setIsLoading: (state, action) => {
      state.isLoading = action.payload;
    },

    // Authentication flow actions
    setPhoneNumber: (state, action) => {
      state.authData.phoneNumber = action.payload;
    },
    setPhoneVerified: (state, action) => {
      state.authData.isPhoneVerified = action.payload;
    },
    setOtpRequestId: (state, action) => {
      state.authData.otpRequestId = action.payload;
    },
    setSelectedUserType: (state, action) => {
      state.authData.selectedUserType = action.payload;
    },
    setExistingUserTypes: (state, action) => {
      state.authData.existingUserTypes = action.payload;
    },
    setIsNewRegistration: (state, action) => {
      state.authData.isNewRegistration = action.payload;
    },
    setBasicInfoCompleted: (state, action) => {
      state.authData.basicInfoCompleted = action.payload;
    },
    setHasExistingCredentials: (state, action) => {
      state.authData.hasExistingCredentials = action.payload;
    },
    resetAuthData: (state) => {
      state.authData = initialState.authData;
    },

    // User type conflict actions
    setUserTypeConflict: (state, action) => {
      state.userTypeConflict = { ...state.userTypeConflict, ...action.payload };
    },
    clearUserTypeConflict: (state) => {
      state.userTypeConflict = initialState.userTypeConflict;
    },
    setAllowParallelRegistration: (state, action) => {
      state.userTypeConflict.allowParallelRegistration = action.payload;
    },

    // Profile and KYC actions
    setProfileStatus: (state, action) => {
      state.profileStatus = { ...state.profileStatus, ...action.payload };
    },
    setProfileCompleted: (state, action) => {
      state.profileStatus.isProfileCompleted = action.payload;
    },
    setKYCCompleted: (state, action) => {
      state.profileStatus.isKYCCompleted = action.payload;
    },
    setProfileCompletionPercentage: (state, action) => {
      state.profileStatus.profileCompletionPercentage = action.payload;
    },

    // Profile data actions
    setProfileData: (state, action) => {
      state.profileData = action.payload;

      // Update profile status based on the API response
      if (action.payload) {
        const { status } = action.payload;

        // Only check status field:
        // Status 0 = Incomplete (show popup)
        // Status 1+ = Complete (no popup needed)
        const isProfileCompleted = status >= 1;
        const isKYCCompleted = status >= 1;

        state.profileStatus.isProfileCompleted = isProfileCompleted;
        state.profileStatus.isKYCCompleted = isKYCCompleted;

        // Calculate completion percentage based on status
        const completionPercentage = status >= 1 ? 100 : 0;
        state.profileStatus.profileCompletionPercentage = completionPercentage;
      }
    },
    clearProfileData: (state) => {
      state.profileData = null;
    },
  },
});

export const {
  setUser,
  setToken,
  resetUser,
  setIsLoading,
  // Authentication flow actions
  setPhoneNumber,
  setPhoneVerified,
  setOtpRequestId,
  setSelectedUserType,
  setExistingUserTypes,
  setIsNewRegistration,
  setBasicInfoCompleted,
  setHasExistingCredentials,
  resetAuthData,
  // User type conflict actions
  setUserTypeConflict,
  clearUserTypeConflict,
  setAllowParallelRegistration,
  // Profile and KYC actions
  setProfileStatus,
  setProfileCompleted,
  setKYCCompleted,
  setProfileCompletionPercentage,
  // Profile data actions
  setProfileData,
  clearProfileData,
} = userSlice.actions;

export default userSlice.reducer;
