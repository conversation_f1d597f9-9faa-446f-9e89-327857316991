import React, { createContext, useContext, useEffect, useState } from 'react';

const BootstrapThemeContext = createContext();

export const useBootstrapTheme = () => {
  const context = useContext(BootstrapThemeContext);
  if (!context) {
    throw new Error('useBootstrapTheme must be used within a BootstrapThemeProvider');
  }
  return context;
};

export const BootstrapThemeProvider = ({ children }) => {
  // Initialize theme from localStorage or default to 'dark'
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem('bootstrap-theme');
    return savedTheme || 'dark';
  });

  // Apply theme to document root
  useEffect(() => {
    document.documentElement.setAttribute('data-bs-theme', theme);
    localStorage.setItem('bootstrap-theme', theme);
  }, [theme]);

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'dark' ? 'light' : 'dark');
  };

  const setLightTheme = () => {
    setTheme('light');
  };

  const setDarkTheme = () => {
    setTheme('dark');
  };

  const isDark = theme === 'dark';
  const isLight = theme === 'light';

  const value = {
    theme,
    isDark,
    isLight,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
  };

  return (
    <BootstrapThemeContext.Provider value={value}>
      {children}
    </BootstrapThemeContext.Provider>
  );
};
