import React from 'react';
import { 
  FaFileCheck, 
  FaCheckCircle, 
  FaEye, 
  FaDownload, 
  FaTrash, 
  FaFileAlt, 
  FaTimes, 
  FaUpload,
  FaExclamationCircle 
} from 'react-icons/fa';

const TestDocumentUpload = () => {
  return (
    <div style={{ padding: '20px', maxWidth: '600px', margin: '0 auto' }}>
      <h2>Document Upload - React Icons Test</h2>
      
      {/* Test the border-only design with React Icons */}
      <div className="clean-document-card">
        <div className="document-info">
          <div className="document-icon">
            <FaFileCheck />
          </div>
          <div className="document-details">
            <div className="document-name">videoframe_71390.png</div>
            <div className="document-status">
              <FaCheckCircle />
              <span>Uploaded successfully</span>
            </div>
          </div>
        </div>

        <div className="document-actions">
          <button type="button" className="btn-action btn-view" title="View document">
            <FaEye />
          </button>
          <button type="button" className="btn-action btn-download" title="Download document">
            <FaDownload />
          </button>
          <button type="button" className="btn-action btn-delete" title="Delete document">
            <FaTrash />
          </button>
        </div>
      </div>

      {/* Success Message */}
      <div className="api-message success">
        <FaCheckCircle className="me-2" />
        <span>Document already uploaded</span>
      </div>

      {/* Error Message Example */}
      <div className="api-message error">
        <FaExclamationCircle className="me-2" />
        <span>Upload failed - please try again</span>
      </div>

      {/* File Upload Card Example */}
      <div className="file-upload-card mb-3">
        <div className="file-info-section">
          <div className="file-icon-wrapper">
            <FaFileAlt />
          </div>
          <div className="file-details">
            <div className="file-name">sample-document.pdf</div>
            <div className="file-size">2.5 MB</div>
          </div>
        </div>

        <div className="file-actions">
          <button type="button" className="btn-upload">
            <FaUpload />
            Upload
          </button>
          <button type="button" className="btn-remove" title="Remove file">
            <FaTimes />
          </button>
        </div>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', background: '#f8f9fa', borderRadius: '8px' }}>
        <h4>✅ React Icons Implementation Complete!</h4>
        <ul>
          <li>✅ Replaced all FontAwesome <code>&lt;i&gt;</code> classes</li>
          <li>✅ Using React Icons components</li>
          <li>✅ Border-only design maintained</li>
          <li>✅ Proper View, Download, Delete icons</li>
          <li>✅ Clean, modern appearance</li>
        </ul>
      </div>
    </div>
  );
};

export default TestDocumentUpload;
