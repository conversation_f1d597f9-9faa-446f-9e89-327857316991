import { useEffect, useRef } from "react";

/**
 * Custom hook to handle beforeunload events
 * @param {Function} handler - Function to call before unload
 * @param {boolean} enabled - Whether the hook is enabled
 */
export const useBeforeUnload = (handler, enabled = true) => {
  const handlerRef = useRef(handler);

  // Update handler ref when it changes
  useEffect(() => {
    handlerRef.current = handler;
  }, [handler]);

  useEffect(() => {
    if (!enabled) return;

    const handleBeforeUnload = (event) => {
      if (handlerRef.current) {
        handlerRef.current(event);
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [enabled]);
};

/**
 * Custom hook for autosave on unload
 * @param {Function} saveFunction - Function to call for saving
 * @param {boolean} enabled - Whether autosave on unload is enabled
 */
export const useAutosaveOnUnload = (saveFunction, enabled = true) => {
  const saveFunctionRef = useRef(saveFunction);

  // Update save function ref when it changes
  useEffect(() => {
    saveFunctionRef.current = saveFunction;
  }, [saveFunction]);

  useEffect(() => {
    if (!enabled) return;

    const handleBeforeUnload = () => {
      if (saveFunctionRef.current) {
        saveFunctionRef.current();
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, [enabled]);
};
