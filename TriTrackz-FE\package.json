{"name": "trit<PERSON><PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@popperjs/core": "^2.11.8", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.77.1", "axios": "^1.9.0", "bootstrap": "5.3.6", "formik": "^2.4.6", "leaflet": "^1.9.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-router-dom": "^7.6.1", "react-select": "^5.10.2", "reactstrap": "^9.2.3", "redux-persist": "^6.0.0", "sass": "^1.89.2", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "cssnano": "^7.1.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss-cli": "^11.0.1", "vite": "^6.3.5"}}