import React, { useState, useMemo, useCallback, memo } from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { useSelector } from "react-redux";
import { FaEye, FaEyeSlash } from "react-icons/fa";
import { useChangePassword } from "@api/authHooks";
import { useRightSidebar } from "@contexts/RightSidebarContext";
import toast from "react-hot-toast";

// Removed inline styles - using Bootstrap classes instead

// Memoized PasswordField component
const PasswordField = memo(
  ({
    id,
    name,
    label,
    placeholder,
    value,
    onChange,
    onBlur,
    showPassword,
    onToggleVisibility,
    error,
    touched,
  }) => (
    <div className="mb-3">
      <label htmlFor={id} className="form-label fw-semibold small">
        {label}
      </label>
      <div className="position-relative">
        <input
          type={showPassword ? "text" : "password"}
          className={`form-control pe-5 ${
            touched && error ? "is-invalid" : ""
          }`}
          id={id}
          name={name}
          value={value}
          onChange={onChange}
          onBlur={onBlur}
          placeholder={placeholder}
        />
        <button
          type="button"
          className="btn position-absolute top-50 end-0 translate-middle-y me-2 p-0 border-0 bg-transparent d-flex align-items-center justify-content-center text-muted"
          onClick={onToggleVisibility}
          style={{ width: "20px", height: "20px" }}
        >
          {showPassword ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
        </button>
      </div>
      {touched && error && (
        <div className="invalid-feedback d-block">{error}</div>
      )}
    </div>
  )
);

PasswordField.displayName = "PasswordField";

const ChangePassword = () => {
  const { user, profileData } = useSelector((state) => state.user);
  const { closeSidebar } = useRightSidebar();
  const [showPasswords, setShowPasswords] = useState({
    old: false,
    new: false,
    confirm: false,
  });

  // Memoize validation schema to prevent recreation
  const validationSchema = useMemo(
    () =>
      Yup.object({
        oldPassword: Yup.string().required("Current password is required"),
        newPassword: Yup.string()
          .min(8, "Password must be at least 8 characters")
          .matches(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
            "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character"
          )
          .required("New password is required"),
        confirmPassword: Yup.string()
          .oneOf([Yup.ref("newPassword"), null], "Passwords must match")
          .required("Please confirm your new password"),
      }),
    []
  );

  // Memoize success and error handlers
  const handleSuccess = useCallback(() => {
    toast.success("Password changed successfully!");
    formik.resetForm();
    closeSidebar();
  }, [closeSidebar]);

  const handleError = useCallback((error) => {
    const errorMessage =
      error?.response?.data?.message || "Failed to change password";
    toast.error(errorMessage);
  }, []);

  // Change password mutation
  const changePasswordMutation = useChangePassword({
    onSuccess: handleSuccess,
    onError: handleError,
  });

  // Memoize submit handler
  const handleSubmit = useCallback(
    (values) => {
      const payload = {
        mobileNumber: profileData?.mobileNumber || user?.phoneNumber,
        email: profileData?.email || user?.email || "",
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
        confirmPassword: values.confirmPassword,
        userType: profileData?.userType || user?.userType || 0,
        isAdmin: true,
      };

      changePasswordMutation.mutate(payload);
    },
    [profileData, user, changePasswordMutation]
  );

  // Formik setup
  const formik = useFormik({
    initialValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
    validationSchema,
    onSubmit: handleSubmit,
  });

  // Memoize toggle handlers
  const toggleOldPassword = useCallback(() => {
    setShowPasswords((prev) => ({ ...prev, old: !prev.old }));
  }, []);

  const toggleNewPassword = useCallback(() => {
    setShowPasswords((prev) => ({ ...prev, new: !prev.new }));
  }, []);

  const toggleConfirmPassword = useCallback(() => {
    setShowPasswords((prev) => ({ ...prev, confirm: !prev.confirm }));
  }, []);

  return (
    <>
      {/* Header Section */}
      <div className="sidebar-section p-3 border-bottom">
        <div
          className="section-title small fw-bold text-uppercase text-secondary mb-3"
          style={{ letterSpacing: "0.5px" }}
        >
          Security Settings
        </div>
        <div
          className="p-3 rounded-2"
          style={{
            fontSize: "0.875rem",
            backgroundColor: "var(--bg-surface)",
          }}
        >
          <strong className="text-primary">Change your password</strong>
          <br />
          <span className="text-secondary">
            Enter your current password and choose a new secure password.
          </span>
        </div>
      </div>

      {/* Form Section */}
      <form onSubmit={formik.handleSubmit} className="sidebar-form p-3">
        <PasswordField
          id="oldPassword"
          name="oldPassword"
          label="Current Password"
          placeholder="Enter current password"
          value={formik.values.oldPassword}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          showPassword={showPasswords.old}
          onToggleVisibility={toggleOldPassword}
          error={formik.errors.oldPassword}
          touched={formik.touched.oldPassword}
        />

        <PasswordField
          id="newPassword"
          name="newPassword"
          label="New Password"
          placeholder="Enter new password"
          value={formik.values.newPassword}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          showPassword={showPasswords.new}
          onToggleVisibility={toggleNewPassword}
          error={formik.errors.newPassword}
          touched={formik.touched.newPassword}
        />

        <PasswordField
          id="confirmPassword"
          name="confirmPassword"
          label="Confirm New Password"
          placeholder="Confirm new password"
          value={formik.values.confirmPassword}
          onChange={formik.handleChange}
          onBlur={formik.handleBlur}
          showPassword={showPasswords.confirm}
          onToggleVisibility={toggleConfirmPassword}
          error={formik.errors.confirmPassword}
          touched={formik.touched.confirmPassword}
        />
      </form>

      {/* Footer */}
      <div className="sidebar-footer p-3 border-top flex-shrink-0">
        <div className="d-flex gap-2 w-100">
          <button
            type="button"
            className="btn btn-outline-secondary flex-fill"
            onClick={closeSidebar}
            disabled={changePasswordMutation.isLoading}
          >
            Cancel
          </button>
          <button
            type="submit"
            className={`btn btn-primary flex-fill ${
              changePasswordMutation.isLoading ? "loading" : ""
            }`}
            onClick={formik.handleSubmit}
            disabled={changePasswordMutation.isLoading || !formik.isValid}
          >
            {changePasswordMutation.isLoading
              ? "Changing..."
              : "Change Password"}
          </button>
        </div>
      </div>
    </>
  );
};

export default ChangePassword;
