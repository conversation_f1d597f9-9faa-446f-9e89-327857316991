import React from 'react';
import { Fa<PERSON>pinner, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';

const AutosaveStatusAlert = ({
  isAutoSaving,
  lastSaveTime,
  timeSinceLastSave,
  saveError,
  hasUnsavedChanges,
  className = '',
  style = {}
}) => {
  // Don't show if no relevant status to display
  if (!isAutoSaving && !lastSaveTime && !saveError && !hasUnsavedChanges) {
    return null;
  }

  const getStatusConfig = () => {
    if (isAutoSaving) {
      return {
        icon: <FaSpinner className="autosave-spinner" />,
        message: "Syncing...",
        variant: "syncing"
      };
    }

    if (saveError) {
      return {
        icon: <FaExclamationTriangle />,
        message: "Sync failed",
        variant: "error"
      };
    }

    if (lastSaveTime) {
      return {
        icon: <FaCheckCircle />,
        message: "Synced",
        variant: "synced"
      };
    }

    if (hasUnsavedChanges) {
      return {
        icon: <FaSpinner className="autosave-spinner" />,
        message: "Syncing...",
        variant: "syncing"
      };
    }

    return null;
  };

  const statusConfig = getStatusConfig();
  if (!statusConfig) return null;

  return (
    <div
      className={`autosave-compact ${statusConfig.variant} ${className}`}
      style={style}
    >
      <span className="autosave-icon">
        {statusConfig.icon}
      </span>
      <span className="autosave-text">{statusConfig.message}</span>
    </div>
  );
};

export default AutosaveStatusAlert;
