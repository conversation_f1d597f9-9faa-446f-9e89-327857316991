import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMapEvents } from 'react-leaflet';

import L from 'leaflet';
import toast from 'react-hot-toast';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Component to handle map clicks and GPS location
const MapClickHandler = ({ onLocationSelect, selectedPosition, setSelectedPosition }) => {
  useMapEvents({
    click(e) {
      const { lat, lng } = e.latlng;
      setSelectedPosition([lat, lng]);
      onLocationSelect(lat, lng);
    },
  });

  return selectedPosition ? (
    <Marker
      position={selectedPosition}
      draggable={true}
      eventHandlers={{
        dragend: (e) => {
          const marker = e.target;
          const position = marker.getLatLng();
          const newPos = [position.lat, position.lng];
          setSelectedPosition(newPos);
          onLocationSelect(position.lat, position.lng);
        },
      }}
    >
      <Popup>
        <div>
          <strong>📍 Selected Location</strong><br />
          <small>Drag pin to adjust exact position</small><br />
          Lat: {selectedPosition[0].toFixed(6)}<br />
          Lng: {selectedPosition[1].toFixed(6)}
        </div>
      </Popup>
    </Marker>
  ) : null;
};

/**
 * LocationMap Component with GPS functionality
 * Allows users to select location by clicking on map or using GPS
 */
const LocationMap = ({
  latitude,
  longitude,
  onLocationSelect,
  height = '400px',
  width = '100%',
  zoom = 13,
  enableGPS = true,
  enableMapClick = true
}) => {
  const mapRef = useRef(null);
  const [selectedPosition, setSelectedPosition] = useState(null);
  const [isLoadingGPS, setIsLoadingGPS] = useState(false);
  const [userLocation, setUserLocation] = useState(null);


  // Set initial position if coordinates are provided
  useEffect(() => {
    if (latitude && longitude) {
      setSelectedPosition([latitude, longitude]);
    }
  }, [latitude, longitude]);

  // Update map view when coordinates change
  useEffect(() => {
    if (mapRef.current && selectedPosition) {
      const map = mapRef.current;
      map.setView(selectedPosition, zoom);
    }
  }, [selectedPosition, zoom]);

  // Get user's current GPS location
  const getCurrentLocation = () => {
    if (!navigator.geolocation) {
      toast.error('Geolocation is not supported by this browser');
      return;
    }

    setIsLoadingGPS(true);
    
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude: lat, longitude: lng } = position.coords;
        setUserLocation([lat, lng]);
        setSelectedPosition([lat, lng]);
        setIsLoadingGPS(false);
        
        // Call the callback to update form fields
        if (onLocationSelect) {
          onLocationSelect(lat, lng);
        }
        
        toast.success('Location detected successfully');
      },
      (error) => {
        setIsLoadingGPS(false);
        let errorMessage = 'Unable to get your location';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied. Please enable location permissions.';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information is unavailable.';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out.';
            break;
          default:
            errorMessage = 'An unknown error occurred while getting location.';
            break;
        }
        
        toast.error(errorMessage);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000
      }
    );
  };



  // Default center (India)
  const defaultCenter = [20.5937, 78.9629];
  const mapCenter = selectedPosition || userLocation || defaultCenter;

  return (
    <div style={{ height, width, background: 'transparent' }} className="rounded-3 overflow-hidden">
      {/* GPS Controls */}
      <div className="map-controls p-3">
        <div className="d-flex justify-content-end align-items-center mb-3">
          {enableGPS && (
            <button
              type="button"
              className="btn btn-sm btn-outline-primary d-flex align-items-center"
              onClick={getCurrentLocation}
              disabled={isLoadingGPS}
            >
              {isLoadingGPS ? (
                <>
                  <span className="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                </>
              ) : (
                <>
                  <i className="fas fa-location-arrow me-1"></i>
                  Use My Location
                </>
              )}
            </button>
          )}
        </div>




      </div>

      {/* Map Container */}
      <div style={{ height: 'calc(100% - 120px)' }}>
        <MapContainer
          center={mapCenter}
          zoom={zoom}
          style={{ height: '100%', width: '100%' }}
          ref={mapRef}
        >
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          />
          
          {enableMapClick && (
            <MapClickHandler
              onLocationSelect={onLocationSelect}
              selectedPosition={selectedPosition}
              setSelectedPosition={setSelectedPosition}
            />
          )}
          
          {/* Show user's current location if available */}
          {userLocation && userLocation !== selectedPosition && (
            <Marker 
              position={userLocation}
              icon={L.divIcon({
                className: 'user-location-marker',
                html: '<div style="background: #007bff; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white; box-shadow: 0 0 5px rgba(0,0,0,0.3);"></div>',
                iconSize: [16, 16],
                iconAnchor: [8, 8]
              })}
            >
              <Popup>
                <div>
                  <strong>Your Current Location</strong><br />
                  Lat: {userLocation[0].toFixed(6)}<br />
                  Lng: {userLocation[1].toFixed(6)}
                </div>
              </Popup>
            </Marker>
          )}
        </MapContainer>
      </div>
    </div>
  );
};

export default LocationMap;
