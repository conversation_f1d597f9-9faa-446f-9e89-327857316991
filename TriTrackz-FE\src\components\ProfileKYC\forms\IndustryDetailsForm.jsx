import React from "react";
import { Field, ErrorMessage } from "formik";
import { INDUSTRY_TYPES, BUSINESS_CATEGORIES } from "@constants/enum";
import { DOCUMENT_TYPES } from "@api/documentUploadHooks";
import SelectInput from "@components/Common/SelectInput";
import MultipleDocumentUploadLikeAadhaar from "./MultipleDocumentUploadLikeAadhaar";

const IndustryDetailsForm = ({
  formik,
  existingDocuments,
  onDocumentChange,
}) => {
  const industryType = formik.values.industryDetails?.industryType;
  const businessCategory = formik.values.industryDetails?.businessCategory;

  return (
    <div className="clean-form-section">
      <div className="clean-section-header">
        <h3 className="clean-section-title">Industry Details</h3>
        <p className="clean-section-subtitle">
          Please provide your industry and business category information
        </p>
      </div>
      <div className="clean-form-fields">
        <div className="row">
          <div className="col-md-6 mb-3">
            <label
              htmlFor="industryDetails.industryType"
              className="clean-form-label"
            >
              Industry Type <span className="text-danger">*</span>
            </label>
            <SelectInput
              name="industryDetails.industryType"
              value={formik.values.industryDetails?.industryType || ""}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              options={INDUSTRY_TYPES}
              placeholder="Select industry type"
              isInvalid={
                formik.touched.industryDetails?.industryType &&
                formik.errors.industryDetails?.industryType
              }
              isValid={
                formik.touched.industryDetails?.industryType &&
                formik.values.industryDetails?.industryType &&
                !formik.errors.industryDetails?.industryType
              }
              isClearable={true}
              isSearchable={true}
            />
            <ErrorMessage
              name="industryDetails.industryType"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Custom Industry Type Input - Show when "Others" is selected */}
          {industryType === "Others" && (
            <div className="col-md-6 mb-3">
              <label
                htmlFor="industryDetails.customIndustryType"
                className="clean-form-label"
              >
                Custom Industry Type <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="industryDetails.customIndustryType"
                className={`clean-form-control ${
                  formik.touched.industryDetails?.customIndustryType &&
                  formik.errors.industryDetails?.customIndustryType
                    ? "is-invalid"
                    : formik.touched.industryDetails?.customIndustryType &&
                      formik.values.industryDetails?.customIndustryType
                    ? "is-valid"
                    : ""
                }`}
                placeholder="Enter custom industry type"
              />
              <ErrorMessage
                name="industryDetails.customIndustryType"
                component="div"
                className="clean-form-error"
              />
            </div>
          )}

          <div className="col-md-6 mb-3">
            <label
              htmlFor="industryDetails.businessCategory"
              className="clean-form-label"
            >
              Business Category <span className="text-danger">*</span>
            </label>
            <SelectInput
              name="industryDetails.businessCategory"
              value={formik.values.industryDetails?.businessCategory || ""}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              options={BUSINESS_CATEGORIES}
              placeholder="Select business category"
              isInvalid={
                formik.touched.industryDetails?.businessCategory &&
                formik.errors.industryDetails?.businessCategory
              }
              isValid={
                formik.touched.industryDetails?.businessCategory &&
                formik.values.industryDetails?.businessCategory &&
                !formik.errors.industryDetails?.businessCategory
              }
              isClearable={true}
              isSearchable={true}
            />
            <ErrorMessage
              name="industryDetails.businessCategory"
              component="div"
              className="clean-form-error"
            />
          </div>

          {/* Custom Business Category Input - Show when "Others" is selected */}
          {businessCategory === "Others" && (
            <div className="col-md-6 mb-3">
              <label
                htmlFor="industryDetails.customBusinessCategory"
                className="clean-form-label"
              >
                Custom Business Category <span className="text-danger">*</span>
              </label>
              <Field
                type="text"
                name="industryDetails.customBusinessCategory"
                className={`clean-form-control ${
                  formik.touched.industryDetails?.customBusinessCategory &&
                  formik.errors.industryDetails?.customBusinessCategory
                    ? "is-invalid"
                    : formik.touched.industryDetails?.customBusinessCategory &&
                      formik.values.industryDetails?.customBusinessCategory
                    ? "is-valid"
                    : ""
                }`}
                placeholder="Enter custom business category"
              />
              <ErrorMessage
                name="industryDetails.customBusinessCategory"
                component="div"
                className="clean-form-error"
              />
            </div>
          )}

        </div>

        {/* Regulatory Documents Upload - Show when any industry type is selected */}
        {industryType && (
          <div className="row">
            <MultipleDocumentUploadLikeAadhaar
              documentType={DOCUMENT_TYPES.REGULATORY_DOC}
              title="Regulatory Documents"
              accept=".pdf,.jpg,.jpeg,.png"
              existingDocuments={existingDocuments}
              onDocumentChange={onDocumentChange}
              maxDocuments={5}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default IndustryDetailsForm;
