import React, { useState, useEffect, useCallback } from "react";
import { useFormik, FormikProvider } from "formik";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { useQueryClient } from "@tanstack/react-query";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { showToast } from "@utils/toastUtils";

import _ from "lodash";
import { extractAutoFillData } from "@utils/profileUtils";
import { setProfileData } from "@store/userSlice";
import { removeDraftFromStorage } from "@utils/draftUtils";
import {
  setKYCFormActive,
  updateKYCFormActivity,
  clearKYCFormSession,
  setupKYCSessionCleanup,
} from "@utils/kycSessionUtils";

import { USER_TYPES, USER_TYPE_LABELS } from "@constants/enum";
import ROUTES from "@constants/routes";

import { useGetProfile, useUpdateProfile } from "@api/profileHooks";
import { useAutosave } from "@hooks/useAutosave";
import { useDraftLoader } from "@hooks/useDraftLoader";
import { useAutosaveOnUnload } from "@hooks/useBeforeUnload";
import AutosaveStatusAlert from "@components/AutosaveStatusAlert";
import autosaveManager from "@utils/autosaveManager";

import BasicDetailsForm from "./forms/BasicDetailsForm";
import BusinessAddressForm from "./forms/BusinessAddressForm";
import CompanyDetailsForm from "./forms/CompanyDetailsForm";
import PanCardDetailsForm from "./forms/PanCardDetailsForm";
import AadhaarCardDetailsForm from "./forms/AadhaarCardDetailsForm";
import BankDetailsForm from "./forms/BankDetailsForm";
import GstDetailsForm from "./forms/GstDetailsForm";
import CinNumberForm from "./forms/CinNumberForm";
import IndustryDetailsForm from "./forms/IndustryDetailsForm";

import {
  basicDetailsSchema,
  businessAddressSchema,
  companyDetailsSchema,
  panCardDetailsSchema,
  aadhaarCardDetailsSchema,
  bankDetailsSchema,
  gstDetailsSchema,
  cinNumberSchema,
  industryDetailsSchema,
} from "./validationSchemas";

import { DOCUMENT_TYPES } from "@api/documentUploadHooks";

const ProfileKYCForm = ({
  initialData = null,
  isEditMode = false,
  onSuccess = null,
  onCancel = null,
  hideHeader = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [isDraftInitialized, setIsDraftInitialized] = useState(false);
  const [isFormInitialized, setIsFormInitialized] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { user, profileData } = useSelector((state) => state.user);
  const [documents, setDocuments] = useState(profileData?.documents || []);
  const userType = initialData?.userType || user?.userType;

  // Fetch profile data to ensure it's loaded
  const {
    data: profileResponse,
    isLoading: profileLoading,
    refetch: refetchProfile,
  } = useGetProfile(user?.id, {
    enabled: !!user?.id,
    onSuccess: (response) => {
      if (response?.data) {
        dispatch(setProfileData(response.data));
        // Extract documents from profile data
        if (response.data.documents) {
          setDocuments(response.data.documents);
        }
      }
    },
    onError: () => {
      // Silent error handling
    },
  });

  // Initialize session tracking on component mount
  useEffect(() => {
    if (!sessionStorage.getItem("session-start-time")) {
      sessionStorage.setItem("session-start-time", Date.now().toString());
    }

    // Mark that user is actively in KYC form
    setKYCFormActive();

    // Set up periodic refresh of the active flag to ensure it stays active
    const refreshInterval = setInterval(() => {
      setKYCFormActive();
    }, 30000); // Refresh every 30 seconds

    // Set up periodic cleanup of expired sessions
    const cleanupInterval = setupKYCSessionCleanup();

    // Handle page unload to clear the flag when user navigates away
    const handleBeforeUnload = () => {
      clearKYCFormSession();
    };

    window.addEventListener("beforeunload", handleBeforeUnload);

    // Cleanup function to clear the flag and intervals when component unmounts
    return () => {
      clearKYCFormSession();
      clearInterval(refreshInterval);
      clearInterval(cleanupInterval);
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);

  // Handle document changes (upload/delete)
  const handleDocumentChange = useCallback(async () => {
    // Update activity timestamp when user uploads documents
    updateKYCFormActivity();

    try {
      // Invalidate and refetch profile query to ensure fresh data
      await queryClient.invalidateQueries(["profile", user?.id]);

      // Also manually refetch
      if (refetchProfile) {
        const result = await refetchProfile();

        // Update documents state immediately if we get new data
        const newDocuments =
          result?.data?.data?.documents || result?.data?.documents;
        if (newDocuments) {
          setDocuments(newDocuments);

          // Also update Redux store
          if (result?.data?.data) {
            dispatch(setProfileData(result.data.data));
          } else if (result?.data) {
            dispatch(setProfileData(result.data));
          }
        }
      }
    } catch {
      // Silent error handling
    }
  }, [refetchProfile, queryClient, user?.id, documents, dispatch]);

  // Update documents when profileData changes
  useEffect(() => {
    if (profileData?.documents) {
      setDocuments(profileData.documents);
    }
  }, [profileData]);

  // Extract auto-fill data from profile - use initialData if in edit mode
  const autoFillData = extractAutoFillData(
    isEditMode ? initialData : profileData
  );

  // Debug: Check localStorage for draft data
  useEffect(() => {
    if (user?.id) {
      const draftKey = `tritrackz-kyc-draft-${user.id}`;
      const storedDraft = localStorage.getItem(draftKey);

      if (storedDraft) {
        try {
          JSON.parse(storedDraft);
        } catch {
          // Silent error handling
        }
      }
    }
  }, [user?.id]);

  // Draft loader hook - moved before formik initialization
  const {
    draftData,
    showDraftPrompt: showDraftPromptFromHook,
    restoreDraft,
    dismissDraft,
    formatDraftAge,
  } = useDraftLoader(
    user?.id,
    () => {
      // Draft data will be used in form initialization
    },
    (draft) => {
      // This callback is called when user clicks "Resume Draft"

      // Force re-initialization with draft data
      if (draft.formData && formik) {
        formik.resetForm({ values: draft.formData });
        setCurrentStep(draft.currentStep || 0);
        setCompletedSteps(draft.completedSteps || []);
      }

      setIsDraftInitialized(true);
    }
  );

  // Success handler for API operations
  const handleSuccess = async (response) => {
    // Remove draft data on successful submission
    if (user?.id) {
      removeDraftFromStorage(user.id);
    }

    // Clear KYC form session since form is completed
    clearKYCFormSession();

    // Fetch updated profile data using React Query hook
    try {
      const updatedProfile = await refetchProfile();
      if (updatedProfile?.data?.data) {
        dispatch(setProfileData(updatedProfile.data.data));
      } else if (response?.data) {
        dispatch(setProfileData(response.data));
      } else {
        const fallbackProfileData = {
          ...user,
          status: 1,
          isComplete: true,
          kycStatus: "Completed",
        };
        dispatch(setProfileData(fallbackProfileData));
      }
    } catch (error) {
      if (response?.data) {
        dispatch(setProfileData(response.data));
      } else {
        const fallbackProfileData = {
          ...user,
          status: 1,
          isComplete: true,
          kycStatus: "Completed",
        };
        dispatch(setProfileData(fallbackProfileData));
      }
    }

    // Handle success based on mode
    if (isEditMode && onSuccess) {
      onSuccess(response);
    } else {
      navigate(ROUTES.DASHBOARD);
    }

    setIsSubmitting(false);
  };

  // Error handler for both submit and update
  const handleError = () => {
    setIsSubmitting(false);
  };

  // API hooks
  const { mutate: submitComprehensiveProfile } = useUpdateProfile({
    onSuccess: handleSuccess,
    onError: handleError,
  });

  // Define steps based on user type
  const getSteps = (userType) => {
    const baseSteps = [
      { id: "basic", title: "Basic Details", component: BasicDetailsForm },
      {
        id: "address",
        title: "Business Address",
        component: BusinessAddressForm,
      },
      { id: "pan", title: "PAN Card Details", component: PanCardDetailsForm },
      {
        id: "aadhaar",
        title: "Aadhaar Card Details",
        component: AadhaarCardDetailsForm,
      },
      { id: "bank", title: "Bank Details", component: BankDetailsForm },
    ];

    // Add company-specific steps for company types
    if (
      [
        USER_TYPES.TRANSPORT_COMPANY,
        USER_TYPES.CARRIER,
        USER_TYPES.SHIPPER_COMPANY,
      ].includes(userType)
    ) {
      baseSteps.splice(2, 0, {
        id: "company",
        title: "Company Details",
        component: CompanyDetailsForm,
      });
      baseSteps.splice(4, 0, {
        id: "gst",
        title: "GST Details",
        component: GstDetailsForm,
      });
      baseSteps.splice(5, 0, {
        id: "cin",
        title: "CIN Number",
        component: CinNumberForm,
      });
    }

    // Add industry details for Shipper Company
    if (userType === USER_TYPES.SHIPPER_COMPANY) {
      baseSteps.push({
        id: "industry",
        title: "Industry Details",
        component: IndustryDetailsForm,
      });
    }

    return baseSteps;
  };

  const steps = getSteps(userType);

  // Check if required documents are uploaded for specific steps
  const checkRequiredDocuments = (stepIndex) => {
    const step = steps[stepIndex];
    const stepId = step?.id;
    const missingDocuments = [];

    // Define required documents for each step
    const requiredDocumentsByStep = {
      gst: [
        {
          type: DOCUMENT_TYPES.GST_CERTIFICATE,
          name: "GST Certificate",
          key: "GstCertificate",
        },
      ],
      pan: [
        { type: DOCUMENT_TYPES.PAN_CARD, name: "PAN Card", key: "PanCard" },
      ],
      aadhaar: [
        {
          type: DOCUMENT_TYPES.AADHAR_CARD,
          name: "Aadhaar Card",
          key: "AadharCard",
        },
      ],
    };

    const requiredDocs = requiredDocumentsByStep[stepId];
    if (!requiredDocs) {
      return { isValid: true, missingDocuments: [] };
    }

    // Check if each required document is uploaded
    requiredDocs.forEach((requiredDoc) => {
      const isUploaded = documents.some(
        (doc) => doc.documentType === requiredDoc.key
      );
      if (!isUploaded) {
        missingDocuments.push(requiredDoc.name);
      }
    });

    return {
      isValid: missingDocuments.length === 0,
      missingDocuments,
    };
  };

  // Get current step validation schema
  const getCurrentStepSchema = (stepIndex) => {
    const step = steps[stepIndex];
    const stepId = step?.id;

    switch (stepId) {
      case "basic":
        return basicDetailsSchema;
      case "company":
        return companyDetailsSchema;
      case "address":
        return businessAddressSchema;
      case "gst":
        return gstDetailsSchema;
      case "cin":
        return cinNumberSchema;
      case "pan":
        return panCardDetailsSchema;
      case "aadhaar":
        return aadhaarCardDetailsSchema;
      case "bank":
        return bankDetailsSchema;
      case "industry":
        return industryDetailsSchema;
      default:
        return null;
    }
  };

  // Get current step field path
  const getCurrentStepFieldPath = (stepIndex) => {
    const step = steps[stepIndex];
    const stepId = step?.id;

    switch (stepId) {
      case "basic":
        return "basicDetails";
      case "company":
        return "companyDetails";
      case "address":
        return "businessAddress";
      case "gst":
        return "gstDetails";
      case "cin":
        return "cinNumber";
      case "pan":
        return "panCardDetails";
      case "aadhaar":
        return "aadhaarCardDetails";
      case "bank":
        return "bankDetails";
      case "industry":
        return "industryDetails";
      default:
        return null;
    }
  };

  // Validate current step
  const validateCurrentStep = async (stepIndex) => {
    const schema = getCurrentStepSchema(stepIndex);
    const fieldPath = getCurrentStepFieldPath(stepIndex);
    const stepInfo = steps[stepIndex];

    if (!schema || !fieldPath) {
      return false;
    }

    try {
      const stepData = formik.values[fieldPath];

      // Only validate if we have step data
      if (!stepData || Object.keys(stepData).length === 0) {
        throw new Error(`${stepInfo?.title || "Step"} data is required`);
      }

      // Determine if GST is required for this user type
      const isGstRequired = [
        USER_TYPES.TRANSPORT_COMPANY,
        USER_TYPES.CARRIER,
        USER_TYPES.SHIPPER_COMPANY,
      ].includes(userType);

      await schema.validate(stepData, {
        abortEarly: false,
        context: { userType, isRequired: isGstRequired },
      });

      // Check required documents for this step
      const documentCheck = checkRequiredDocuments(stepIndex);
      if (!documentCheck.isValid) {
        const missingDocsMessage = `Please upload required documents: ${documentCheck.missingDocuments.join(
          ", "
        )}`;
        throw new Error(missingDocsMessage);
      }

      // Clear any existing errors for this step if validation passes
      const clearedErrors = { ...formik.errors };
      delete clearedErrors[fieldPath];
      formik.setErrors(clearedErrors);

      return true;
    } catch (error) {
      // Set field errors for the current step ONLY
      const stepErrors = {};
      const errorMessages = [];

      if (error.inner && error.inner.length > 0) {
        error.inner.forEach((err) => {
          // Create nested error structure for proper display
          if (!stepErrors[fieldPath]) {
            stepErrors[fieldPath] = {};
          }
          stepErrors[fieldPath][err.path] = err.message;
          errorMessages.push(err.message);
        });
      } else {
        stepErrors[fieldPath] = error.message;
        errorMessages.push(error.message);
      }

      // Set errors for current step (keep existing errors from other steps)
      formik.setErrors({ ...formik.errors, ...stepErrors });

      // Mark ALL fields in this step as touched to show validation errors immediately
      const touchedFields = {};
      const stepDataKeys = Object.keys(formik.values[fieldPath] || {});
      stepDataKeys.forEach((field) => {
        touchedFields[field] = true;
      });

      // Also mark any nested fields as touched
      if (error.inner) {
        error.inner.forEach((err) => {
          touchedFields[err.path] = true;
        });
      }

      formik.setTouched({
        ...formik.touched,
        [fieldPath]: { ...formik.touched[fieldPath], ...touchedFields },
      });

      // Show toast notification for validation errors
      if (errorMessages.length > 0) {
        // Show only the first specific error message
        const firstError = errorMessages[0];
        showToast.error(firstError);
      }

      return false;
    }
  };

  // Check if step is valid and complete
  const isStepValid = (stepIndex) => {
    const schema = getCurrentStepSchema(stepIndex);
    const fieldPath = getCurrentStepFieldPath(stepIndex);

    if (!schema || !fieldPath) return false;

    const stepData = formik.values[fieldPath];

    // Check if step data exists
    if (!stepData || typeof stepData !== "object") return false;

    try {
      // Validate the step data against its schema
      // Determine if GST is required for this user type
      const isGstRequired = [
        USER_TYPES.TRANSPORT_COMPANY,
        USER_TYPES.CARRIER,
        USER_TYPES.SHIPPER_COMPANY,
        6,
      ].includes(userType);

      schema.validateSync(stepData, {
        abortEarly: false,
        context: { userType, isRequired: isGstRequired },
      });

      // Check if all required fields have valid values
      const hasRequiredData = Object.entries(stepData).every(([key, value]) => {
        // Check if field is required in schema
        const fieldSchema = schema.fields[key];
        if (!fieldSchema) return true;

        // If field is required, check if it has a valid value
        const isRequired =
          fieldSchema.tests?.some((test) => test.name === "required") ||
          fieldSchema._exclusive?.required ||
          fieldSchema.spec?.presence === "required";

        if (isRequired) {
          // More strict validation for required fields
          if (value === null || value === undefined || value === "") {
            return false;
          }
          // For strings, also check if it's not just whitespace
          if (typeof value === "string" && value.trim() === "") {
            return false;
          }
          // For arrays, check if they have elements
          if (Array.isArray(value) && value.length === 0) {
            return false;
          }
        }

        return true;
      });

      // Additional check: ensure no validation errors exist for this step
      const stepErrors = formik.errors[fieldPath];
      const hasNoErrors =
        !stepErrors ||
        (typeof stepErrors === "object" &&
          Object.keys(stepErrors).length === 0);

      // Check required documents for this step
      const documentCheck = checkRequiredDocuments(stepIndex);

      return hasRequiredData && hasNoErrors && documentCheck.isValid;
    } catch (error) {
      // If validation fails, the step is not valid
      return false;
    }
  };

  // Remove step from completed steps if it becomes invalid
  const validateAndUpdateCompletedSteps = () => {
    const validCompletedSteps = completedSteps.filter((stepIndex) =>
      isStepValid(stepIndex)
    );
    if (validCompletedSteps.length !== completedSteps.length) {
      setCompletedSteps(validCompletedSteps);
    }
  };

  // Force validation and show all errors for current step
  const forceValidateCurrentStep = async () => {
    const fieldPath = getCurrentStepFieldPath(currentStep);

    if (!fieldPath) {
      return false;
    }

    // Mark all fields in current step as touched first
    const stepData = formik.values[fieldPath] || {};

    const touchedFields = {};
    Object.keys(stepData).forEach((field) => {
      touchedFields[field] = true;
    });

    formik.setTouched({
      ...formik.touched,
      [fieldPath]: { ...formik.touched[fieldPath], ...touchedFields },
    });

    // Then validate
    return await validateCurrentStep(currentStep);
  };

  // Navigation functions
  const handleNext = async () => {
    // Update activity timestamp when user navigates
    updateKYCFormActivity();

    if (currentStep < steps.length - 1) {
      const isValid = await forceValidateCurrentStep();

      if (isValid) {
        try {
          // Save current form data before moving to next step
          await saveFormNow();

          // Add current step to completed steps if not already there
          if (!completedSteps.includes(currentStep)) {
            setCompletedSteps([...completedSteps, currentStep]);
          }

          setCurrentStep(currentStep + 1);
        } catch (error) {
          // Don't proceed to next step if save fails
          return;
        }
      } else {
        // Scroll to first error field if any
        setTimeout(() => {
          const firstErrorField = document.querySelector(
            ".is-invalid, .clean-form-error:not(:empty)"
          );
          if (firstErrorField) {
            firstErrorField.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        }, 100);
      }
    }
  };

  const handlePrevious = () => {
    // Update activity timestamp when user navigates
    updateKYCFormActivity();

    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = async (stepIndex) => {
    // Update activity timestamp when user navigates
    updateKYCFormActivity();

    // Allow clicking on completed steps, current step, or next step if current is valid
    const canNavigate =
      completedSteps.includes(stepIndex) ||
      stepIndex === currentStep ||
      (stepIndex === currentStep + 1 && isStepValid(currentStep));

    if (canNavigate) {
      try {
        // Save current form data before navigating to different step
        if (stepIndex !== currentStep) {
          await saveFormNow();
        }

        // If moving to next step and current step is valid, mark current as completed
        if (
          stepIndex > currentStep &&
          isStepValid(currentStep) &&
          !completedSteps.includes(currentStep)
        ) {
          setCompletedSteps([...completedSteps, currentStep]);
        }
        setCurrentStep(stepIndex);
      } catch (error) {
        return;
      }
    } else {
      // If trying to skip ahead, validate current step first
      if (stepIndex > currentStep) {
        const isCurrentValid = await validateCurrentStep(currentStep);
        if (!isCurrentValid) {
          // Scroll to first error field if any
          setTimeout(() => {
            const firstErrorField = document.querySelector(
              ".is-invalid, .clean-form-error:not(:empty)"
            );
            if (firstErrorField) {
              firstErrorField.scrollIntoView({
                behavior: "smooth",
                block: "center",
              });
            }
          }, 100);

          return;
        }
      }
    }
  };

  // Get initial values based on user type
  const getInitialValues = (userType) => {
    const baseValues = {
      basicDetails: {
        firstName: autoFillData.firstName || user?.firstName || null,
        lastName: autoFillData.lastName || user?.lastName || null,
        email: autoFillData.email || user?.email || null,
        phoneNumber: autoFillData.phoneNumber || user?.phoneNumber || null,
        userType: userType,
      },
      businessAddress: {
        address: autoFillData.address || null,
        city: autoFillData.city || null,
        state: autoFillData.state || null,
        country: autoFillData.country || "India",
        pincode: autoFillData.postalCode || null,
      },
      panCardDetails: {
        nameAsPerPan:
          autoFillData.nameAsPerPan || autoFillData.displayName || null,
        fatherOrHusbandNameInPan: autoFillData.fatherOrHusbandNameInPan || null,
        dateOfBirthInPan: autoFillData.dateOfBirthInPan || null,
        panNumber: autoFillData.panNumber || null,
      },
      aadhaarCardDetails: {
        aadhaarNumber: autoFillData.aadharNumber || null,
        nameAsPerAadhaar:
          autoFillData.nameAsPerAadhaar || autoFillData.displayName || null,
        fatherOrHusbandNameInAadhaar:
          autoFillData.fatherOrHusbandNameInAadhaar || null,
        dateOfBirthInAadhaar: autoFillData.dateOfBirthInAadhaar || null,
        genderInAadhaar: autoFillData.genderInAadhaar || null,
      },
      bankDetails: {
        accountNumber: autoFillData.accountNumber || null,
        accountHolderName: autoFillData.accountHolderName || null,
        bankName: autoFillData.bankName || null,
        ifscCode: autoFillData.ifscCode || null,
      },
    };

    // Add company-specific fields for company types
    if (
      [
        USER_TYPES.TRANSPORT_COMPANY,
        USER_TYPES.CARRIER,
        USER_TYPES.SHIPPER_COMPANY,
      ].includes(userType)
    ) {
      baseValues.companyDetails = {
        companyName: autoFillData.companyName || null,
        brandName: autoFillData.brandName || null,
        companyLogo: autoFillData.companyLogo || null,
        companyContactEmail: autoFillData.companyContactEmail || null,
        companyContactPhone: autoFillData.companyContactPhone || null,
      };

      baseValues.gstDetails = {
        gstNumber:
          autoFillData.gstNumber || autoFillData.gstRegistrationNumber || null,
        legalName: autoFillData.legalName || autoFillData.companyName || null,
        tradeName: autoFillData.tradeName || autoFillData.companyName || null,
        placeOfBusiness:
          autoFillData.placeOfBusiness || autoFillData.address || null,
        registrationDate: autoFillData.gstRegistrationDate || null,
      };

      baseValues.cinNumber = {
        cin: autoFillData.cinNumber || null,
      };

      // Add company-specific PAN fields
      baseValues.panCardDetails.companyName = autoFillData.companyName || null;
      baseValues.panCardDetails.dateOfIncorporation =
        autoFillData.dateOfIncorporation || null;
      baseValues.panCardDetails.businessPanNumber =
        autoFillData.businessPanNumber || null;
    }

    // Add industry details for Shipper Company
    if (userType === USER_TYPES.SHIPPER_COMPANY) {
      baseValues.industryDetails = {
        industryType: autoFillData.industryType || null,
        customIndustryType: autoFillData.customIndustryType || null,
        businessCategory: autoFillData.businessCategory || null,
        customBusinessCategory: autoFillData.customBusinessCategory || null,
      };
    }

    return baseValues;
  };

  // Real-time validation for current step - more conservative approach
  const validateCurrentStepRealTime = async () => {
    // Don't run validation if form is not initialized or no user interaction
    if (!isFormInitialized) return;

    const schema = getCurrentStepSchema(currentStep);
    const fieldPath = getCurrentStepFieldPath(currentStep);

    if (!schema || !fieldPath) return;

    const stepData = formik.values[fieldPath];
    const stepTouched = formik.touched[fieldPath];

    // Only validate if the step has been touched by the user
    if (!stepTouched || Object.keys(stepTouched).length === 0) return;

    // Only validate if we have some data in the step
    if (!stepData || Object.keys(stepData).length === 0) return;

    try {
      // Determine if GST is required for this user type
      const isGstRequired = [
        USER_TYPES.TRANSPORT_COMPANY,
        USER_TYPES.CARRIER,
        USER_TYPES.SHIPPER_COMPANY,
        6,
      ].includes(userType);

      await schema.validate(stepData, {
        abortEarly: false,
        context: { userType, isRequired: isGstRequired },
      });

      // Clear errors for this step if validation passes
      const clearedErrors = { ...formik.errors };
      delete clearedErrors[fieldPath];
      formik.setErrors(clearedErrors);
    } catch (error) {
      // Only set errors for fields that have been touched
      const stepErrors = {};
      if (error.inner) {
        error.inner.forEach((err) => {
          const fieldTouched = formik.touched[fieldPath]?.[err.path];

          // Only show error if field has been touched
          if (fieldTouched) {
            // Create nested error structure for proper display
            if (!stepErrors[fieldPath]) {
              stepErrors[fieldPath] = {};
            }
            stepErrors[fieldPath][err.path] = err.message;
          }
        });
      }

      // Only update errors if we have errors to set
      if (Object.keys(stepErrors).length > 0) {
        formik.setErrors({ ...formik.errors, ...stepErrors });
      }
    }
  };

  // Get initial values - check for draft data first
  const getFormInitialValues = useCallback(() => {
    // If we have draft data, use it; otherwise use default initial values
    if (draftData && draftData.formData) {
      return draftData.formData;
    }

    return getInitialValues(userType);
  }, [draftData, userType, profileData]);

  // Formik setup
  const formik = useFormik({
    initialValues: getFormInitialValues(),
    validationSchema: undefined, // Remove global validation - use step-specific validation
    validationContext: { userType },
    validateOnChange: false, // Disable to prevent premature validation
    validateOnBlur: true, // Keep blur validation for better UX
    enableReinitialize: true,
    onSubmit: async (values) => {
      // Update activity timestamp when user submits form
      updateKYCFormActivity();

      setIsSubmitting(true);

      try {
        // Format dates to ISO string
        const formatDate = (dateString) => {
          if (!dateString) return null;
          return new Date(dateString).toISOString();
        };

        // Structure the payload to match API expectations with nested objects
        const structuredData = {
          // Basic Details
          basicDetails: {
            firstName: values.basicDetails?.firstName || "",
            lastName: values.basicDetails?.lastName || "",
            email: values.basicDetails?.email || "",
            phoneNumber: values.basicDetails?.phoneNumber || "",
            userType: values.basicDetails?.userType || userType,
          },

          // Business Address
          businessAddress: {
            address: values.businessAddress?.address || "",
            city: values.businessAddress?.city || "",
            state: values.businessAddress?.state || "",
            country: values.businessAddress?.country || "",
            postalCode: values.businessAddress?.pincode || "",
          },

          // PAN Card Details
          panCardDetails: {
            nameAsPerPan: values.panCardDetails?.nameAsPerPan || "",
            fatherOrHusbandNameInPan:
              values.panCardDetails?.fatherOrHusbandNameInPan || "",
            dateOfBirthInPan: formatDate(
              values.panCardDetails?.dateOfBirthInPan
            ),
            panNumber: values.panCardDetails?.panNumber || "",
          },

          // Aadhaar Card Details
          aadhaarCardDetails: {
            aadharNumber: values.aadhaarCardDetails?.aadhaarNumber || "",
            nameAsPerAadhaar: values.aadhaarCardDetails?.nameAsPerAadhaar || "",
            fatherOrHusbandNameInAadhaar:
              values.aadhaarCardDetails?.fatherOrHusbandNameInAadhaar || "",
            dateOfBirthInAadhaar: formatDate(
              values.aadhaarCardDetails?.dateOfBirthInAadhaar
            ),
            genderInAadhaar: values.aadhaarCardDetails?.genderInAadhaar || "",
          },

          // Bank Details
          bankDetails: {
            accountNumber: values.bankDetails?.accountNumber || "",
            accountHolderName: values.bankDetails?.accountHolderName || "",
            bankName: values.bankDetails?.bankName || "",
            ifscCode: values.bankDetails?.ifscCode || "",
          },
        };

        // Add company-specific fields for company types
        if (
          [
            USER_TYPES.TRANSPORT_COMPANY,
            USER_TYPES.CARRIER,
            USER_TYPES.SHIPPER_COMPANY,
          ].includes(userType)
        ) {
          // Company Details
          structuredData.companyDetails = {
            companyName: values.companyDetails?.companyName || "",
            brandName: values.companyDetails?.brandName || "",
            companyLogo: values.companyDetails?.companyLogo || "",
            companyContactEmail:
              values.companyDetails?.companyContactEmail || "",
            companyContactPhone:
              values.companyDetails?.companyContactPhone || "",
          };

          // GST Details
          structuredData.gstDetails = {
            gstNumber: values.gstDetails?.gstNumber || "",
            legalName: values.gstDetails?.legalName || "",
            tradeName: values.gstDetails?.tradeName || "",
            placeOfBusiness: values.gstDetails?.placeOfBusiness || "",
            registrationDate: formatDate(values.gstDetails?.registrationDate),
          };

          // Add company-specific PAN fields
          structuredData.panCardDetails = {
            ...structuredData.panCardDetails,
            companyName: values.panCardDetails?.companyName || "",
            dateOfIncorporation: formatDate(
              values.panCardDetails?.dateOfIncorporation
            ),
            businessPanNumber: values.panCardDetails?.businessPanNumber || "",
          };

          // CIN Number
          structuredData.cinNumber = {
            cin: values.cinNumber?.cin || "",
          };
        }

        // Add industry details for Shipper Company
        if (userType === USER_TYPES.SHIPPER_COMPANY) {
          structuredData.industryDetails = {
            industryType: values.industryDetails?.industryType || "",
            customIndustryType:
              values.industryDetails?.customIndustryType || "",
            businessCategory: values.industryDetails?.businessCategory || "",
            customBusinessCategory:
              values.industryDetails?.customBusinessCategory || "",
          };
        }

        // Remove undefined fields
        const cleanedValues = JSON.parse(
          JSON.stringify(structuredData, (_, value) => {
            return value === undefined ? null : value;
          })
        );

        // Determine status based on current profile status and edit mode
        let submissionStatus;

        if (isEditMode && profileData?.status === 3) {
          // If editing an approved profile (status 3), keep it as approved
          submissionStatus = 3;
        } else if (isEditMode && profileData?.status === 4) {
          // If editing a rejected profile (status 4), resubmit for approval
          submissionStatus = 1;
        } else {
          // New submission or editing incomplete profile - mark as completed for admin approval
          submissionStatus = 1;
        }

        // Submit comprehensive profile with appropriate status
        const finalProfileData = {
          ...cleanedValues,
          status: submissionStatus,
        };

        // Add profile ID if editing existing profile
        if (profileData?.id) {
          finalProfileData.id = profileData.id;
        }

        if (!user?.id) {
          throw new Error("User ID is missing");
        }

        if (!submitComprehensiveProfile) {
          throw new Error(
            "submitComprehensiveProfile function is not available"
          );
        }

        submitComprehensiveProfile({
          userId: user.id,
          profileData: finalProfileData,
        });
      } catch {
        setIsSubmitting(false);
      }
    },
  });

  // Autosave hook with profile status management
  const {
    isAutoSaving,
    lastSaveTime,
    timeSinceLastSave,
    saveError,
    saveOnUnload,
    hasUnsavedChanges,
    saveNow: saveFormNow,
  } = useAutosave({
    userId: user?.id,
    formData: formik.values,
    currentStep,
    completedSteps,
    debounceMs: 60000, // 1 minute
    enabled: !isSubmitting,
    profileData: profileData, // Pass profile data for status checking
    isEditMode: isEditMode, // Pass edit mode flag
    onSuccess: () => {},
    onError: () => {},
  });

  // Auto-save on page unload
  useAutosaveOnUnload(saveOnUnload, hasUnsavedChanges, !isSubmitting);

  // Initialize form state from draft data
  useEffect(() => {
    if (draftData && !isDraftInitialized) {
      // Set current step from draft
      if (typeof draftData.currentStep === "number") {
        setCurrentStep(draftData.currentStep);
      }

      // Set completed steps from draft
      if (Array.isArray(draftData.completedSteps)) {
        setCompletedSteps(draftData.completedSteps);
      }

      // Update formik values if we have form data
      if (draftData.formData && formik) {
        formik.resetForm({ values: draftData.formData });
      }

      // Mark as initialized to prevent re-initialization
      setIsDraftInitialized(true);
    }
  }, [draftData, isDraftInitialized, formik]);

  // Register with global autosave manager for logout scenarios
  useEffect(() => {
    if (user?.id) {
      const autosaveKey = `kyc-form-${user.id}`;

      autosaveManager.register(autosaveKey, () => {
        if (saveOnUnload) {
          saveOnUnload();
        }
      });

      return () => {
        autosaveManager.unregister(autosaveKey);
      };
    }
  }, [user?.id, saveOnUnload]);

  // Set form as initialized after first render
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsFormInitialized(true);
    }, 1000); // Give form time to initialize with data

    return () => clearTimeout(timer);
  }, []);

  // Real-time validation effect - only run when user stops typing and has interacted
  useEffect(() => {
    // Don't run validation on initial load or if no user interaction
    const hasInteracted = Object.keys(formik.touched).length > 0;
    if (!hasInteracted || !isFormInitialized) return;

    const timeoutId = setTimeout(() => {
      validateCurrentStepRealTime();
      // Also validate and update completed steps
      validateAndUpdateCompletedSteps();
    }, 1000); // Longer debounce to prevent interference with typing

    return () => clearTimeout(timeoutId);
  }, [formik.values, currentStep, isFormInitialized]);

  // Additional effect to handle form errors and update step states
  useEffect(() => {
    // Force re-render when errors change to update step visual states
    const hasErrors = Object.keys(formik.errors).length > 0;
    if (hasErrors) {
      // Trigger a state update to refresh step indicators
      setCompletedSteps((prev) => [...prev]);
    }
  }, [formik.errors]);

  // Check if user type is valid
  if (!userType || !Object.values(USER_TYPES).includes(userType)) {
    return (
      <div className="alert alert-danger">
        <h5>Invalid User Type</h5>
        <p>Unable to determine your user type. Please contact support.</p>
      </div>
    );
  }

  const CurrentStepComponent = steps[currentStep]?.component;

  // Show loading if profile data is still being fetched
  if (profileLoading && !profileData) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ minHeight: "400px" }}
      >
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading profile data...</span>
          </div>
          <p className="mt-3 text-muted">Loading your profile information...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="clean-profile-kyc-container">
      {/* Draft Restore Prompt */}
      {showDraftPromptFromHook && draftData && (
        <div
          className="modal fade show d-block"
          style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
        >
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  <i className="fas fa-save me-2 text-primary"></i>
                  Resume Previous Session
                </h5>
              </div>
              <div className="modal-body">
                <p className="mb-3">
                  We found a saved draft of your KYC form from{" "}
                  <strong>{formatDraftAge()}</strong>. Would you like to
                  continue where you left off?
                </p>
                <div className="d-flex align-items-center text-muted small">
                  <i className="fas fa-info-circle me-2"></i>
                  Step {(draftData.currentStep || 0) + 1} of {steps.length} •{" "}
                  {draftData.completedSteps?.length || 0} steps completed
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-outline-secondary"
                  onClick={dismissDraft}
                >
                  Start Fresh
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={restoreDraft}
                >
                  <i className="fas fa-undo me-2"></i>
                  Resume Draft
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Clean Header */}
      {!hideHeader && (
        <div className="clean-kyc-header">
          <div className="container-fluid">
            <div className="row">
              <div className="col-12">
                <div className="d-flex justify-content-between align-items-start mb-3">
                  <div>
                    <h1 className="clean-kyc-title mb-2">
                      Complete Your Profile & KYC
                    </h1>
                    <p className="clean-kyc-subtitle mb-0">
                      Please fill in all the required information to complete
                      your profile and KYC verification. You are registering as:{" "}
                      <strong>{USER_TYPE_LABELS[userType]}</strong>
                    </p>
                  </div>

                  {/* Compact Autosave Status */}
                  <AutosaveStatusAlert
                    isAutoSaving={isAutoSaving}
                    lastSaveTime={lastSaveTime}
                    timeSinceLastSave={timeSinceLastSave}
                    saveError={saveError}
                    hasUnsavedChanges={hasUnsavedChanges}
                  />
                </div>

                <div className="clean-progress-indicator">
                  <div
                    className="progress"
                    style={{
                      height: "4px",
                      backgroundColor: "rgba(0,0,0,0.1)",
                    }}
                  >
                    <div
                      className="progress-bar bg-success"
                      role="progressbar"
                      style={{
                        width: `${
                          (completedSteps.filter((stepIndex) =>
                            isStepValid(stepIndex)
                          ).length /
                            steps.length) *
                          100
                        }%`,
                        transition: "width 0.3s ease",
                      }}
                      aria-valuenow={
                        completedSteps.filter((stepIndex) =>
                          isStepValid(stepIndex)
                        ).length
                      }
                      aria-valuemin="0"
                      aria-valuemax={steps.length}
                    ></div>
                  </div>
                  <div className="d-flex justify-content-between align-items-center mt-1">
                    <small className="text-muted">
                      {
                        completedSteps.filter((stepIndex) =>
                          isStepValid(stepIndex)
                        ).length
                      }{" "}
                      of {steps.length} steps completed
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Clean Content Layout */}
      <div className="clean-kyc-content">
        <div className="container-fluid">
          <div className="row">
            {/* Left Side - Clean Stepper */}
            <div className="col-12 col-lg-2">
              <div className="clean-stepper-nav">
                <div className="clean-stepper-list">
                  {steps.map((step, index) => {
                    const isCompleted = completedSteps.includes(index);
                    const isActive = index === currentStep;
                    const isValid = isStepValid(index);
                    const isClickable =
                      isCompleted ||
                      isActive ||
                      (index === currentStep + 1 && isStepValid(currentStep));

                    // Determine step state for visual display
                    const showCompleted = isCompleted && isValid;
                    const showValid = isValid && !isActive && !isCompleted;
                    const showInvalid =
                      !isValid &&
                      !isActive &&
                      (formik.touched[getCurrentStepFieldPath(index)] ||
                        completedSteps.includes(index));

                    return (
                      <div
                        key={step.id}
                        className={`clean-stepper-item ${
                          isActive ? "active" : ""
                        } ${showCompleted ? "completed" : ""} ${
                          showValid ? "valid" : ""
                        } ${showInvalid ? "invalid" : ""} ${
                          isClickable ? "clickable" : ""
                        }`}
                        onClick={() => handleStepClick(index)}
                      >
                        <div className="clean-stepper-number">
                          {showCompleted ? "✓" : showInvalid ? "!" : index + 1}
                        </div>
                        <div className="clean-stepper-text">
                          <div className="clean-stepper-label">
                            Step {index + 1}
                          </div>
                          <div className="clean-stepper-title">
                            {step.title}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Right Side - Clean Form */}
            <div className="col-12 col-lg-10">
              <div className="clean-form-container">
                <FormikProvider value={formik}>
                  <form onSubmit={formik.handleSubmit} className="clean-form">
                    {/* Current Step Form */}
                    {CurrentStepComponent && (
                      <CurrentStepComponent
                        formik={formik}
                        userType={userType}
                        existingDocuments={documents}
                        onDocumentChange={handleDocumentChange}
                      />
                    )}

                    {/* Clean Navigation - Right Aligned */}
                    <div className="clean-form-navigation">
                      <div className="d-flex justify-content-end align-items-center gap-3">
                        <div className="clean-step-indicator">
                          Step {currentStep + 1} of {steps.length}
                        </div>

                        <div className="d-flex align-items-center gap-2">
                          <button
                            type="button"
                            className="btn btn-outline-secondary clean-nav-btn d-flex align-items-center"
                            onClick={handlePrevious}
                            disabled={currentStep === 0}
                          >
                            <FaChevronLeft className="me-1" />
                            Previous
                          </button>

                          {currentStep === steps.length - 1 ? (
                            <button
                              type="button"
                              className="btn btn-primary clean-submit-btn"
                              disabled={isSubmitting}
                              onClick={async () => {
                                // Validate current step first
                                const isCurrentStepValid =
                                  await forceValidateCurrentStep();
                                if (!isCurrentStepValid) {
                                  // Toast is already shown by forceValidateCurrentStep, no need for duplicate
                                  return;
                                }

                                // Then submit the form
                                formik.handleSubmit();
                              }}
                            >
                              {isSubmitting ? (
                                <>
                                  <span
                                    className="spinner-border spinner-border-sm me-2"
                                    role="status"
                                    aria-hidden="true"
                                  ></span>
                                  Submitting...
                                </>
                              ) : (
                                "Complete Profile & KYC"
                              )}
                            </button>
                          ) : (
                            <button
                              type="button"
                              className="btn btn-primary clean-nav-btn d-flex align-items-center"
                              onClick={handleNext}
                            >
                              Next
                              <FaChevronRight className="ms-1" />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>
                  </form>
                </FormikProvider>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileKYCForm;
