import React from "react";
import { Field, ErrorMessage } from "formik";

const BankDetailsForm = ({ formik, existingDocuments, onDocumentChange }) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">Bank Details</h3>
        <p className="text-muted mb-0">
          Please provide your bank account information
        </p>
      </div>
      <div className="row">
        <div className="col-md-6 mb-3">
          <label
            htmlFor="bankDetails.accountNumber"
            className="form-label fw-medium"
          >
            Account Number <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="bankDetails.accountNumber"
            className={`form-control ${
              formik.touched.bankDetails?.accountNumber &&
              formik.errors.bankDetails?.accountNumber
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter account number"
          />
          <ErrorMessage
            name="bankDetails.accountNumber"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="bankDetails.accountHolderName"
            className="form-label fw-medium"
          >
            Account Holder Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="bankDetails.accountHolderName"
            className={`form-control ${
              formik.touched.bankDetails?.accountHolderName &&
              formik.errors.bankDetails?.accountHolderName
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter account holder name"
          />
          <ErrorMessage
            name="bankDetails.accountHolderName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="bankDetails.bankName"
            className="form-label fw-medium"
          >
            Bank Name <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="bankDetails.bankName"
            className={`form-control ${
              formik.touched.bankDetails?.bankName &&
              formik.errors.bankDetails?.bankName
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter bank name"
          />
          <ErrorMessage
            name="bankDetails.bankName"
            component="div"
            className="invalid-feedback"
          />
        </div>

        <div className="col-md-6 mb-3">
          <label
            htmlFor="bankDetails.ifscCode"
            className="form-label fw-medium"
          >
            IFSC Code <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="bankDetails.ifscCode"
            className={`form-control text-uppercase ${
              formik.touched.bankDetails?.ifscCode &&
              formik.errors.bankDetails?.ifscCode
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter IFSC code"
          />
          <ErrorMessage
            name="bankDetails.ifscCode"
            component="div"
            className="invalid-feedback"
          />
        </div>
      </div>
    </div>
  );
};

export default BankDetailsForm;
