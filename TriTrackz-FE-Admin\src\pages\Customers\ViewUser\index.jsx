import React, { useState, useCallback, useMemo, memo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useGetProfile } from "@api/profileHooks";
import {
  useDocumentDownload,
  getRequiredDocumentsByUserType,
} from "@api/documentUploadHooks";
import { useApproveUser, useRejectUser } from "@api/usersHooks";
import { USER_TYPE_LABELS, USER_TYPES } from "@constants/enum";
import KYCStatusAlert from "@components/KYCStatusAlert";
import Avatar from "@components/Common/Avatar";
import toast from "react-hot-toast";
import {
  FaUser,
  FaBuilding,
  FaMapMarkerAlt,
  FaSpinner,
  FaExclamationTriangle,
  FaIdCard,
  FaIndustry,
  FaEye,
  FaEyeSlash,
  FaArrowLeft,
  FaFileAlt,
  FaDownload,
  FaCheckCircle,
  FaFilePdf,
  FaFileImage,
  FaCheck,
  FaTimes,
  FaChartLine,
} from "react-icons/fa";

// Import the ProfileKYCForm for editing
import { ProfileKYCForm } from "@components/ProfileKYC";

// Constants moved outside component to prevent recreation
const DOCUMENT_TYPE_MAP = {
  GstCertificate: 0,
  PanCard: 2,
  AadharCard: 4,
  TradeLicense: 1,
  BusinessLicense: 3,
  DrivingLicense: 5,
};

const FILE_ICON_MAP = {
  pdf: { icon: FaFilePdf, color: "#dc3545" },
  jpg: { icon: FaFileImage, color: "#28a745" },
  jpeg: { icon: FaFileImage, color: "#28a745" },
  png: { icon: FaFileImage, color: "#28a745" },
  gif: { icon: FaFileImage, color: "#28a745" },
  bmp: { icon: FaFileImage, color: "#28a745" },
  webp: { icon: FaFileImage, color: "#28a745" },
  doc: { icon: FaFileAlt, color: "#0d6efd" },
  docx: { icon: FaFileAlt, color: "#0d6efd" },
  xls: { icon: FaFileAlt, color: "#198754" },
  xlsx: { icon: FaFileAlt, color: "#198754" },
  txt: { icon: FaFileAlt, color: "#6f42c1" },
  default: { icon: FaFileAlt, color: "#6c757d" },
};

const STATUS_LABELS = {
  0: "Incomplete",
  1: "Complete",
  2: "Under Review",
  3: "Approved",
  4: "Rejected",
};

const STATUS_BADGE_CLASSES = {
  0: "bg-secondary",
  1: "bg-info",
  2: "bg-warning",
  3: "bg-success",
  4: "bg-danger",
};

const COMPANY_USER_TYPES = [
  USER_TYPES.TRANSPORT_COMPANY,
  USER_TYPES.CARRIER,
  USER_TYPES.SHIPPER_COMPANY,
];

// Helper functions moved outside component to prevent recreation
const getFileIconAndColor = (fileName) => {
  if (!fileName) return FILE_ICON_MAP.default;

  const extension = fileName.toLowerCase().split(".").pop();
  return FILE_ICON_MAP[extension] || FILE_ICON_MAP.default;
};

const getStatusLabel = (status) => STATUS_LABELS[status] || "Unknown";

const getStatusBadgeClass = (status) =>
  STATUS_BADGE_CLASSES[status] || "bg-secondary";

const isCompanyType = (userType) => COMPANY_USER_TYPES.includes(userType);

const isShipperCompany = (userType) => userType === USER_TYPES.SHIPPER_COMPANY;

const formatDate = (dateString) => {
  if (!dateString) return "N/A";
  return new Date(dateString).toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

// Memoized Document Row Component
const DocumentRow = memo(
  ({ document, onDownload, isDownloading, userType }) => {
    const requiredDocInfo = useMemo(
      () =>
        getRequiredDocumentsByUserType(userType).find(
          (reqDoc) => reqDoc.key === document.documentType
        ),
      [document.documentType, userType]
    );

    const documentName = useMemo(
      () =>
        requiredDocInfo?.name ||
        document.documentType.replace(/([A-Z])/g, " $1").trim(),
      [requiredDocInfo?.name, document.documentType]
    );

    const { icon: FileIcon } = useMemo(
      () => getFileIconAndColor(document.fileName),
      [document.fileName]
    );

    const handleDownload = useCallback(() => {
      onDownload(document);
    }, [document, onDownload]);

    return (
      <div
        key={document.documentId}
        className="d-flex align-items-center p-3 border rounded mb-2"
      >
        {/* Document Icon */}
        <div className="flex-shrink-0 me-3 d-flex align-items-center justify-content-center bg-primary bg-opacity-10 border border-primary border-opacity-25 rounded">
          <FileIcon size={20} className="   " title={document.fileName} />
        </div>

        {/* Document Name */}
        <div className="flex-grow-1 me-3">
          <h6 className="mb-0">{documentName}</h6>
        </div>

        {/* Document Status */}
        <div className="me-3">
          <span className="badge bg-success">
            <FaCheckCircle className="me-1" size={10} />
            Uploaded Successfully
          </span>
        </div>

        {/* Document Actions */}
        <div>
          <button
            type="button"
            className="btn btn-outline-primary btn-sm"
            onClick={handleDownload}
            disabled={isDownloading}
          >
            {isDownloading ? (
              <>
                <FaSpinner className="fa-spin me-1" size={12} />
                Downloading...
              </>
            ) : (
              <>
                <FaDownload className="me-1" size={12} />
                Download
              </>
            )}
          </button>
        </div>
      </div>
    );
  }
);

DocumentRow.displayName = "DocumentRow";

// Memoized Field Display Component
const FieldDisplay = memo(
  ({
    label,
    value,
    type = "text",
    showToggle = false,
    isVisible = true,
    onToggle,
  }) => {
    const displayValue = useMemo(() => {
      if (!value) return "N/A";
      if (type === "date") return formatDate(value);
      if (showToggle && !isVisible) {
        return "*".repeat(Math.min(value.length, 12));
      }
      return value;
    }, [value, type, showToggle, isVisible]);

    const handleToggle = useCallback(() => {
      if (onToggle) onToggle();
    }, [onToggle]);

    return (
      <div className="mb-3 p-3 border rounded">
        <label className="form-label text-uppercase fw-bold text-muted small mb-1">
          {label}
        </label>
        <div className="d-flex align-items-center">
          <span className="   fw-semibold">{displayValue}</span>
          {showToggle && (
            <button
              type="button"
              className="btn btn-link p-0 ms-2"
              onClick={handleToggle}
              title={isVisible ? "Hide" : "Show"}
            >
              {isVisible ? <FaEyeSlash size={14} /> : <FaEye size={14} />}
            </button>
          )}
        </div>
      </div>
    );
  }
);

FieldDisplay.displayName = "FieldDisplay";

// Memoized Approval Modal Component
const ApprovalModal = memo(
  ({ isOpen, onClose, onApprove, notes, setNotes, isLoading }) => {
    const handleSubmit = useCallback(
      (e) => {
        e.preventDefault();
        onApprove();
      },
      [onApprove]
    );

    const handleNotesChange = useCallback(
      (e) => {
        setNotes(e.target.value);
      },
      [setNotes]
    );

    if (!isOpen) return null;

    return (
      <div className="modal show d-block bg-dark bg-opacity-50">
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Approve User</h5>
              <button
                type="button"
                className="btn-close"
                onClick={onClose}
              ></button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label">Approval Notes *</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    value={notes}
                    onChange={handleNotesChange}
                    placeholder="Enter notes for approval..."
                    required
                  />
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-success"
                  disabled={isLoading || !notes.trim()}
                >
                  {isLoading ? (
                    <>
                      <FaSpinner className="fa-spin me-1" />
                      Approving...
                    </>
                  ) : (
                    <>
                      <FaCheck className="me-1" />
                      Approve User
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }
);

ApprovalModal.displayName = "ApprovalModal";

// Memoized Rejection Modal Component
const RejectionModal = memo(
  ({
    isOpen,
    onClose,
    onReject,
    reason,
    setReason,
    notes,
    setNotes,
    isLoading,
  }) => {
    const handleSubmit = useCallback(
      (e) => {
        e.preventDefault();
        onReject();
      },
      [onReject]
    );

    const handleReasonChange = useCallback(
      (e) => {
        setReason(e.target.value);
      },
      [setReason]
    );

    const handleNotesChange = useCallback(
      (e) => {
        setNotes(e.target.value);
      },
      [setNotes]
    );

    if (!isOpen) return null;

    return (
      <div className="modal show d-block bg-dark bg-opacity-50">
        <div className="modal-dialog">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Reject User</h5>
              <button
                type="button"
                className="btn-close"
                onClick={onClose}
              ></button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className="modal-body">
                <div className="mb-3">
                  <label className="form-label">Rejection Reason *</label>
                  <input
                    type="text"
                    className="form-control"
                    value={reason}
                    onChange={handleReasonChange}
                    placeholder="Enter reason for rejection..."
                    required
                  />
                </div>
                <div className="mb-3">
                  <label className="form-label">Additional Notes</label>
                  <textarea
                    className="form-control"
                    rows="3"
                    value={notes}
                    onChange={handleNotesChange}
                    placeholder="Enter additional notes (optional)..."
                  />
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={onClose}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="btn btn-danger"
                  disabled={isLoading || !reason.trim()}
                >
                  {isLoading ? (
                    <>
                      <FaSpinner className="fa-spin me-1" />
                      Rejecting...
                    </>
                  ) : (
                    <>
                      <FaTimes className="me-1" />
                      Reject User
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    );
  }
);

RejectionModal.displayName = "RejectionModal";

const ViewUser = memo(() => {
  const { userId } = useParams();
  const navigate = useNavigate();
  const [isEditing, setIsEditing] = useState(false);
  const [showNumbers, setShowNumbers] = useState({
    pan: false,
    aadhaar: false,
    account: false,
  });
  const [downloadLoading, setDownloadLoading] = useState({});
  const [showApproveModal, setShowApproveModal] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [approveNotes, setApproveNotes] = useState("");
  const [rejectReason, setRejectReason] = useState("");
  const [rejectNotes, setRejectNotes] = useState("");

  // Fetch profile data using ComprehensiveProfile endpoint
  const {
    data: profileResponse,
    isLoading,
    error,
    refetch,
  } = useGetProfile(userId);

  // Memoized derived values
  const profileData = useMemo(
    () => profileResponse?.data,
    [profileResponse?.data]
  );
  const userType = useMemo(
    () => profileData?.userType,
    [profileData?.userType]
  );
  const userDocuments = useMemo(
    () => profileData?.documents || [],
    [profileData?.documents]
  );

  // Memoized computed values - moved here to ensure hooks are called before any returns
  const isUserCompanyType = useMemo(() => isCompanyType(userType), [userType]);
  const isUserShipperCompany = useMemo(
    () => isShipperCompany(userType),
    [userType]
  );

  // Memoized callbacks for mutations
  const handleApproveSuccess = useCallback(() => {
    toast.success("User approved successfully!");
    setShowApproveModal(false);
    setApproveNotes("");
    refetch();
  }, [refetch]);

  const handleApproveError = useCallback((error) => {
    toast.error(error?.response?.data?.message || "Failed to approve user");
  }, []);

  const handleRejectSuccess = useCallback(() => {
    toast.success("User rejected successfully!");
    setShowRejectModal(false);
    setRejectReason("");
    setRejectNotes("");
    refetch();
  }, [refetch]);

  const handleRejectError = useCallback((error) => {
    toast.error(error?.response?.data?.message || "Failed to reject user");
  }, []);

  // Approve and Reject mutations
  const approveUserMutation = useApproveUser({
    onSuccess: handleApproveSuccess,
    onError: handleApproveError,
  });

  const rejectUserMutation = useRejectUser({
    onSuccess: handleRejectSuccess,
    onError: handleRejectError,
  });

  // Document download hook
  const documentDownloadMutation = useDocumentDownload();

  // Memoized toggle functions for show/hide numbers
  const togglePanVisibility = useCallback(() => {
    setShowNumbers((prev) => ({ ...prev, pan: !prev.pan }));
  }, []);

  const toggleAadhaarVisibility = useCallback(() => {
    setShowNumbers((prev) => ({ ...prev, aadhaar: !prev.aadhaar }));
  }, []);

  const toggleAccountVisibility = useCallback(() => {
    setShowNumbers((prev) => ({ ...prev, account: !prev.account }));
  }, []);

  // Memoized event handlers - moved here to ensure all hooks are called before any returns
  const handleEditClick = useCallback(() => {
    setIsEditing(true);
  }, []);

  const handleCancelEdit = useCallback(() => {
    setIsEditing(false);
  }, []);

  const handleSaveSuccess = useCallback(() => {
    setIsEditing(false);
    refetch();
  }, [refetch]);

  // Memoized approve and reject handlers
  const handleApproveUser = useCallback(() => {
    if (!approveNotes.trim()) {
      toast.error("Please provide notes for approval");
      return;
    }
    approveUserMutation.mutate({
      userId,
      notes: approveNotes.trim(),
    });
  }, [approveNotes, approveUserMutation, userId]);

  const handleRejectUser = useCallback(() => {
    if (!rejectReason.trim()) {
      toast.error("Please provide a reason for rejection");
      return;
    }
    rejectUserMutation.mutate({
      userId,
      reason: rejectReason.trim(),
      notes: rejectNotes.trim(),
    });
  }, [rejectReason, rejectNotes, rejectUserMutation, userId]);

  const handleBackClick = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  // Memoized modal handlers
  const handleCloseApproveModal = useCallback(() => {
    setShowApproveModal(false);
  }, []);

  const handleCloseRejectModal = useCallback(() => {
    setShowRejectModal(false);
  }, []);

  const handleSetApproveNotes = useCallback((notes) => {
    setApproveNotes(notes);
  }, []);

  const handleSetRejectReason = useCallback((reason) => {
    setRejectReason(reason);
  }, []);

  const handleSetRejectNotes = useCallback((notes) => {
    setRejectNotes(notes);
  }, []);

  // Memoized document download handler
  const handleDocumentDownload = useCallback(
    async (document) => {
      const documentKey = `${document.documentType}-${document.fileName}`;
      setDownloadLoading((prev) => ({ ...prev, [documentKey]: true }));

      try {
        const documentTypeId = DOCUMENT_TYPE_MAP[document.documentType];

        if (documentTypeId === undefined) {
          throw new Error(`Unknown document type: ${document.documentType}`);
        }

        const result = await documentDownloadMutation.mutateAsync({
          documentId: document.documentId,
          documentType: documentTypeId,
        });

        const fileName =
          document.fileName?.replace(/[^a-zA-Z0-9.\-_]/g, "_") ||
          "downloaded-file";

        // Create download link
        const url = window.URL.createObjectURL(result.blob);
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("Error downloading document:", error);
        toast.error(`Failed to download document: ${error.message}`);
      } finally {
        setDownloadLoading((prev) => ({ ...prev, [documentKey]: false }));
      }
    },
    [documentDownloadMutation]
  );

  // Show loading state
  if (isLoading) {
    return (
      <div className="container-fluid py-4">
        <div className="row">
          <div className="col-12 text-center">
            <FaSpinner className="fa-spin me-2" size={20} />
            <span>Loading profile data...</span>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="container-fluid py-4">
        <div className="row">
          <div className="col-12">
            <div className="alert alert-danger d-flex align-items-center">
              <FaExclamationTriangle className="me-2" size={20} />
              <div>
                <strong>Error loading profile data:</strong>
                <br />
                {error?.response?.data?.message ||
                  error?.message ||
                  "Failed to load profile data"}
                <br />
                <button
                  className="btn btn-sm btn-outline-danger mt-2"
                  onClick={() => refetch()}
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show message if no profile data
  if (!profileData) {
    return (
      <div className="container-fluid py-4">
        <div className="row">
          <div className="col-12">
            <div className="alert alert-warning d-flex align-items-center">
              <FaExclamationTriangle className="me-2" size={20} />
              <div>
                <strong>No profile data found</strong>
                <br />
                Please complete your profile registration first.
                <br />
                <button
                  className="btn btn-sm btn-outline-warning mt-2"
                  onClick={() => refetch()}
                >
                  Refresh
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center">
          <FaSpinner className="fa-spin     mb-3" size={32} />
          <p className="text-muted">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center">
          <FaExclamationTriangle className="text-warning mb-3" size={32} />
          <p className="text-muted">Failed to load profile data</p>
          <button className="btn btn-primary btn-sm" onClick={() => refetch()}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  // If in edit mode, show the ProfileKYCForm
  if (isEditing) {
    return (
      <div className="view-user-page">
        <div className="container-fluid py-4">
          <div className="row mb-4">
            <div className="col-12">
              <div className="d-flex align-items-center mb-3">
                <button
                  className="btn btn-link p-0 me-3 text-decoration-none text-body"
                  onClick={handleCancelEdit}
                >
                  <FaArrowLeft size={16} />
                </button>
                <div>
                  <h4 className="mb-1">Edit Profile</h4>
                  <p className="text-muted mb-0">
                    Update your account information
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="row">
            <div className="col-12">
              <ProfileKYCForm
                initialData={profileData}
                isEditMode={true}
                onSuccess={handleSaveSuccess}
                onCancel={handleCancelEdit}
                hideHeader={true}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container-fluid py-3 px-4">
      {/* Header */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div className="d-flex align-items-center">
              <button
                className="btn btn-link p-0 me-3 text-decoration-none text-body"
                onClick={handleBackClick}
              >
                <FaArrowLeft size={16} />
              </button>
              <div>
                <h5 className="mb-1 fw-semibold">User Profile</h5>
                <p className="text-muted mb-0 small">
                  View user account information and details
                </p>
              </div>
            </div>
            <div className="d-flex gap-2">
              {/* Show Approve/Reject buttons only for Complete (1) or Under Review (2) status */}
              {(profileData?.status === 1 || profileData?.status === 2) && (
                <>
                  <button
                    className="btn btn-success btn-sm"
                    onClick={() => setShowApproveModal(true)}
                    disabled={isLoading || approveUserMutation.isPending}
                  >
                    {approveUserMutation.isPending ? (
                      <>
                        <FaSpinner className="fa-spin me-1" size={12} />
                        Approving...
                      </>
                    ) : (
                      <>
                        <FaCheck className="me-1" size={12} />
                        Approve
                      </>
                    )}
                  </button>
                  <button
                    className="btn btn-danger btn-sm"
                    onClick={() => setShowRejectModal(true)}
                    disabled={isLoading || rejectUserMutation.isPending}
                  >
                    {rejectUserMutation.isPending ? (
                      <>
                        <FaSpinner className="fa-spin me-1" size={12} />
                        Rejecting...
                      </>
                    ) : (
                      <>
                        <FaTimes className="me-1" size={12} />
                        Reject
                      </>
                    )}
                  </button>
                </>
              )}
              <button
                className="btn btn-primary btn-sm"
                onClick={handleEditClick}
                disabled={isLoading}
              >
                Edit Profile
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* KYC Status Alert */}
      <div className="row mb-4">
        <div className="col-12">
          <KYCStatusAlert profileData={profileData} />
        </div>
      </div>

      {/* Profile Header Card */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card  shadow-sm">
            <div className="card-body p-3">
              <div className="row align-items-center">
                <div className="col-auto">
                  <Avatar
                    name={
                      profileData?.displayName ||
                      `${profileData?.firstName} ${profileData?.lastName}`
                    }
                    size="large"
                    showBorder={true}
                    className="border border-primary border-2"
                  />
                </div>
                <div className="col">
                  <h6 className="mb-1 fw-semibold">
                    {profileData?.displayName ||
                      `${profileData?.firstName} ${profileData?.lastName}`}
                  </h6>
                  <p className="text-muted mb-1 small">
                    {USER_TYPE_LABELS[profileData?.userType] || "User"}
                  </p>
                  <div className="d-flex flex-wrap gap-2">
                    <div className="d-flex align-items-center">
                      <span className="text-muted me-1 small">Status:</span>
                      <span
                        className={`badge ${getStatusBadgeClass(
                          profileData?.status
                        )}`}
                      >
                        {getStatusLabel(profileData?.status)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Basic Details and Business Address Side by Side */}
      <div className="row mb-3">
        {/* Basic Details Card - Left Side */}
        <div className="col-lg-6 col-md-12">
          <div className="card  shadow-sm h-100">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <h6 className="mb-0 fw-semibold">Basic Details</h6>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="mb-3 p-3 rounded">
                    <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                      First Name
                    </label>
                    <div className="d-flex align-items-center">
                      <span className="   fw-semibold">
                        {profileData?.firstName || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3 p-3 rounded">
                    <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                      Last Name
                    </label>
                    <div className="d-flex align-items-center">
                      <span className="   fw-semibold">
                        {profileData?.lastName || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3 p-3 rounded">
                    <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                      Display Name
                    </label>
                    <div className="d-flex align-items-center">
                      <span className="   fw-semibold">
                        {profileData?.displayName || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3 p-3 rounded">
                    <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                      User Type
                    </label>
                    <div className="d-flex align-items-center">
                      <span className="   fw-semibold">
                        {USER_TYPE_LABELS[userType] || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3 p-3 rounded">
                    <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                      Email
                    </label>
                    <div className="d-flex align-items-center">
                      <span className="   fw-semibold">
                        {profileData?.email || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="col-md-6">
                  <div className="mb-3 p-3 rounded">
                    <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                      Phone Number
                    </label>
                    <div className="d-flex align-items-center">
                      <span className="   fw-semibold">
                        {profileData?.phoneNumber || "N/A"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Business Address Card - Right Side */}
        <div className="col-lg-6 col-md-12">
          <div className="card  shadow-sm h-100">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <h6 className="mb-0 fw-semibold">Business Address</h6>
              </div>
            </div>
            <div className="card-body p-4 d-flex align-items-center justify-content-center">
              <div className="w-100">
                <div className="border rounded p-4 text-center">
                  <div className="mb-2 fw-semibold">
                    {profileData?.address || "Street Address Not Available"}
                  </div>
                  <div className="mb-2">
                    {[
                      profileData?.city,
                      profileData?.state,
                      profileData?.postalCode,
                    ]
                      .filter(Boolean)
                      .join(", ") || "City, State, Postal Code Not Available"}
                  </div>
                  <div className="mb-2">
                    {profileData?.country || "Country Not Available"}
                  </div>
                  {profileData?.region && (
                    <div className="text-muted fst-italic">
                      Region: {profileData.region}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Company Details Card - 2x6 Layout */}
      {isUserCompanyType && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card  shadow-sm">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <h6 className="mb-0 fw-semibold">Company Details</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Company Name
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.companyName || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Brand Name
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.brandName || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Legal Name
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.legalName || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Trade Name
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.tradeName || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Company Email
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.companyContactEmail || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Company Phone
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.companyContactPhone || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-2 col-md-4 col-sm-6">
                    <div className="mb-3 p-3  rounded">
                      <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                        Place of Business
                      </label>
                      <div className="d-flex align-items-center">
                        <span className="   fw-semibold">
                          {profileData?.placeOfBusiness || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  {profileData?.companyLogo && (
                    <div className="col-lg-2 col-md-4 col-sm-6">
                      <div className="mb-3 p-3  rounded">
                        <label className="form-label text-uppercase fw-bold text-muted small mb-1">
                          Company Logo
                        </label>
                        <div className="d-flex align-items-center">
                          <img
                            src={profileData.companyLogo}
                            alt="Company Logo"
                            className="me-2 rounded"
                            width="40"
                            height="40"
                          />
                          <small className="text-muted">Logo uploaded</small>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full Width GST Details Card - 2x6 Layout */}
      {isUserCompanyType && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card  compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <h6 className="mb-0 fw-semibold">GST Details</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">GST Number</label>
                      <div className="compact-field-value">
                        <span>{profileData?.gstNumber || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">GST Name</label>
                      <div className="compact-field-value">
                        <span>{profileData?.gstName || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        GST Registration Number
                      </label>
                      <div className="compact-field-value">
                        <span>
                          {profileData?.gstRegistrationNumber || "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        GST Registration Date
                      </label>
                      <div className="compact-field-value">
                        <span>
                          {formatDate(profileData?.gstRegistrationDate) ||
                            "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Full Width CIN Number Card - 2x6 Layout */}
      {isUserCompanyType && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card  compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <h6 className="mb-0 fw-semibold">CIN Number</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">CIN Number</label>
                      <div className="compact-field-value">
                        <span>{profileData?.cinNumber || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Date of Incorporation
                      </label>
                      <div className="compact-field-value">
                        <span>
                          {formatDate(profileData?.dateOfIncorporation) ||
                            "N/A"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Physical Card Style Layout - PAN, Aadhaar, Bank Cards */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card  shadow-sm">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <h6 className="mb-0 fw-semibold">Identity & Financial Cards</h6>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-3">
                {/* PAN Card */}
                <div className="col-lg-4 col-md-6 col-sm-12">
                  <div className="border border-primary border-2 rounded p-3 h-100">
                    <div className="border-bottom border-primary pb-2 mb-3">
                      <span className="fw-bold     text-uppercase">
                        PAN CARD
                      </span>
                    </div>
                    <div>
                      <div className="row g-2">
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Name
                            </span>
                            <span className="d-block    fw-semibold small">
                              {profileData?.nameAsPerPan || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Father's Name
                            </span>
                            <span className="d-block    fw-semibold small">
                              {profileData?.fatherOrHusbandNameInPan || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Date of Birth
                            </span>
                            <span className="d-block    fw-semibold small">
                              {formatDate(profileData?.dateOfBirthInPan) ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-12">
                          <div className="bg-primary bg-opacity-10 border border-primary border-opacity-25 rounded p-2">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="d-block text-muted small fw-bold">
                                PAN Number
                              </span>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-primary p-1"
                                onClick={togglePanVisibility}
                              >
                                {showNumbers.pan ? (
                                  <FaEyeSlash size={10} />
                                ) : (
                                  <FaEye size={10} />
                                )}
                              </button>
                            </div>
                            <span className="d-block     fw-bold bg-primary bg-opacity-10 border border-primary border-opacity-30 rounded px-2 py-1">
                              {showNumbers.pan
                                ? profileData?.panNumber || "**********"
                                : "••••••••••"}
                            </span>
                          </div>
                        </div>
                        {isUserCompanyType &&
                          profileData?.businessPanNumber && (
                            <>
                              <div className="col-6">
                                <div className="mb-2">
                                  <span className="d-block text-muted small fw-bold">
                                    Business PAN
                                  </span>
                                  <span className="d-block     fw-bold">
                                    {profileData?.businessPanNumber}
                                  </span>
                                </div>
                              </div>
                              <div className="col-6"></div>
                            </>
                          )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Aadhaar Card */}
                <div className="col-lg-4 col-md-6 col-sm-12">
                  <div className="border border-warning border-2 rounded p-3 h-100">
                    <div className="border-bottom border-warning pb-2 mb-3">
                      <span className="fw-bold text-warning text-uppercase">
                        AADHAAR CARD
                      </span>
                    </div>
                    <div>
                      <div className="row g-2">
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Name
                            </span>
                            <span className="d-block   fw-semibold small">
                              {profileData?.nameAsPerAadhaar || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Father's Name
                            </span>
                            <span className="d-block   fw-semibold small">
                              {profileData?.fatherOrHusbandNameInAadhaar ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Date of Birth
                            </span>
                            <span className="d-block   fw-semibold small">
                              {formatDate(profileData?.dateOfBirthInAadhaar) ||
                                "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Gender
                            </span>
                            <span className="d-block   fw-semibold small">
                              {profileData?.genderInAadhaar || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-12">
                          <div className="bg-warning bg-opacity-10 border border-warning border-opacity-25 rounded p-2">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="d-block text-muted small fw-bold">
                                Aadhaar Number
                              </span>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-warning p-1"
                                onClick={toggleAadhaarVisibility}
                              >
                                {showNumbers.aadhaar ? (
                                  <FaEyeSlash size={10} />
                                ) : (
                                  <FaEye size={10} />
                                )}
                              </button>
                            </div>
                            <span className="d-block text-warning fw-bold bg-warning bg-opacity-10 border border-warning border-opacity-30 rounded px-2 py-1">
                              {showNumbers.aadhaar
                                ? profileData?.aadharNumber ||
                                  profileData?.aadhaarNumber ||
                                  profileData?.adhaarNumber ||
                                  "**********12"
                                : "••••-••••-••••"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Bank Card */}
                <div className="col-lg-4 col-md-6 col-sm-12">
                  <div className="border border-success border-2 rounded p-3 h-100">
                    <div className="border-bottom border-success pb-2 mb-3">
                      <span className="fw-bold text-success text-uppercase">
                        BANK DETAILS
                      </span>
                    </div>
                    <div>
                      <div className="row g-2">
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Account Holder
                            </span>
                            <span className="d-block   fw-semibold small">
                              {profileData?.accountHolderName || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              Bank Name
                            </span>
                            <span className="d-block   fw-semibold small">
                              {profileData?.bankName || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-6">
                          <div className="mb-2">
                            <span className="d-block text-muted small fw-bold">
                              IFSC Code
                            </span>
                            <span className="d-block   fw-semibold small">
                              {profileData?.ifscCode || "N/A"}
                            </span>
                          </div>
                        </div>
                        <div className="col-12">
                          <div className="bg-success bg-opacity-10 border border-success border-opacity-25 rounded p-2">
                            <div className="d-flex justify-content-between align-items-center mb-2">
                              <span className="d-block text-muted small fw-bold">
                                Account Number
                              </span>
                              <button
                                type="button"
                                className="btn btn-sm btn-outline-success p-1"
                                onClick={toggleAccountVisibility}
                              >
                                {showNumbers.account ? (
                                  <FaEyeSlash size={10} />
                                ) : (
                                  <FaEye size={10} />
                                )}
                              </button>
                            </div>
                            <span className="d-block text-success fw-bold bg-success bg-opacity-10 border border-success border-opacity-30 rounded px-2 py-1">
                              {showNumbers.account
                                ? profileData?.accountNumber || "**********"
                                : "••••••••••"}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Industry Details Card - 2x6 Layout */}
      {isUserShipperCompany && (
        <div className="row mb-3">
          <div className="col-12">
            <div className="card  compact-details-card">
              <div className="card-header bg-transparent border-bottom py-3">
                <div className="d-flex align-items-center">
                  <h6 className="mb-0 fw-semibold">Industry Details</h6>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-3">
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Industry Type
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.industryType || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-3 col-md-6 col-sm-6">
                    <div className="compact-field-group">
                      <label className="compact-field-label">
                        Business Category
                      </label>
                      <div className="compact-field-value">
                        <span>{profileData?.businessCategory || "N/A"}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Documents Section */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card  compact-details-card">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <h6 className="mb-0 fw-semibold">Documents</h6>
                {userType !== 3 && ( // Don't show badge for Broker (userType 3)
                  <span className="badge bg-info ms-auto">
                    {USER_TYPE_LABELS[userType] || "User"} Documents
                  </span>
                )}
              </div>
            </div>
            <div className="card-body p-4">
              {isLoading ? (
                <div className="text-center py-4">
                  <FaSpinner className="fa-spin     mb-2" size={24} />
                  <p className="text-muted mb-0">Loading documents...</p>
                </div>
              ) : error ? (
                <div className="text-center py-4">
                  <FaExclamationTriangle
                    className="text-warning mb-2"
                    size={24}
                  />
                  <p className="text-muted mb-0">Failed to load documents</p>
                </div>
              ) : (
                <div className="documents-list">
                  {userDocuments.length > 0 ? (
                    userDocuments.map((uploadedDoc) => (
                      <DocumentRow
                        key={uploadedDoc.documentId}
                        document={uploadedDoc}
                        onDownload={handleDocumentDownload}
                        isDownloading={
                          downloadLoading[
                            `${uploadedDoc.documentType}-${uploadedDoc.fileName}`
                          ]
                        }
                        userType={userType}
                      />
                    ))
                  ) : (
                    <div className="text-center py-4">
                      <FaFileAlt className="text-muted mb-2" size={24} />
                      <p className="text-muted mb-0">
                        No documents uploaded yet
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Full Width Account Information Card - 2x6 Layout */}
      <div className="row mb-3">
        <div className="col-12">
          <div className="card  compact-details-card">
            <div className="card-header bg-transparent border-bottom py-3">
              <div className="d-flex align-items-center">
                <h6 className="mb-0 fw-semibold">Account Information</h6>
              </div>
            </div>
            <div className="card-body p-4">
              <div className="row g-3">
                <div className="col-lg-3 col-md-6 col-sm-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">
                      Account Created
                    </label>
                    <div className="compact-field-value">
                      <span>{formatDate(profileData?.createdAt)}</span>
                    </div>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6 col-sm-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Last Updated</label>
                    <div className="compact-field-value">
                      <span>{formatDate(profileData?.updatedAt)}</span>
                    </div>
                  </div>
                </div>
                <div className="col-lg-3 col-md-6 col-sm-6">
                  <div className="compact-field-group">
                    <label className="compact-field-label">Last Login</label>
                    <div className="compact-field-value">
                      <span>{formatDate(profileData?.lastLoginAt)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Optimized Modals */}
      <ApprovalModal
        isOpen={showApproveModal}
        onClose={handleCloseApproveModal}
        onApprove={handleApproveUser}
        notes={approveNotes}
        setNotes={handleSetApproveNotes}
        isLoading={approveUserMutation.isPending}
      />

      <RejectionModal
        isOpen={showRejectModal}
        onClose={handleCloseRejectModal}
        onReject={handleRejectUser}
        reason={rejectReason}
        setReason={handleSetRejectReason}
        notes={rejectNotes}
        setNotes={handleSetRejectNotes}
        isLoading={rejectUserMutation.isPending}
      />
    </div>
  );
});

ViewUser.displayName = "ViewUser";

export default ViewUser;
