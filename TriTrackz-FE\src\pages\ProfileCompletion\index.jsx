import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { ProfileKYCForm } from '@components/ProfileKYC';
import { USER_TYPES, USER_TYPE_LABELS } from '@constants/enum';
import ROUTES from '@constants/routes';
import toast from 'react-hot-toast';

const ProfileCompletion = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { user, token } = useSelector((state) => state.user);
  const { isProfileCompleted, isKYCCompleted } = useSelector((state) => state.user.profileStatus);

  useEffect(() => {
    // Check if user is authenticated
    if (!user || !token) {
      toast.error('Please login to continue');
      navigate(ROUTES.LOGIN);
      return;
    }

    // Check if profile and KYC are already completed
    if (isProfileCompleted && isKYCCompleted) {
      toast.success('Profile and KYC already completed');
      navigate(ROUTES.DASHBOARD);
      return;
    }

    // Check if user type is valid
    if (!user.userType || !Object.values(USER_TYPES).includes(user.userType)) {
      toast.error('Invalid user type. Please contact support.');
      navigate(ROUTES.DASHBOARD);
      return;
    }
  }, [user, token, isProfileCompleted, isKYCCompleted, navigate]);

  // Show loading if user data is not available
  if (!user || !token) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
        <div className="text-center">
          <div className="spinner-border text-primary mb-3" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="text-muted">Loading profile...</p>
        </div>
      </div>
    );
  }

  return <ProfileKYCForm />;
};

export default ProfileCompletion;
