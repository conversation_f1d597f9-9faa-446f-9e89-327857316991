/// <reference types="react" />
import { CSSObject } from '@emotion/styled';
interface StyledMenuLabelProps {
    rootStyles?: CSSObject;
}
export declare const StyledMenuLabel: import("@emotion/styled").StyledComponent<{
    theme?: import("@emotion/react").Theme | undefined;
    as?: import("react").ElementType<any> | undefined;
} & StyledMenuLabelProps, import("react").DetailedHTMLProps<import("react").HTMLAttributes<HTMLSpanElement>, HTMLSpanElement>, {}>;
export {};
