// User Types
export const USER_TYPES = {
  ADMIN: 0,
  TRANSPORT_COMPANY: 2,
  BROKER: 3,
  CARRIER: 4,
  SHIPPER_COMPANY: 7,
  SHIPPER_INDIVIDUAL: 8,
};

// User Type Labels
export const USER_TYPE_LABELS = {
  [USER_TYPES.ADMIN]: "Admin",
  [USER_TYPES.TRANSPORT_COMPANY]: "Transport Company",
  [USER_TYPES.BROKER]: "Broker",
  [USER_TYPES.CARRIER]: "Carrier",
  [USER_TYPES.SHIPPER_COMPANY]: "Shipper Company",
  [USER_TYPES.SHIPPER_INDIVIDUAL]: "Shipper Individual",
};

// Profile Status Values
export const PROFILE_STATUS = {
  INCOMPLETE: 0,
  COMPLETE: 1,
  UNDER_REVIEW: 2,
  APPROVED: 3,
  REJECTED: 4,
};

// Profile Status Labels
export const PROFILE_STATUS_LABELS = {
  [PROFILE_STATUS.INCOMPLETE]: "Incomplete",
  [PROFILE_STATUS.COMPLETE]: "Complete",
  [PROFILE_STATUS.UNDER_REVIEW]: "Under Review",
  [PROFILE_STATUS.APPROVED]: "Approved",
  [PROFILE_STATUS.REJECTED]: "Rejected",
};

// Gender Options
export const GENDER_OPTIONS = [
  { value: "Male", label: "Male" },
  { value: "Female", label: "Female" },
  { value: "Other", label: "Other" },
];

// Industry Types
export const INDUSTRY_TYPES = [
  { value: "Manufacturing", label: "Manufacturing" },
  { value: "Trading", label: "Trading" },
  { value: "Services", label: "Services" },
  { value: "Agriculture", label: "Agriculture" },
  { value: "Construction", label: "Construction" },
  { value: "Technology", label: "Technology" },
  { value: "Healthcare", label: "Healthcare" },
  { value: "Education", label: "Education" },
  { value: "Retail", label: "Retail" },
  { value: "Transportation", label: "Transportation" },
  { value: "Others", label: "Others" },
];

// Business Categories
export const BUSINESS_CATEGORIES = [
  { value: "Sole Proprietorship", label: "Sole Proprietorship" },
  { value: "Partnership", label: "Partnership" },
  { value: "Private Limited", label: "Private Limited" },
  { value: "Public Limited", label: "Public Limited" },
  { value: "LLP", label: "Limited Liability Partnership (LLP)" },
  { value: "One Person Company", label: "One Person Company (OPC)" },
  { value: "Others", label: "Others" },
];
