import React from 'react';
import { Field, ErrorMessage } from 'formik';
import { 
  getDateInputAttributes, 
  DATE_FIELD_TYPES,
  formatDateForInput 
} from '@utils/dateValidationUtils';

/**
 * Enhanced DateInput Component with built-in validation and calendar restrictions
 * 
 * @param {Object} props - Component props
 * @param {string} props.name - Field name for Formik
 * @param {string} props.label - Label text
 * @param {boolean} props.required - Whether field is required
 * @param {string} props.fieldType - Type of date field (from DATE_FIELD_TYPES)
 * @param {string} props.placeholder - Placeholder text
 * @param {string} props.className - Additional CSS classes
 * @param {Object} props.formik - Formik instance
 * @param {boolean} props.disabled - Whether input is disabled
 * @param {string} props.helpText - Help text to display below input
 * @param {Date|string} props.minDate - Custom minimum date (overrides fieldType)
 * @param {Date|string} props.maxDate - Custom maximum date (overrides fieldType)
 */
const DateInput = ({
  name,
  label,
  required = false,
  fieldType = DATE_FIELD_TYPES.ANY_DATE,
  placeholder = '',
  className = '',
  formik,
  disabled = false,
  helpText = '',
  minDate = null,
  maxDate = null,
  ...props
}) => {
  // Get date attributes based on field type or custom dates
  const dateAttributes = getDateInputAttributes(fieldType);
  
  // Override with custom dates if provided
  const finalMinDate = minDate ? formatDateForInput(minDate) : dateAttributes.min;
  const finalMaxDate = maxDate ? formatDateForInput(maxDate) : dateAttributes.max;
  
  // Get field error and touched state
  const fieldError = name.split('.').reduce((obj, key) => obj?.[key], formik.errors);
  const fieldTouched = name.split('.').reduce((obj, key) => obj?.[key], formik.touched);
  const fieldValue = name.split('.').reduce((obj, key) => obj?.[key], formik.values);
  
  // Determine field state classes
  const getFieldStateClass = () => {
    if (fieldTouched && fieldError) {
      return 'is-invalid';
    }
    if (fieldTouched && fieldValue && !fieldError) {
      return 'is-valid';
    }
    return '';
  };
  
  // Generate help text based on field type
  const getHelpText = () => {
    if (helpText) return helpText;
    
    switch (fieldType) {
      case DATE_FIELD_TYPES.DATE_OF_BIRTH:
        return 'Must be at least 18 years old';
      case DATE_FIELD_TYPES.DATE_OF_INCORPORATION:
        return 'Date when company was incorporated';
      case DATE_FIELD_TYPES.REGISTRATION_DATE:
        return 'Date of business registration';
      case DATE_FIELD_TYPES.FUTURE_DATE:
        return 'Select a future date';
      case DATE_FIELD_TYPES.PAST_DATE:
        return 'Select a past date';
      default:
        return '';
    }
  };
  
  return (
    <div className={`date-input-wrapper ${className}`}>
      {label && (
        <label htmlFor={name} className="clean-form-label">
          {label}
          {required && <span className="text-danger"> *</span>}
        </label>
      )}
      
      <Field
        type="date"
        name={name}
        id={name}
        className={`clean-form-control ${getFieldStateClass()}`}
        placeholder={placeholder}
        disabled={disabled}
        min={finalMinDate}
        max={finalMaxDate}
        {...props}
      />
      
      <ErrorMessage
        name={name}
        component="div"
        className="clean-form-error"
      />
      
      {getHelpText() && (
        <small className="form-text text-muted mt-1">
          {getHelpText()}
        </small>
      )}
    </div>
  );
};

export default DateInput;
