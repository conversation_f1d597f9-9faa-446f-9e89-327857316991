import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  FaHome,
  FaCog,
  <PERSON>a<PERSON>ser,
  FaChartBar,
  FaBoxOpen,
} from "react-icons/fa";
import { getDisplayName, getUserRoleLabel } from "@utils/profileUtils";
import ROUTES from "@constants/routes";
import NavigationGuard from "@components/NavigationGuard";

const NewSidebar = ({ collapsed, mobileOpen, onMobileClose }) => {
  const location = useLocation();
  const { profileData } = useSelector((state) => state.user);

  // Get display name and role from profile data
  const displayName = getDisplayName(profileData);
  const userRole = getUserRoleLabel(profileData);

  const isActive = (route) => location.pathname === route;

  const menuItems = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <FaHome size={16} />,
      route: ROUTES.DASHBOARD,
      active: isActive(ROUTES.DASHBOARD),
      protected: false, // Dashboard is always accessible
    },
    {
      id: "shipments",
      label: "Shipments",
      icon: <FaBoxOpen size={16} />,
      route: "/shipments",
      active: isActive("/shipments"),
      protected: true, // Shipments require profile/KYC completion
    },
    {
      id: "reports",
      label: "Reports",
      icon: <FaChartBar size={16} />,
      route: "/reports",
      active: isActive("/reports"),
      protected: true, // Reports require profile/KYC completion
    },
  ];

  const bottomMenuItems = [
    {
      id: "settings",
      label: "Settings",
      icon: <FaCog size={16} />,
      route: "/settings",
      active: isActive("/settings"),
      protected: true, // Settings require profile/KYC completion
    },
  ];

  return (
    <>
      {/* Mobile Backdrop */}
      {mobileOpen && (
        <div className="new-sidebar-backdrop" onClick={onMobileClose}></div>
      )}

      {/* Sidebar */}
      <div className={`new-sidebar ${collapsed ? "collapsed" : ""} ${mobileOpen ? "mobile-open" : ""}`}>
        {/* Logo Section */}
        <div className="new-sidebar-header">
          <div className="new-sidebar-logo">
            <div className="logo-icon">
              <span>🚚</span>
            </div>
            {!collapsed && (
              <div className="logo-text ">
                <h5 className="text-white">Tritrackz</h5>
              </div>
            )}
          </div>
        </div>

        {/* Main Menu */}
        <div className="new-sidebar-menu">
          <nav className="menu-nav">
            {menuItems.map((item) => {
              const menuItemContent = (
                <div className={`menu-item ${item.active ? "active" : ""}`}>
                  <div className="menu-icon">{item.icon}</div>
                  {!collapsed && <span className="menu-label">{item.label}</span>}
                </div>
              );

              if (item.protected) {
                return (
                  <NavigationGuard
                    key={item.id}
                    to={item.route}
                    onClick={() => mobileOpen && onMobileClose()}
                  >
                    {menuItemContent}
                  </NavigationGuard>
                );
              } else {
                return (
                  <Link
                    key={item.id}
                    to={item.route}
                    className={`menu-item ${item.active ? "active" : ""}`}
                    onClick={() => mobileOpen && onMobileClose()}
                  >
                    <div className="menu-icon">{item.icon}</div>
                    {!collapsed && <span className="menu-label">{item.label}</span>}
                  </Link>
                );
              }
            })}
          </nav>
        </div>

        {/* Bottom Menu */}
        <div className="new-sidebar-bottom">
          <nav className="bottom-nav">
            {bottomMenuItems.map((item) => {
              const menuItemContent = (
                <div className={`menu-item ${item.active ? "active" : ""}`}>
                  <div className="menu-icon">{item.icon}</div>
                  {!collapsed && <span className="menu-label">{item.label}</span>}
                </div>
              );

              if (item.protected) {
                return (
                  <NavigationGuard
                    key={item.id}
                    to={item.route}
                    onClick={() => mobileOpen && onMobileClose()}
                  >
                    {menuItemContent}
                  </NavigationGuard>
                );
              } else {
                return (
                  <Link
                    key={item.id}
                    to={item.route}
                    className={`menu-item ${item.active ? "active" : ""}`}
                    onClick={() => mobileOpen && onMobileClose()}
                  >
                    <div className="menu-icon">{item.icon}</div>
                    {!collapsed && <span className="menu-label">{item.label}</span>}
                  </Link>
                );
              }
            })}
          </nav>

          {/* User Profile */}
          {!collapsed && (
            <div className="user-profile">
              <div className="user-avatar">
                <FaUser size={16} />
              </div>
              <div className="user-info">
                <div className="user-name">{displayName}</div>
                <div className="user-role">{userRole}</div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default NewSidebar;
