export const API_ENDPOINTS = {
    // Authentication endpoints
    MO<PERSON>LE_REGISTER: '/v1/Auth/register/mobile',
    VERIFY_OTP: '/v1/Auth/otp/verify',
    MOBILE_VERIFY: '/v1/Auth/register/mobile/verify',
    LOGIN: '/v1/Auth/login',
    MOBILE_LOGIN: '/v1/Auth/mobile/login',
    CHECK_USER_TYPES: '/v1/Auth/mobile/usertypes',
    CHANGE_PASSWORD: '/v1/Auth/change-password',

    // Mobile number change endpoints
    REGISTER_MOBILE_CHANGE: '/v1/Auth/register/mobile',
    VERIFY_CHANGE_OTP: '/v1/Auth/verify-change-otp',
    CHANGE_MOBILE_NUMBER: '/v1/Auth/change-mobile-number',

    // User endpoints
    COMPLETE_PROFILE: '/v1/User/complete-profile',

    // Profile/KYC endpoints
    COMPREHENSIVE_PROFILE: '/ComprehensiveProfile',
    LOCATION_BY_PINCODE: '/ComprehensiveProfile/location',
    GET_PROFILE: '/ComprehensiveProfile', // GET endpoint for profile data

    // Verification endpoints
    VERIFY_PAN: '/ComprehensiveProfile/verify-pan',
    VERIFY_GST: '/ComprehensiveProfile/verify-gst',

    // Document upload endpoints
    UPLOAD_DOCUMENT: '/Documents/upload',
    DOCUMENT_GET_FILE: (userId) => `/Documents/get-file/${userId}`,
}