import React, { useState } from "react";
import { useSelector } from "react-redux";
import { useNavigate } from "react-router-dom";
import ModernActionPopup from "@components/ModernActionPopup";

/**
 * NavigationGuard Component
 * 
 * This component provides a wrapper for navigation links that checks
 * if the user has completed their profile and KYC before allowing navigation.
 * If not completed, it shows a modal with completion requirements.
 */
const NavigationGuard = ({
  children,
  to,
  requireProfile = true,
  requireKYC = false, // Disable KYC requirement for user portals
  className = "",
  onClick,
  ...props
}) => {
  const [showModernPopup, setShowModernPopup] = useState(false);
  const navigate = useNavigate();

  const { isProfileCompleted, isKYCCompleted } = useSelector(
    (state) => state.user.profileStatus
  );
  const { profileData } = useSelector((state) => state.user);

  const handleClick = (e) => {
    e.preventDefault();

    // Check if profile status is 0 (Incomplete) - show popup
    if (profileData?.status === 0) {
      setShowModernPopup(true);
      return;
    }

    // If profile is complete (status >= 1), proceed with navigation
    if (onClick) {
      onClick(e);
    }

    if (to) {
      navigate(to);
    }
  };



  return (
    <>
      {/* Render children with click handler */}
      {React.cloneElement(children, {
        ...props,
        className: `${className} ${children.props.className || ''}`.trim(),
        onClick: handleClick,
        style: { cursor: 'pointer', ...children.props.style }
      })}

      {/* Modern Action Popup */}
      <ModernActionPopup
        isOpen={showModernPopup}
        onClose={() => setShowModernPopup(false)}
      />
    </>
  );
};

export default NavigationGuard;
