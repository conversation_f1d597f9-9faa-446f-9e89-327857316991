import React from "react";
import { Field, ErrorMessage } from "formik";
import { DOCUMENT_TYPES } from "@api/documentUploadHooks";
import DocumentUpload from "./DocumentUpload";

const CinNumberForm = ({
  formik,
  existingDocuments,
  onDocumentChange,
  targetUserId = null,
}) => {
  // existingDocuments and onDocumentChange are not used in this form but passed from parent
  return (
    <div className="mb-4">
      <div className="mb-4">
        <h3 className="h4 fw-semibold  mb-2">CIN Number</h3>
        <p className="text-muted mb-0">
          Please provide your Corporate Identification Number
        </p>
      </div>
      <div className="row">
        <div className="col-12 mb-3">
          <label htmlFor="cinNumber.cin" className="form-label fw-medium">
            CIN Number <span className="text-danger">*</span>
          </label>
          <Field
            type="text"
            name="cinNumber.cin"
            className={`form-control ${
              formik.touched.cinNumber?.cin && formik.errors.cinNumber?.cin
                ? "is-invalid"
                : ""
            }`}
            placeholder="Enter CIN number"
            style={{ textTransform: "uppercase" }}
          />
          <ErrorMessage
            name="cinNumber.cin"
            component="div"
            className="invalid-feedback"
          />
          <div className="form-text">
            Corporate Identification Number (CIN) is a unique 21-digit
            alphanumeric code.
          </div>
        </div>
      </div>

      <div className="col-12 mb-3">
        {/* CIN Document Upload - Optional */}
        <DocumentUpload
          documentType={DOCUMENT_TYPES.TRADE_LICENSE}
          title="Trade Document"
          accept=".pdf,.jpg,.jpeg,.png"
          existingDocuments={existingDocuments}
          onDocumentChange={onDocumentChange}
          optional={true}
          targetUserId={targetUserId}
        />
      </div>
    </div>
  );
};

export default CinNumberForm;
