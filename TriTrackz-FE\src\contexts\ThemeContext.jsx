import React, { createContext, useContext, useEffect, useState } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  // Get initial theme from localStorage or default to 'dark'
  const [theme, setTheme] = useState(() => {
    const savedTheme = localStorage.getItem('tritrackz-theme');
    if (savedTheme) {
      return savedTheme;
    }

    // Always default to dark theme
    return 'dark';
  });

  // Apply theme to document and save to localStorage
  useEffect(() => {
    const root = document.documentElement;

    // Add theme switching class to prevent transition flashing
    root.classList.add('theme-switching');

    // Set both theme attributes for maximum compatibility
    root.setAttribute('data-theme', theme); // Custom theme attribute
    root.setAttribute('data-bs-theme', theme); // Bootstrap's native theme attribute

    // Save to localStorage
    localStorage.setItem('tritrackz-theme', theme);

    // Remove theme switching class after a short delay
    const timer = setTimeout(() => {
      root.classList.remove('theme-switching');
    }, 100);

    return () => clearTimeout(timer);
  }, [theme]);

  // Listen for system theme changes
  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleSystemThemeChange = (e) => {
      // Only update if no theme is saved in localStorage (user hasn't manually set a preference)
      const savedTheme = localStorage.getItem('tritrackz-theme');
      if (!savedTheme) {
        // Always prefer dark theme, even if system prefers light
        setTheme('dark');
      }
    };

    mediaQuery.addEventListener('change', handleSystemThemeChange);

    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange);
    };
  }, []);

  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'dark' ? 'light' : 'dark');
  };

  const setLightTheme = () => {
    setTheme('light');
  };

  const setDarkTheme = () => {
    setTheme('dark');
  };

  const setSystemTheme = () => {
    // Remove saved preference and use dark theme as default
    localStorage.removeItem('tritrackz-theme');
    setTheme('dark');
  };

  const resetToDefaultTheme = () => {
    // Reset to dark theme and remove from localStorage
    localStorage.removeItem('tritrackz-theme');
    setTheme('dark');
  };

  const value = {
    theme,
    toggleTheme,
    setLightTheme,
    setDarkTheme,
    setSystemTheme,
    resetToDefaultTheme,
    isDark: theme === 'dark',
    isLight: theme === 'light'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export default ThemeContext;
