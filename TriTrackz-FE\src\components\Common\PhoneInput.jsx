import React from 'react';
import PhoneInputComponent from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';

const PhoneInput = ({ 
  name, 
  value, 
  onChange, 
  onBlur,
  placeholder = "Enter phone number",
  className = "",
  isInvalid = false,
  isValid = false,
  disabled = false,
  country = "in" // Default to India
}) => {
  const handleChange = (phone, countryData) => {
    // Ensure we only allow valid Indian mobile numbers
    // The phone value comes with country code (e.g., "************")
    let processedPhone = phone;

    // If it's an Indian number, validate the format
    if (countryData.countryCode === 'in' || phone.startsWith('91')) {
      const cleaned = phone.replace(/\D/g, '');

      // Ensure it starts with 91 and has exactly 12 digits
      if (cleaned.length <= 12) {
        if (cleaned.length >= 2 && !cleaned.startsWith('91')) {
          // If user is typing and it doesn't start with 91, prepend it
          processedPhone = '91' + cleaned;
        } else {
          processedPhone = cleaned;
        }

        // Validate the 10-digit part (after country code)
        if (cleaned.length > 2) {
          const mobileDigits = cleaned.substring(2);
          if (mobileDigits.length > 0 && !/^[6-9]/.test(mobileDigits)) {
            // Don't allow numbers that don't start with 6-9
            return;
          }
        }
      } else {
        // Don't allow more than 12 digits
        return;
      }
    }

    // Create a synthetic event-like object for Formik compatibility
    const syntheticEvent = {
      target: {
        name: name,
        value: processedPhone
      }
    };
    onChange(syntheticEvent);
  };

  const handleBlur = () => {
    if (onBlur) {
      const syntheticEvent = {
        target: {
          name: name,
          value: value
        }
      };
      onBlur(syntheticEvent);
    }
  };

  // Determine the CSS classes based on validation state
  const getInputClass = () => {
    let baseClass = `clean-form-control phone-input-field ${className}`;
    if (isInvalid) {
      baseClass += ' is-invalid';
    } else if (isValid) {
      baseClass += ' is-valid';
    }
    return baseClass;
  };

  return (
    <div className="phone-input-wrapper">
      <PhoneInputComponent
        country={country}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        placeholder={placeholder}
        disabled={disabled}
        inputClass={getInputClass()}
        containerClass="phone-input-container"
        buttonClass="phone-input-button"
        dropdownClass="phone-input-dropdown"
        searchClass="phone-input-search"
        inputProps={{
          name: name,
          required: true,
          autoComplete: 'tel'
        }}
        specialLabel=""
        enableSearch={true}
        disableSearchIcon={false}
        countryCodeEditable={false}
        enableAreaCodes={false}
        enableLongNumbers={true}
        autoFormat={true}
        disableCountryCode={false}
        disableDropdown={false}
        priority={{
          in: 0,
          us: 1,
          gb: 2,
          ca: 3
        }}
      />
    </div>
  );
};

export default PhoneInput;
