import React from 'react';
import { FaTimes } from 'react-icons/fa';
import { useRightSidebar } from '@contexts/RightSidebarContext';

const RightSidebar = () => {
  const { isOpen, ContentComponent, title, icon, width, closeSidebar } = useRightSidebar();

  return (
    <div
      className={`layout-right-sidebar d-flex flex-column h-100 ${isOpen ? 'open' : ''}`}
      style={{
        '--sidebar-width': width,
        width: isOpen ? width : '0'
      }}
    >
      {isOpen && (
        <>
          {/* Header */}
          <div className="right-sidebar-header d-flex align-items-center justify-content-between p-3 flex-shrink-0">
            <div className="right-sidebar-title d-flex align-items-center gap-3">
              {icon && (
                <span className="right-sidebar-icon d-flex align-items-center justify-content-center rounded-2"
                      style={{ width: '32px', height: '32px', fontSize: '14px' }}>
                  {icon}
                </span>
              )}
              <h5 className="mb-0 fw-semibold">{title}</h5>
            </div>
            <button
              type="button"
              className="right-sidebar-close btn p-2 rounded-2"
              onClick={closeSidebar}
              aria-label="Close sidebar"
            >
              <FaTimes size={18} />
            </button>
          </div>

          {/* Content */}
          <div className="right-sidebar-content flex-fill overflow-auto">
            {ContentComponent && <ContentComponent />}
          </div>
        </>
      )}
    </div>
  );
};

export default RightSidebar;
