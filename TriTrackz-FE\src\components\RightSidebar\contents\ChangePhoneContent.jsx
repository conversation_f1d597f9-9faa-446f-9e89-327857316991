import React, { useState, useRef } from 'react';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useSelector } from 'react-redux';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { useRightSidebar } from '@contexts/RightSidebarContext';
import { useRegisterMobileChange, useVerifyChangeOTP, useChangeMobileNumber } from '@api/authHooks';
import toast from 'react-hot-toast';

const ChangePhoneContent = () => {
  const { user, profileData } = useSelector((state) => state.user);
  const { closeSidebar } = useRightSidebar();

  // State management
  const [step, setStep] = useState(1); // 1: Choose method, 2: Verify, 3: Enter new mobile, 4: Success
  const [verificationMethod, setVerificationMethod] = useState(''); // 'otp' or 'password'
  const [showPassword, setShowPassword] = useState(false);
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [otpData, setOtpData] = useState(null);

  const inputRefs = useRef([]);

  // Get current mobile number
  const currentMobile = profileData?.mobileNumber || user?.phoneNumber || '';
  const userId = user?.id || profileData?.userId || '';

  // API hooks
  const registerMobileChangeMutation = useRegisterMobileChange({
    onSuccess: (response) => {
      if (response.success || response.otp) {
        setOtpData(response);

        // Auto-fill OTP if received in response
        if (response.otp) {
          const otpString = response.otp.toString();
          const otpArray = otpString.split('').slice(0, 6);
          // Pad with empty strings if OTP is less than 6 digits
          while (otpArray.length < 6) {
            otpArray.push('');
          }
          setOtp(otpArray);
          toast.success(`OTP sent to your current mobile number. Auto-filled: ${response.otp}`);
        } else {
          toast.success('OTP sent to your current mobile number');
        }

        setStep(2);
      } else {
        toast.error(response.message || 'Failed to send OTP');
      }
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || 'Failed to send OTP');
    }
  });

  const verifyChangeOTPMutation = useVerifyChangeOTP({
    onSuccess: (response) => {
      if (response.success) {
        toast.success('Verification successful');
        setStep(3);
      } else {
        toast.error(response.message || 'Verification failed');
      }
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || 'Verification failed');
    }
  });

  const changeMobileNumberMutation = useChangeMobileNumber({
    onSuccess: (response) => {
      if (response.success) {
        toast.success('Mobile number changed successfully');
        setStep(4);
        setTimeout(() => {
          closeSidebar();
        }, 2000);
      } else {
        toast.error(response.message || 'Failed to change mobile number');
      }
    },
    onError: (error) => {
      toast.error(error?.response?.data?.message || 'Failed to change mobile number');
    }
  });

  // Validation schemas
  const passwordValidationSchema = Yup.object({
    password: Yup.string().required('Password is required for verification')
  });

  const newMobileValidationSchema = Yup.object({
    newMobileNumber: Yup.string()
      .required('New mobile number is required')
      .matches(/^\+?[1-9]\d{1,14}$/, 'Please enter a valid mobile number')
  });

  // Password form
  const passwordFormik = useFormik({
    initialValues: { password: '' },
    validationSchema: passwordValidationSchema,
    onSubmit: (values) => {
      const payload = {
        mobileNumber: trimCountryCode(currentMobile),
        userId: userId,
        password: values.password
      };
      verifyChangeOTPMutation.mutate(payload);
    }
  });

  // New mobile form
  const newMobileFormik = useFormik({
    initialValues: { newMobileNumber: '' },
    validationSchema: newMobileValidationSchema,
    onSubmit: (values) => {
      const payload = {
        mobileNumber: trimCountryCode(values.newMobileNumber),
        currentUserMobileNumber: trimCountryCode(currentMobile)
      };
      changeMobileNumberMutation.mutate(payload);
    }
  });

  // Helper function to trim +91 country code
  const trimCountryCode = (phoneNumber) => {
    if (!phoneNumber) return '';
    // Remove +91 country code specifically
    return phoneNumber.replace(/^\+91/, '');
  };

  // Handler functions
  const handleMethodSelect = (method) => {
    setVerificationMethod(method);
    if (method === 'otp') {
      // Send OTP to current mobile number
      const payload = {
        mobileNumber: trimCountryCode(currentMobile),
        userId: userId
      };
      registerMobileChangeMutation.mutate(payload);
    } else {
      // Go directly to password verification
      setStep(2);
    }
  };

  const handleOtpChange = (index, value) => {
    if (value.length > 1) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleVerifyOTP = () => {
    const otpString = otp.join('');
    if (otpString.length !== 6) {
      toast.error('Please enter the complete 6-digit OTP');
      return;
    }

    const payload = {
      mobileNumber: trimCountryCode(currentMobile),
      userId: userId,
      otp: otpString
    };
    verifyChangeOTPMutation.mutate(payload);
  };

  const handleBackToMethod = () => {
    setStep(1);
    setVerificationMethod('');
    setOtp(['', '', '', '', '', '']);
    setOtpData(null);
    passwordFormik.resetForm();
  };

  const handleBackToVerify = () => {
    setStep(2);
    newMobileFormik.resetForm();
  };

  // Render different steps
  const renderStep1 = () => (
    <>
      <div className="sidebar-section p-3 border-bottom">
        <div className="section-title small fw-bold text-uppercase text-secondary mb-3" style={{ letterSpacing: '0.5px' }}>
          Current Information
        </div>
        <div className="p-3 rounded-2" style={{
          fontSize: '0.875rem',
          backgroundColor: 'var(--bg-surface)'
        }}>
          <strong className="text-primary">Current phone number:</strong><br />
          <span className="text-secondary">{currentMobile}</span>
        </div>
      </div>

      <div className="sidebar-form p-3">
        <div className="section-title small fw-bold text-uppercase text-secondary mb-3" style={{ letterSpacing: '0.5px' }}>
          Choose Verification Method
        </div>

        <div className="d-grid gap-3">
          <button
            type="button"
            className="btn btn-outline-primary text-start p-3"
            onClick={() => handleMethodSelect('otp')}
            disabled={registerMobileChangeMutation.isPending}
            style={{ borderRadius: '8px' }}
          >
            <div className="d-flex align-items-center">
              <div className="flex-grow-1">
                <div className="fw-semibold">Verify with OTP</div>
                <small className="text-secondary">We'll send an OTP to your current mobile number</small>
              </div>
            </div>
          </button>

          <button
            type="button"
            className="btn btn-outline-primary text-start p-3"
            onClick={() => handleMethodSelect('password')}
            style={{ borderRadius: '8px' }}
          >
            <div className="d-flex align-items-center">
              <div className="flex-grow-1">
                <div className="fw-semibold">Verify with Password</div>
                <small className="text-secondary">Enter your current password to verify</small>
              </div>
            </div>
          </button>
        </div>
      </div>

      <div className="sidebar-section p-3 border-bottom">
        <div className="alert alert-info p-3 rounded-2 small">
          <strong>Note:</strong> You'll need to verify your identity before changing your mobile number for security purposes.
        </div>
      </div>

      <div className="sidebar-footer p-3 border-top flex-shrink-0">
        <button
          type="button"
          className="btn btn-outline-secondary w-100"
          onClick={closeSidebar}
        >
          Cancel
        </button>
      </div>
    </>
  );

  const renderStep2 = () => {
    if (verificationMethod === 'otp') {
      return (
        <>
          <div className="sidebar-section p-3 border-bottom">
            <div className="section-title small fw-bold text-uppercase text-secondary mb-3" style={{ letterSpacing: '0.5px' }}>
              Enter OTP
            </div>
            <div className="p-3 rounded-2 mb-3" style={{
              fontSize: '0.875rem',
              backgroundColor: 'var(--bg-surface)'
            }}>
              <strong className="text-primary">OTP sent to:</strong><br />
              <span className="text-secondary">{currentMobile}</span>
            </div>
          </div>

          <div className="sidebar-form p-3">
            <div className="form-group mb-3">
              <div className="d-flex justify-content-between align-items-center mb-2">
                <label className="form-label fw-semibold small mb-0">Enter 6-digit OTP</label>
                {otpData?.otp && (
                  <button
                    type="button"
                    className="btn btn-link btn-sm p-0 text-decoration-none"
                    onClick={() => setOtp(['', '', '', '', '', ''])}
                    style={{ fontSize: '0.75rem' }}
                  >
                    Clear OTP
                  </button>
                )}
              </div>

              {otpData?.otp && (
                <div className="alert alert-success p-2 mb-3 small">
                  <strong>Auto-filled OTP:</strong> {otpData.otp}
                </div>
              )}

              <div className="d-flex gap-2 justify-content-center">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    ref={(el) => (inputRefs.current[index] = el)}
                    type="text"
                    className="form-control text-center"
                    style={{
                      width: '45px',
                      height: '45px',
                      fontSize: '1.2rem',
                      fontWeight: 'bold',
                      borderRadius: '8px',
                      backgroundColor: otpData?.otp ? 'var(--bs-success-bg-subtle)' : 'var(--bg-surface)',
                      borderColor: otpData?.otp ? 'var(--bs-success-border-subtle)' : 'var(--border-primary)'
                    }}
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Backspace' && !digit && index > 0) {
                        inputRefs.current[index - 1]?.focus();
                      }
                    }}
                  />
                ))}
              </div>
            </div>
          </div>

          <div className="sidebar-footer p-3 border-top flex-shrink-0">
            <div className="d-flex gap-2 w-100">
              <button
                type="button"
                className="btn btn-outline-secondary flex-fill"
                onClick={handleBackToMethod}
              >
                Back
              </button>
              <button
                type="button"
                className="btn btn-primary flex-fill"
                onClick={handleVerifyOTP}
                disabled={verifyChangeOTPMutation.isPending || otp.join('').length !== 6}
              >
                {verifyChangeOTPMutation.isPending ? 'Verifying...' : 'Verify OTP'}
              </button>
            </div>
          </div>
        </>
      );
    } else {
      return (
        <>
          <div className="sidebar-section p-3 border-bottom">
            <div className="section-title small fw-bold text-uppercase text-secondary mb-3" style={{ letterSpacing: '0.5px' }}>
              Password Verification
            </div>
          </div>

          <form onSubmit={passwordFormik.handleSubmit} className="sidebar-form p-3">
            <div className="form-group mb-3">
              <label htmlFor="password" className="form-label fw-semibold small">
                Current Password
              </label>
              <div className="position-relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  id="password"
                  name="password"
                  className={`form-control ${passwordFormik.touched.password && passwordFormik.errors.password ? 'is-invalid' : ''}`}
                  placeholder="Enter your current password"
                  value={passwordFormik.values.password}
                  onChange={passwordFormik.handleChange}
                  onBlur={passwordFormik.handleBlur}
                  style={{
                    height: '45px',
                    borderRadius: '8px',
                    paddingRight: '45px'
                  }}
                />
                <button
                  type="button"
                  className="btn position-absolute top-50 end-0 translate-middle-y me-2"
                  style={{ border: 'none', background: 'none', zIndex: 5 }}
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
                </button>
              </div>
              {passwordFormik.touched.password && passwordFormik.errors.password && (
                <div className="invalid-feedback d-block">{passwordFormik.errors.password}</div>
              )}
            </div>
          </form>

          <div className="sidebar-footer p-3 border-top flex-shrink-0">
            <div className="d-flex gap-2 w-100">
              <button
                type="button"
                className="btn btn-outline-secondary flex-fill"
                onClick={handleBackToMethod}
              >
                Back
              </button>
              <button
                type="submit"
                className="btn btn-primary flex-fill"
                onClick={passwordFormik.handleSubmit}
                disabled={verifyChangeOTPMutation.isPending || !passwordFormik.isValid}
              >
                {verifyChangeOTPMutation.isPending ? 'Verifying...' : 'Verify Password'}
              </button>
            </div>
          </div>
        </>
      );
    }
  };

  const renderStep3 = () => (
    <>
      <div className="sidebar-section p-3 border-bottom">
        <div className="section-title small fw-bold text-uppercase text-secondary mb-3" style={{ letterSpacing: '0.5px' }}>
          Enter New Mobile Number
        </div>
        <div className="alert alert-success p-3 rounded-2 small">
          <strong>Verification successful!</strong> Now enter your new mobile number.
        </div>
      </div>

      <form onSubmit={newMobileFormik.handleSubmit} className="sidebar-form p-3">
        <div className="form-group mb-3">
          <label htmlFor="newMobileNumber" className="form-label fw-semibold small">
            New Mobile Number
          </label>
          <PhoneInput
            country={'in'}
            value={newMobileFormik.values.newMobileNumber}
            onChange={(phone) => newMobileFormik.setFieldValue('newMobileNumber', `+${phone}`)}
            onBlur={() => newMobileFormik.setFieldTouched('newMobileNumber', true)}
            inputStyle={{
              width: '100%',
              height: '45px',
              borderRadius: '8px',
              border: `1px solid ${newMobileFormik.touched.newMobileNumber && newMobileFormik.errors.newMobileNumber ? 'var(--danger)' : 'var(--border-primary)'}`,
              backgroundColor: 'var(--bg-surface)',
              color: 'var(--text-primary)',
              fontSize: '0.875rem'
            }}
            buttonStyle={{
              borderRadius: '8px 0 0 8px',
              border: `1px solid ${newMobileFormik.touched.newMobileNumber && newMobileFormik.errors.newMobileNumber ? 'var(--danger)' : 'var(--border-primary)'}`,
              backgroundColor: 'var(--bg-surface)'
            }}
            dropdownStyle={{
              backgroundColor: 'var(--bg-card)',
              color: 'var(--text-primary)'
            }}
          />
          {newMobileFormik.touched.newMobileNumber && newMobileFormik.errors.newMobileNumber && (
            <div className="invalid-feedback d-block">{newMobileFormik.errors.newMobileNumber}</div>
          )}
        </div>
      </form>

      <div className="sidebar-footer p-3 border-top flex-shrink-0">
        <div className="d-flex gap-2 w-100">
          <button
            type="button"
            className="btn btn-outline-secondary flex-fill"
            onClick={handleBackToVerify}
          >
            Back
          </button>
          <button
            type="submit"
            className="btn btn-primary flex-fill"
            onClick={newMobileFormik.handleSubmit}
            disabled={changeMobileNumberMutation.isPending || !newMobileFormik.isValid}
          >
            {changeMobileNumberMutation.isPending ? 'Updating...' : 'Update Mobile Number'}
          </button>
        </div>
      </div>
    </>
  );

  const renderStep4 = () => (
    <>
      <div className="sidebar-section p-3 text-center">
        <div className="mb-4">
          <div className="text-success mb-3" style={{ fontSize: '3rem' }}>
            ✓
          </div>
          <h5 className="text-success mb-3">Mobile Number Updated Successfully!</h5>
          <p className="text-secondary small">
            Your mobile number has been changed successfully. You can now use your new number for login and verification.
          </p>
        </div>
      </div>
    </>
  );

  return (
    <>
      {step === 1 && renderStep1()}
      {step === 2 && renderStep2()}
      {step === 3 && renderStep3()}
      {step === 4 && renderStep4()}
    </>
  );
};

export default ChangePhoneContent;
