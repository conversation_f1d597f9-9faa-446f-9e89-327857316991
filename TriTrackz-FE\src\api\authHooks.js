import { useMutation } from '@tanstack/react-query';
import { API_ENDPOINTS } from '@constants/apiNamevariables';
import { authAPI, baseAPI } from './axiosInstance';

// Check user types
export const useCheckUserTypes = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.CHECK_USER_TYPES, {
        mobileNumber: data.mobileNumber,
        countryCode: data.countryCode || "IN",
      }),
    ...options,
  });

// Mobile registration
export const useMobileRegister = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.MOBILE_REGISTER, {
        mobileNumber: data.mobileNumber,
        countryCode: data.countryCode || "IN",
        userType: data.userType,
      }),
    ...options,
  });

// OTP verification
export const useVerifyOTP = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.VERIFY_OTP, {
        mobileNumber: data.mobileNumber,
        userType: data.userType,
        otpToken: data.otpToken,
        deviceInfo: data.deviceInfo || "web-browser",
      }),
    ...options,
  });

// Login
export const useLogin = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.LOGIN, data),
    ...options,
  });

// Mobile login with password
export const useMobileLogin = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.MOBILE_LOGIN, {
        mobileNumber: data.mobileNumber,
        password: data.password,
        userType: data.userType,
        isAdmin: data.isAdmin || false,
        deviceInfo: data.deviceInfo || "web-browser",
      }),
    ...options,
  });

// Complete profile
export const useCompleteProfile = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.COMPLETE_PROFILE, data),
    ...options,
  });

// Submit comprehensive profile (Profile + KYC)
export const useSubmitComprehensiveProfile = (options) =>
  useMutation({
    mutationFn: ({ userId, profileData }) =>
      baseAPI.put(`${API_ENDPOINTS.COMPREHENSIVE_PROFILE}/${userId}`, profileData),
    ...options,
  });

// Autosave comprehensive profile (Profile + KYC draft)
export const useAutosaveComprehensiveProfile = (options) =>
  useMutation({
    mutationFn: ({ userId, profileData, isDraft = true, preserveStatus = null }) => {
      // Determine status based on parameters
      let status;
      if (preserveStatus !== null) {
        // Use preserved status (for editing completed profiles)
        status = preserveStatus;
      } else {
        // Use draft logic (0 for draft, 1 for final submission)
        status = isDraft ? 0 : 1;
      }

      const payload = {
        ...profileData,
        status: status
      };

      console.log('Autosave API call:', {
        isDraft,
        preserveStatus,
        finalStatus: status,
        userId
      });

      return baseAPI.put(`${API_ENDPOINTS.COMPREHENSIVE_PROFILE}/${userId}`, payload);
    },
    ...options,
  });

// Mobile verification (for profile)
export const useMobileVerify = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.MOBILE_VERIFY, data),
    ...options,
  });

// Resend OTP
export const useResendOTP = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.MOBILE_REGISTER, {
        mobileNumber: data.mobileNumber,
        countryCode: data.countryCode || "IN",
        userType: data.userType,
        isResend: true,
      }),
    ...options,
  });

// Change password
export const useChangePassword = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.CHANGE_PASSWORD, {
        mobileNumber: data.mobileNumber,
        email: data.email,
        oldPassword: data.oldPassword,
        newPassword: data.newPassword,
        confirmPassword: data.confirmPassword,
        userType: data.userType,
        isAdmin: data.isAdmin || false,
      }),
    ...options,
  });

// Mobile number change - Register new mobile for OTP
export const useRegisterMobileChange = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.REGISTER_MOBILE_CHANGE, {
        mobileNumber: data.mobileNumber,
        userId: data.userId,
      }),
    ...options,
  });

// Mobile number change - Verify OTP or Password
export const useVerifyChangeOTP = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.VERIFY_CHANGE_OTP, {
        mobileNumber: data.mobileNumber,
        userId: data.userId,
        otp: data.otp,
        password: data.password,
      }),
    ...options,
  });

// Mobile number change - Final step to change mobile number
export const useChangeMobileNumber = (options) =>
  useMutation({
    mutationFn: (data) =>
      authAPI.post(API_ENDPOINTS.CHANGE_MOBILE_NUMBER, {
        mobileNumber: data.mobileNumber,
        currentUserMobileNumber: data.currentUserMobileNumber,
      }),
    ...options,
  });
