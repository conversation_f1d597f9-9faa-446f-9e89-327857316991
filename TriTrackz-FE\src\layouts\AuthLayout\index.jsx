import React, { useEffect, useRef } from "react";
import { Outlet, useLocation } from "react-router-dom";
import bannerVideo from "@assets/img/IstockBanner5.mp4";

const AuthLayout = () => {
  const heroRef = useRef(null);
  const formRef = useRef(null);
  const containerRef = useRef(null);
  const location = useLocation();

  // Check if current page is register
  const isRegisterPage = location.pathname === '/register';

  useEffect(() => {
    // Force dark theme for AuthLayout
    const root = document.documentElement;
    const originalTheme = root.getAttribute('data-theme');

    // Set dark theme
    root.setAttribute('data-theme', 'dark');
    root.setAttribute('data-bs-theme', 'dark');

    // Animate hero section
    if (heroRef.current) {
      setTimeout(() => {
        heroRef.current.style.opacity = "1";
        heroRef.current.style.transform = "translateX(0)";
      }, 300);
    }

    // Animate form section
    if (formRef.current) {
      setTimeout(() => {
        formRef.current.style.opacity = "1";
        formRef.current.style.transform = "translateX(0)";
      }, 600);
    }

    // Cleanup: restore original theme when component unmounts
    return () => {
      if (originalTheme) {
        root.setAttribute('data-theme', originalTheme);
        root.setAttribute('data-bs-theme', originalTheme);
      }
    };
  }, []);
  return (
    <div ref={containerRef} className={`min-vh-100 vh-100 d-flex flex-md-row flex-column position-relative overflow-hidden tritracz-container ${isRegisterPage ? 'register-page' : ''}`}>
      <video
        className="position-absolute top-0 start-0 w-100 h-100 tritracz-video-background"
        autoPlay
        muted
        loop
        playsInline
      >
        <source src={bannerVideo} type="video/mp4" />
        {/* Fallback for browsers that don't support video */}
        Your browser does not support the video tag.
      </video>

      <div ref={heroRef} className="hero-section">
        <div className="hero-content">
          <div className="auth-brand-container">
            <h1 className="auth-brand-title">
              Streamline Your Logistics Brokerage Operations
            </h1>
            <p className="auth-brand-subtitle">
              Efficiently manage carriers, shipments, contracts, and compliance all in one centralized platform designed for freight brokers.
            </p>
          </div>
        </div>
      </div>
      {/* Static Form Section - Content comes from individual pages */}
      <div ref={formRef} className="form-section">
        <div className="form-container">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;
