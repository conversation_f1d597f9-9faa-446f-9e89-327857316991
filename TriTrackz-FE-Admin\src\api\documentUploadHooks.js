import { useMutation, useQuery } from '@tanstack/react-query';
import { API_ENDPOINTS } from '@constants/apiNamevariables';
import { baseAPI } from './axiosInstance';

// Document types enum
export const DOCUMENT_TYPES = {
  GST_CERTIFICATE: 0,
  TRADE_LICENSE: 1,
  PAN_CARD: 2,
  BUSINESS_LICENSE: 3,
  AADHAR_CARD: 4,
  DRIVING_LICENSE: 5,
  PROFILE_PHOTO: 11
};

// Document upload hook
export const useDocumentUpload = (options) =>
  useMutation({
    mutationFn: async ({ file, documentType, userId }) => {
      const formData = new FormData();
      formData.append('File', file);
      formData.append('DocumentType', documentType);
      formData.append('UserId', userId);

      const response = await baseAPI.post(API_ENDPOINTS.UPLOAD_DOCUMENT, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });

      return response; // Return only the relevant data
    },
    ...options,
  });

// Utility function to validate file
export const validateFile = (file, maxSizeMB = 5) => {
  const errors = [];
  
  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  if (file.size > maxSizeBytes) {
    errors.push(`File size must be less than ${maxSizeMB}MB`);
  }
  
  // Check file type
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/jpg',
    'image/png'
  ];

  if (!allowedTypes.includes(file.type)) {
    errors.push('Only PDF, JPG, JPEG, and PNG files are allowed');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

// Utility function to get user ID from Redux persist storage
export const getUserIdFromStorage = () => {
  try {
    // Get from Redux persist storage
    const persistedData = localStorage.getItem('persist:tritrackz-admin-user');
    if (persistedData) {
      const parsedData = JSON.parse(persistedData);
      const userData = JSON.parse(parsedData.user || '{}');
      return userData.user?.id || userData.user?.userId || null;
    }
    return null;
  } catch (error) {
    console.error('Error getting user ID from storage:', error);
    return null;
  }
};

// Document download hook
export const useDocumentDownload = () => {
  return useMutation({
    mutationFn: async ({ documentId, documentType }) => {
      const response = await fetch(
        `${import.meta.env.VITE_APP_BASE_URL}/api/Documents/download/${documentId}?documentType=${documentType}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to download document');
      }

      // Get filename from response headers or use default
      const contentDisposition = response.headers.get('content-disposition');
      let filename = 'document';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      const blob = await response.blob();
      return { blob, filename };
    },
  });
};

// Document delete hook
export const useDocumentDelete = (options = {}) => {
  return useMutation({
    mutationFn: async ({ documentId, documentType }) => {
      const response = await fetch(
        `${import.meta.env.VITE_APP_BASE_URL}/api/Documents/${documentId}`,
        {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || 'Failed to delete document');
      }

      return await response.json();
    },
    ...options,
  });
};

// Hook to fetch user documents
export const useGetUserDocuments = (userId, options = {}) => {
  return useQuery({
    queryKey: ['userDocuments', userId],
    queryFn: async () => {
      const response = await fetch(
        `${import.meta.env.VITE_APP_BASE_URL}/api/Documents/get-file/${userId}`,
        {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Failed to fetch user documents');
      }

      return await response.json();
    },
    enabled: !!userId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    ...options,
  });
};

// Utility function to get required documents based on user type
export const getRequiredDocumentsByUserType = (userType) => {
  const baseDocuments = [
    { type: DOCUMENT_TYPES.PAN_CARD, name: 'PAN Card', key: 'PanCard' },
    { type: DOCUMENT_TYPES.AADHAR_CARD, name: 'Aadhaar Card', key: 'AadharCard' }
  ];

  // Add user type specific documents
  switch (userType) {
    case 2: // Transport Company
    case 4: // Carrier
    case 6: // Shipper Company
      return [
        ...baseDocuments,
        { type: DOCUMENT_TYPES.GST_CERTIFICATE, name: 'GST Certificate', key: 'GstCertificate' },
        { type: DOCUMENT_TYPES.TRADE_LICENSE, name: 'Trade License', key: 'TradeLicense' },
        { type: DOCUMENT_TYPES.BUSINESS_LICENSE, name: 'Business License', key: 'BusinessLicense' }
      ];

    case 5: // Driver
      return [
        ...baseDocuments,
        { type: DOCUMENT_TYPES.DRIVING_LICENSE, name: 'Driving License', key: 'DrivingLicense' }
      ];

    case 3: // Broker
    case 7: // Shipper Individual
    default:
      return baseDocuments;
  }
};

// Utility function to get document type name
export const getDocumentTypeName = (documentType) => {
  const typeNames = {
    [DOCUMENT_TYPES.GST_CERTIFICATE]: 'GST Certificate',
    [DOCUMENT_TYPES.TRADE_LICENSE]: 'Trade License',
    [DOCUMENT_TYPES.PAN_CARD]: 'PAN Card',
    [DOCUMENT_TYPES.BUSINESS_LICENSE]: 'Business License',
    [DOCUMENT_TYPES.AADHAR_CARD]: 'Aadhaar Card',
    [DOCUMENT_TYPES.DRIVING_LICENSE]: 'Driving License',
    [DOCUMENT_TYPES.PROFILE_PHOTO]: 'Profile Photo'
  };
  return typeNames[documentType] || 'Unknown Document';
};
