import React from "react";
import { useSelector } from "react-redux";
import { FaInfoCircle, FaClock, FaExclamationTriangle } from "react-icons/fa";

const KYCStatusAlert = ({
  className = "",
  style = {},
  profileData: propProfileData,
}) => {
  const { profileData: reduxProfileData } = useSelector((state) => state.user);

  // Use prop profileData if provided, otherwise use redux profileData
  const profileData = propProfileData || reduxProfileData;

  // Status enum: Incomplete = 0, Complete = 1, UnderReview = 2, Approved = 3, Rejected = 4
  // Don't show if no profile data or status is 3 (approved) or 0 (incomplete)
  if (!profileData || profileData.status === 3 || profileData.status === 0) {
    return null;
  }

  const getStatusConfig = () => {
    switch (profileData.status) {
      case 1: // Complete
        return {
          icon: <FaClock className="me-2" />,
          message:
            "KY<PERSON> completed and pending admin approval, you'll be notified once verified.",
          alertClass: "alert-warning",
          bgColor: "#fff3cd",
          borderColor: "#ffeaa7",
          textColor: "#856404",
        };
      case 2: // UnderReview
        return {
          icon: <FaInfoCircle className="me-2" />,
          message: "Your profile is under review by our team",
          alertClass: "alert-info",
          bgColor: "#d1ecf1",
          borderColor: "#74b9ff",
          textColor: "#0c5460",
        };
      case 4: // Rejected
        return {
          icon: <FaExclamationTriangle className="me-2" />,
          message:
            "KYC was rejected by the admin. Please update the required details and resubmit.",
          alertClass: "alert-danger",
          bgColor: "#f8d7da",
          borderColor: "#f5c6cb",
          textColor: "#721c24",
        };
      default:
        return null;
    }
  };

  const statusConfig = getStatusConfig();
  if (!statusConfig) return null;

  return (
    <div
      className={`alert ${statusConfig.alertClass} ${className}`}
      style={{
        backgroundColor: statusConfig.bgColor,
        borderColor: statusConfig.borderColor,
        color: statusConfig.textColor,
        border: "1px solid",
        borderRadius: "8px",
        margin: "0 0 20px 0",
        padding: "12px 16px",
        display: "flex",
        alignItems: "center",
        fontSize: "14px",
        fontWeight: "500",
        ...style,
      }}
    >
      {statusConfig.icon}
      <span>{statusConfig.message}</span>
    </div>
  );
};

export default KYCStatusAlert;
